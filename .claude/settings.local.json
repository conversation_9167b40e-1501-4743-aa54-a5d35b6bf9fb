{"permissions": {"allow": ["mcp__desktop-commander__start_process", "mcp__desktop-commander__search_code", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__read_file", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__write_file", "Bash(./start_langchain.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(brew install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(brew services start:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(curl:*)", "mcp__servers__create_entities", "mcp__servers__create_relations", "Bash(npm install:*)", "Bash(node:*)", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__interact_with_process", "Bash(npm run build:frontend:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run setup:dev-users:*)", "<PERSON><PERSON>(npx knex seed:run:*)", "Bash(npx knex migrate:*)", "Bash(lsof:*)", "Bash(NODE_ENV=development npm run dev)", "Bash(sqlite3:*)", "Bash(npm run typecheck:*)", "Bash(npm remove:*)", "Bash(npm cache clean:*)", "Bash(cp:*)", "Bash(npx claude-flow@alpha swarm init:*)", "Bash(npx:*)"], "deny": []}}