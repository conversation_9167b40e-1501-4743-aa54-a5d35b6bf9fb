# UI Design Team Coordination Prompt

## Team Assembly
@UI Design System Architect @TailwindCSS Glassmorphism Designer @Framer Motion Animation Specialist @Responsive Layout Specialist @Radix UI Accessibility Expert

## Mission: Modern AI Services Dashboard

You are working together to create a stunning, modern AI Services Dashboard that showcases our 8 AI services with beautiful visuals, smooth animations, and perfect responsiveness. This is a collaborative effort where each specialist contributes their expertise.

## Project Context

### Target File
- **Location**: `frontend/src/pages/Dashboard.tsx`
- **Framework**: React 18 + TypeScript
- **Styling**: TailwindCSS 3.3.5 with custom design system
- **Animation**: Framer Motion 10.16.4
- **Components**: Radix UI + Custom UI system

### Required Data (CRITICAL - Use Exact Values)
```typescript
const services = [
  { name: '<PERSON>elian', color: '#8B5CF6', requests: 15234, uptime: 99.9, description: 'Advanced AI analytics platform' },
  { name: 'ZeroEntropy', color: '#06B6D4', requests: 12847, uptime: 99.8, description: 'Quantum optimization engine' },
  { name: 'Hello.cv', color: '#10B981', requests: 8921, uptime: 99.5, description: 'Resume parsing and analysis' },
  { name: 'YoinkUI', color: '#F97316', requests: 6743, uptime: 99.9, description: 'UI component generator' },
  { name: 'Clueso', color: '#EF4444', requests: 4521, uptime: 98.5, description: 'Intelligent search' },
  { name: 'Permut', color: '#8B5CF6', requests: 3892, uptime: 99.6, description: 'Permutation engine' },
  { name: 'Intervo', color: '#14B8A6', requests: 5678, uptime: 99.8, description: 'Voice AI assistant' },
  { name: 'Pixelesq', color: '#EC4899', requests: 7234, uptime: 99.7, description: 'Image processing' }
];
```

## Team Coordination Protocol

### Phase 1: Architecture & Planning
**@UI Design System Architect** - Lead this phase
1. Define the overall component structure and TypeScript interfaces
2. Establish the authentication integration pattern
3. Create the base component architecture
4. Define prop interfaces and component composition
5. Coordinate with other specialists on requirements

### Phase 2: Visual Foundation
**@TailwindCSS Glassmorphism Designer** - Lead this phase
1. Implement the aurora background with animated gradient blobs
2. Create glassmorphic service cards with proper transparency and blur
3. Design the gradient text effects for the header
4. Implement the color system integration for service-specific colors
5. Ensure dark mode compatibility

### Phase 3: Animation & Interactions
**@Framer Motion Animation Specialist** - Lead this phase
1. Add floating animations to service cards with staggered delays
2. Implement smooth number counting animations for request metrics
3. Create hover effects and micro-interactions
4. Add page transition animations
5. Ensure performance optimization and reduced motion support

### Phase 4: Responsive Layout
**@Responsive Layout Specialist** - Lead this phase
1. Implement the Bento grid layout that adapts across screen sizes
2. Ensure proper card sizing and spacing on all devices
3. Optimize touch interactions for mobile
4. Test and refine breakpoint behavior
5. Validate cross-device compatibility

### Phase 5: Accessibility Integration
**@Radix UI Accessibility Expert** - Review and enhance
1. Ensure proper ARIA attributes and semantic structure
2. Validate keyboard navigation and screen reader support
3. Check color contrast compliance
4. Add proper focus management
5. Test with assistive technologies

## Technical Requirements

### Must Include
1. **Authentication Check**: 
   ```typescript
   import useAuthStore from '@/stores/authStore';
   import { useNavigate } from 'react-router-dom';
   
   const { user } = useAuthStore();
   const navigate = useNavigate();
   
   useEffect(() => {
     if (!user) {
       navigate('/login');
     }
   }, [user, navigate]);
   ```

2. **Animated Request Counters**: Smooth counting animation from 0 to target value

3. **Circular Progress Indicators**: SVG-based progress rings for uptime display

4. **Responsive Bento Grid**: 
   - Mobile: 1 column
   - Tablet: 2-3 columns  
   - Desktop: 6 columns with varying card sizes
   - First card: 2x2 (featured)
   - Cards 2-4: 2x1 (medium)
   - Cards 5-8: 1x1 (small)

5. **Aurora Background**: Animated gradient blobs with pulse effects

6. **Glass Effects**: Backdrop blur with semi-transparent backgrounds

### Performance Standards
- 60fps animations on all devices
- Smooth scrolling and interactions
- Optimized for mobile performance
- Proper loading states
- Accessibility compliance

### Code Quality Standards
- TypeScript strict mode compliance
- Proper component composition
- Reusable animation variants
- Responsive utility classes
- Clean, documented code

## Collaboration Guidelines

### Communication Protocol
1. **Lead Specialist** announces their phase and requirements
2. **Supporting Specialists** provide input and dependencies
3. **All Specialists** review and approve the integrated solution
4. **Final Review** by all team members before implementation

### Integration Points
- **Design System**: Use established color palette and spacing
- **Animation Timing**: Coordinate animation delays and durations
- **Responsive Breakpoints**: Align on breakpoint strategy
- **Accessibility**: Ensure all interactions are accessible
- **Performance**: Optimize for smooth experience

### Quality Gates
1. **Visual Quality**: Pixel-perfect implementation
2. **Animation Smoothness**: 60fps performance
3. **Responsive Behavior**: Works on all screen sizes
4. **Accessibility**: WCAG 2.1 AA compliance
5. **Code Quality**: Clean, maintainable TypeScript

## Expected Deliverable

A complete `Dashboard.tsx` component that:
- ✅ Displays all 8 services with correct data
- ✅ Features stunning glassmorphic design
- ✅ Includes smooth floating animations
- ✅ Works perfectly on all devices
- ✅ Maintains accessibility standards
- ✅ Integrates with existing auth system
- ✅ Follows project coding standards

## Success Criteria

### Visual Excellence
- Beautiful aurora background with animated elements
- Glassmorphic cards with proper depth and transparency
- Smooth, purposeful animations that enhance UX
- Consistent visual hierarchy and spacing

### Technical Excellence
- Clean, type-safe TypeScript code
- Optimal performance across devices
- Proper error handling and loading states
- Accessibility compliance

### User Experience
- Intuitive and responsive interactions
- Fast loading and smooth animations
- Clear information hierarchy
- Delightful micro-interactions

## Coordination Commands

When working together, use these coordination patterns:

**@UI Design System Architect**: "I'm establishing the base structure with these interfaces..."
**@TailwindCSS Glassmorphism Designer**: "Building on that structure, I'll add these glass effects..."
**@Framer Motion Animation Specialist**: "I'll enhance those elements with these animations..."
**@Responsive Layout Specialist**: "I'll ensure this works across devices with these adaptations..."
**@Radix UI Accessibility Expert**: "I'll review and enhance accessibility with these improvements..."

## Final Integration

The final component should seamlessly blend all specialties:
- Architectural foundation from Design System Architect
- Visual beauty from Glassmorphism Designer  
- Smooth animations from Motion Specialist
- Perfect responsiveness from Layout Specialist
- Full accessibility from Accessibility Expert

Work together to create something exceptional that showcases the power of coordinated expertise!
