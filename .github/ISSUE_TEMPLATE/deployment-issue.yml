name: 🚀 Deployment Issue
description: Report issues with CI/CD pipeline or deployments
title: "[DEPLOY] "
labels: ["deployment", "ci/cd"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        ## Deployment Issue Report
        
        Please provide detailed information about the deployment issue you're experiencing.

  - type: dropdown
    id: deployment-stage
    attributes:
      label: Deployment Stage
      description: At which stage did the issue occur?
      options:
        - CI Pipeline (Testing/Building)
        - Security Scanning
        - Infrastructure Provisioning
        - Database Migration
        - Application Deployment
        - Post-deployment Verification
        - Monitoring/Alerting
        - Other
    validations:
      required: true

  - type: dropdown
    id: environment
    attributes:
      label: Environment
      description: Which environment was affected?
      options:
        - Development
        - Staging
        - Production
        - All Environments
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: Severity
      description: How severe is this issue?
      options:
        - Critical (Production down)
        - High (Major functionality affected)
        - Medium (Some functionality affected)
        - Low (Minor issue)
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Issue Description
      description: Provide a clear and detailed description of the issue
      placeholder: Describe what happened, what you expected to happen, and any error messages...
    validations:
      required: true

  - type: input
    id: workflow-run
    attributes:
      label: GitHub Actions Workflow Run
      description: Link to the failed workflow run (if applicable)
      placeholder: https://github.com/username/repo/actions/runs/123456789

  - type: input
    id: git-commit
    attributes:
      label: Git Commit SHA
      description: The commit SHA that was being deployed
      placeholder: abc123def456...

  - type: textarea
    id: error-logs
    attributes:
      label: Error Logs
      description: Paste relevant error logs or output
      render: shell
      placeholder: |
        Error: terraform apply failed
        │ Error: creating EKS Cluster (ss-platform-staging): InvalidParameter: 1 validation error detected...

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: How can this issue be reproduced?
      placeholder: |
        1. Run workflow X
        2. Deploy to environment Y
        3. Error occurs at step Z

  - type: textarea
    id: current-workaround
    attributes:
      label: Current Workaround
      description: Any temporary workarounds or solutions you've implemented
      placeholder: Describe any workarounds currently in place...

  - type: checkboxes
    id: affected-services
    attributes:
      label: Affected Services
      description: Which services are affected by this deployment issue?
      options:
        - label: Backend API
        - label: Frontend Application
        - label: Database
        - label: Redis Cache
        - label: Container Images
        - label: Kubernetes Cluster
        - label: Load Balancer/Ingress
        - label: Monitoring/Logging
        - label: Security Scanning
        - label: External Integrations

  - type: checkboxes
    id: deployment-checklist
    attributes:
      label: Deployment Troubleshooting Checklist
      description: Please check items that have been verified
      options:
        - label: Checked GitHub Actions logs
        - label: Verified secrets and environment variables
        - label: Confirmed infrastructure state
        - label: Checked database connectivity
        - label: Verified container image builds
        - label: Reviewed monitoring/alerting
        - label: Consulted deployment documentation

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other information that might be helpful
      placeholder: |
        - Recent changes to infrastructure
        - Related issues or PRs
        - External dependencies
        - Timeline of events

  - type: markdown
    attributes:
      value: |
        ## Emergency Contacts
        
        **For Critical Production Issues:**
        - 🚨 Use the `#alerts` Slack channel
        - 📞 Contact the on-call engineer
        - 🔄 Consider running emergency rollback procedures
        
        **Documentation:**
        - [Deployment Guide](docs/deployment/)
        - [Troubleshooting Guide](docs/troubleshooting.md)
        - [Emergency Procedures](docs/emergency-procedures.md)