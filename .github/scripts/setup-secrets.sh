#!/bin/bash

# GitHub Actions CI/CD Secrets Setup Script
# This script helps set up all required secrets for the CI/CD pipeline

set -e

echo "🔐 GitHub Actions CI/CD Secrets Setup"
echo "====================================="
echo ""
echo "This script will help you set up all required secrets for the CI/CD pipeline."
echo "Make sure you have the GitHub CLI (gh) installed and authenticated."
echo ""

# Check if gh CLI is available
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed."
    echo "Please install it from: https://cli.github.com/"
    exit 1
fi

# Check if authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI."
    echo "Please run: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI is installed and authenticated."
echo ""

# Get repository information
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner)
echo "📁 Repository: $REPO"
echo ""

# Function to set secret
set_secret() {
    local name=$1
    local description=$2
    local example=$3
    
    echo "🔑 Setting up: $name"
    echo "   Description: $description"
    if [ -n "$example" ]; then
        echo "   Example: $example"
    fi
    
    read -s -p "   Enter value: " value
    echo ""
    
    if [ -n "$value" ]; then
        gh secret set "$name" --body "$value"
        echo "   ✅ Secret '$name' set successfully"
    else
        echo "   ⚠️ Skipping empty secret '$name'"
    fi
    echo ""
}

# AWS Secrets
echo "🔧 AWS Configuration Secrets"
echo "=============================="
set_secret "AWS_ACCESS_KEY_ID" "AWS Access Key for infrastructure deployment" "AKIA..."
set_secret "AWS_SECRET_ACCESS_KEY" "AWS Secret Access Key" "abc123..."
set_secret "TERRAFORM_STATE_BUCKET" "S3 bucket for Terraform state" "my-terraform-state-bucket"
set_secret "TERRAFORM_LOCK_TABLE" "DynamoDB table for Terraform locking" "my-terraform-locks"

echo "🗄️ Database Configuration Secrets"
echo "=================================="
set_secret "STAGING_DATABASE_URL" "Staging database connection string" "postgresql://user:pass@host:port/db"
set_secret "PRODUCTION_DATABASE_URL" "Production database connection string" "postgresql://user:pass@host:port/db"

echo "🔐 Application Secrets"
echo "======================"
set_secret "STAGING_JWT_SECRET" "JWT secret for staging environment" "your-jwt-secret-key"
set_secret "PRODUCTION_JWT_SECRET" "JWT secret for production environment" "your-jwt-secret-key"
set_secret "STAGING_SESSION_SECRET" "Session secret for staging" "your-session-secret"
set_secret "PRODUCTION_SESSION_SECRET" "Session secret for production" "your-session-secret"

echo "☸️ Kubernetes Configuration"
echo "==========================="
echo "For Kubernetes secrets, you need to provide base64-encoded kubeconfig data."
echo "You can generate this by running:"
echo "  cat ~/.kube/config | base64 -w 0"
echo ""
set_secret "KUBE_CONFIG_DATA" "Base64-encoded kubeconfig for Kubernetes access" "LS0tLS1CRUdJTi..."

echo "🔔 Notification Secrets"
echo "======================="
set_secret "SLACK_WEBHOOK" "Slack webhook URL for general notifications" "https://hooks.slack.com/services/..."
set_secret "SECURITY_SLACK_WEBHOOK" "Slack webhook for security alerts" "https://hooks.slack.com/services/..."
set_secret "PERFORMANCE_SLACK_WEBHOOK" "Slack webhook for performance alerts" "https://hooks.slack.com/services/..."
set_secret "DATABASE_SLACK_WEBHOOK" "Slack webhook for database migration notifications" "https://hooks.slack.com/services/..."
set_secret "INFRASTRUCTURE_SLACK_WEBHOOK" "Slack webhook for infrastructure deployment notifications" "https://hooks.slack.com/services/..."

echo "🔒 Security Tool Secrets"
echo "========================"
set_secret "SNYK_TOKEN" "Snyk authentication token for vulnerability scanning" "your-snyk-token"
set_secret "SEMGREP_APP_TOKEN" "Semgrep App token for SAST scanning" "your-semgrep-token"
set_secret "NVD_API_KEY" "NIST NVD API key for vulnerability database access" "your-nvd-api-key"

echo "🚨 Maintenance Mode Secrets"
echo "==========================="
set_secret "MAINTENANCE_MODE_URL" "URL for maintenance mode API" "https://your-app.com/api/maintenance"
set_secret "MAINTENANCE_API_KEY" "API key for maintenance mode control" "your-maintenance-api-key"

echo "📊 Environment Variables (not secrets)"
echo "======================================"
echo ""
echo "The following should be set as environment variables (not secrets):"
echo ""
echo "Repository Variables:"
echo "- AWS_REGION: us-east-1 (or your preferred region)"
echo "- NODE_VERSION: 18.x"
echo "- TERRAFORM_VERSION: 1.6.0"
echo "- KUBECTL_VERSION: 1.28.0"
echo "- HELM_VERSION: 3.12.0"
echo ""
echo "You can set these using:"
echo "  gh variable set AWS_REGION --body 'us-east-1'"
echo ""

# Ask if user wants to set variables
read -p "Would you like to set the repository variables now? (y/N): " set_vars
if [[ $set_vars =~ ^[Yy]$ ]]; then
    echo ""
    echo "🔧 Setting repository variables..."
    
    gh variable set AWS_REGION --body 'us-east-1'
    gh variable set NODE_VERSION --body '18.x'
    gh variable set TERRAFORM_VERSION --body '1.6.0'
    gh variable set KUBECTL_VERSION --body '1.28.0'
    gh variable set HELM_VERSION --body '3.12.0'
    
    echo "✅ Repository variables set successfully"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "All secrets have been configured for your CI/CD pipeline."
echo ""
echo "Next steps:"
echo "1. Review your secrets in GitHub: https://github.com/$REPO/settings/secrets/actions"
echo "2. Ensure your AWS credentials have the necessary permissions"
echo "3. Set up your Terraform backend (S3 bucket and DynamoDB table)"
echo "4. Configure your Kubernetes cluster access"
echo "5. Test the pipeline with a small change"
echo ""
echo "For more information, see the documentation in docs/deployment/"
echo ""
echo "Happy deploying! 🚀"