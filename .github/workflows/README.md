# GitHub Actions CI/CD Workflows

This directory contains a comprehensive set of GitHub Actions workflows for the AI Services Platform, providing automated testing, security scanning, deployment, and monitoring capabilities.

## 🚀 Quick Start

### Prerequisites

1. **GitHub Secrets**: Set up required secrets using the provided script:
   ```bash
   chmod +x .github/scripts/setup-secrets.sh
   ./.github/scripts/setup-secrets.sh
   ```

2. **AWS Infrastructure**: Ensure AWS account is configured with:
   - EKS cluster for Kubernetes deployments
   - RDS PostgreSQL instances for databases
   - ElastiCache Redis instances
   - S3 bucket for Terraform state
   - DynamoDB table for Terraform locking

3. **Repository Settings**: Configure the following repository settings:
   - Enable "Allow GitHub Actions to create and approve pull requests"
   - Set up environment protection rules for `production` environment
   - Configure branch protection rules for `main` and `develop`

## 📋 Workflow Overview

| Workflow | Trigger | Purpose | Duration |
|----------|---------|---------|----------|
| **CI Pipeline** | PR, Push to develop | Code quality, testing, building | ~15-20 min |
| **Production Deploy** | Push to main, Manual | Production deployment with approvals | ~25-30 min |
| **Security Scan** | Schedule, Manual | Comprehensive security testing | ~20-25 min |
| **Performance Monitor** | Schedule, Push | Load testing and performance analysis | ~10-15 min |
| **Database Migration** | Migration changes, Manual | Database schema management | ~5-10 min |
| **Infrastructure Deploy** | Infrastructure changes, Manual | AWS/K8s infrastructure management | ~20-25 min |

## 🔄 Workflow Details

### 1. CI Pipeline (`ci.yml`)

**Primary workflow for code validation and testing**

```yaml
Triggers:
  - pull_request: [main, develop]
  - push: [develop]
  - workflow_dispatch
```

**Key Features:**
- 🎯 **Smart Change Detection**: Only runs relevant jobs based on changed files
- 🧪 **Multi-Environment Testing**: Separate backend, frontend, and integration tests
- 🐳 **Container Building**: Multi-platform Docker image builds with caching
- 🔒 **Security Scanning**: Trivy vulnerability scans
- ⚡ **Performance Testing**: Basic load testing with Artillery
- 🏗️ **Infrastructure Validation**: Terraform and Kubernetes manifest validation

**Jobs Flow:**
```mermaid
graph LR
    A[Detect Changes] --> B[Code Quality]
    A --> C[Backend Tests]
    A --> D[Frontend Tests]
    B --> E[Integration Tests]
    C --> E
    D --> E
    E --> F[Build Images]
    F --> G[Security Scan]
    G --> H[Performance Test]
```

### 2. Production Deployment (`production-deploy.yml`)

**Zero-downtime production deployment with approval gates**

```yaml
Triggers:
  - push: [main]
  - workflow_dispatch: [staging, production]
```

**Deployment Strategies:**
- 🔵 **Blue-Green Deployment**: Zero-downtime deployments (default)
- 🕊️ **Canary Deployment**: Progressive rollout (configurable)

**Safety Features:**
- ✅ Pre-deployment validation
- 🗄️ Automatic database backups
- 🔄 Automatic rollback on failure
- 🧪 Post-deployment verification
- 📊 Real-time monitoring

### 3. Security Scanning (`security-scan.yml`)

**Comprehensive security testing suite**

```yaml
Triggers:
  - schedule: "0 2 * * *"  # Daily at 2 AM
  - push: [main, develop]
  - workflow_dispatch
```

**Security Scans:**
- 🔍 **SAST**: Static Application Security Testing (CodeQL, Semgrep)
- 📦 **Dependency Scan**: npm audit, Snyk, OWASP Dependency Check
- 🐳 **Container Scan**: Trivy, Grype, Dockle
- 🏗️ **Infrastructure Scan**: Checkov, tfsec, Kubesec
- 🔐 **Secrets Scan**: GitLeaks, TruffleHog
- 🌐 **DAST**: Dynamic Application Security Testing (OWASP ZAP)
- 📋 **Compliance**: CIS benchmarks, security standards

### 4. Performance Monitoring (`performance-monitoring.yml`)

**Continuous performance testing and monitoring**

```yaml
Triggers:
  - schedule: "0 */6 * * *"  # Every 6 hours
  - push: [main]
  - pull_request: [main]
```

**Performance Tests:**
- 🚀 **Load Testing**: Artillery-based API load testing
- 🌐 **Browser Performance**: Playwright performance metrics
- 💡 **Lighthouse Audit**: Web performance scoring
- 📊 **Performance Analysis**: Trend analysis and alerting

**Test Intensities:**
- **Light**: 5 RPS, 10 users
- **Medium**: 20 RPS, 50 users  
- **Heavy**: 50 RPS, 100 users
- **Stress**: 100 RPS, 200 users

### 5. Database Migration (`database-migration.yml`)

**Safe database schema management**

```yaml
Triggers:
  - push: [main, develop] (migration file changes)
  - workflow_dispatch: [staging, production]
```

**Migration Features:**
- ✅ **Validation**: Migration file structure and syntax
- 🧪 **Testing**: Fresh database and rollback testing
- 📦 **Staging First**: Automatic staging deployment
- 🔒 **Production Approval**: Approval-gated production deployment
- 💾 **Backup**: Automatic database backups before migration
- 🚨 **Emergency Rollback**: Automatic rollback on failure

### 6. Infrastructure Deployment (`infrastructure-deploy.yml`)

**Infrastructure as Code management**

```yaml
Triggers:
  - push: [main] (infrastructure changes)
  - pull_request: [main] (infrastructure changes)
  - workflow_dispatch
```

**Infrastructure Management:**
- 🏗️ **Terraform**: AWS infrastructure provisioning
- ☸️ **Kubernetes**: Application deployment to EKS
- 🎛️ **Helm**: Package management and templating
- 📊 **Monitoring**: Prometheus and Grafana setup
- 🔄 **Rollback**: Emergency infrastructure rollback

## 🌍 Environment Strategy

### Development
- **Purpose**: Feature development and initial testing
- **Deployment**: Automatic on feature branches
- **Resources**: Minimal (1 replica, 256Mi memory)
- **Data**: Synthetic test data

### Staging
- **Purpose**: Pre-production testing and QA
- **Deployment**: Automatic on `develop` branch
- **Resources**: Production-like but scaled (2 replicas, 512Mi memory)
- **Data**: Sanitized production snapshots

### Production
- **Purpose**: Live customer-facing application
- **Deployment**: Approval-gated from `main` branch
- **Resources**: Full production capacity (5+ replicas, 2Gi memory)
- **Data**: Live customer data with compliance

## 🔐 Security & Compliance

### Security Features
- 🛡️ **Container Security**: Multi-stage builds, non-root users, read-only filesystems
- 🔐 **Secrets Management**: GitHub Secrets with rotation
- 🌐 **Network Security**: Private subnets, network policies
- 🔍 **Vulnerability Scanning**: Continuous security monitoring
- 📋 **Compliance**: SOC2, GDPR compliance validation

### Security Scans Schedule
- **Daily**: Full security scan suite
- **On Push**: Basic vulnerability scans
- **Weekly**: DAST scans (if enabled)
- **Monthly**: Compliance audits

## 📊 Monitoring & Alerting

### Monitoring Stack
- **Metrics**: Prometheus + Grafana
- **Logs**: ELK Stack (if configured)
- **Traces**: Jaeger (if configured)
- **Uptime**: Synthetic monitoring

### Alert Channels
- 📱 **Slack**: Real-time notifications
- 📧 **Email**: Critical alerts
- 🚨 **PagerDuty**: On-call escalation
- 🎫 **GitHub Issues**: Automatic issue creation

### Key Metrics
- **Performance**: Response time, throughput, error rate
- **Infrastructure**: CPU, memory, disk usage
- **Security**: Vulnerability count, failed auth attempts
- **Business**: API usage, user activity, feature adoption

## 🛠️ Configuration Files

### Workflow Configuration
- **`.github/workflows/*.yml`**: GitHub Actions workflow definitions
- **`.github/scripts/`**: Helper scripts for setup and automation
- **`.github/ISSUE_TEMPLATE/`**: Issue templates for deployment issues

### Application Configuration
- **`infrastructure/helm/values-*.yaml`**: Helm value files per environment
- **`infrastructure/kubernetes/`**: Kubernetes manifests
- **`infrastructure/terraform/`**: Terraform configuration files
- **`.zap/rules.tsv`**: OWASP ZAP scanning rules

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check workflow logs
gh run view <run-id> --log

# Verify dependencies
npm audit
npm outdated
```

#### Deployment Issues
```bash
# Check Kubernetes status
kubectl get pods -n ss-platform-production
kubectl describe deployment backend -n ss-platform-production

# Check application logs
kubectl logs deployment/backend -n ss-platform-production
```

#### Database Migration Issues
```bash
# Check migration status
cd backend && npm run db:migrate:status

# Manual rollback (if needed)
npm run db:migrate:rollback
```

### Debug Commands

```bash
# List recent workflow runs
gh run list --limit 10

# View specific workflow run
gh run view <run-id>

# Re-run failed jobs
gh run rerun <run-id>

# Check deployment status
kubectl get deployments --all-namespaces
```

## 📖 Best Practices

### Development Workflow
1. **Feature Development**: Work in feature branches
2. **Pull Requests**: All changes via PR with review
3. **Testing**: Ensure all tests pass before merge
4. **Documentation**: Update docs with changes

### Deployment Best Practices
1. **Staging First**: Always deploy to staging first
2. **Gradual Rollout**: Use canary deployments for risky changes
3. **Monitoring**: Watch metrics after deployment
4. **Rollback Plan**: Have rollback plan for every deployment

### Security Best Practices
1. **Secrets Rotation**: Rotate secrets regularly
2. **Least Privilege**: Minimal required permissions
3. **Security Scans**: Address high/critical vulnerabilities promptly
4. **Compliance**: Regular compliance audits

## 🔄 Maintenance

### Regular Tasks
- **Weekly**: Review security scan results
- **Monthly**: Update base images and dependencies
- **Quarterly**: Review and update monitoring
- **Bi-annually**: Disaster recovery testing

### Upgrades
- **Node.js**: Plan version upgrades quarterly
- **Kubernetes**: Follow AWS EKS upgrade schedule
- **Dependencies**: Regular security updates

## 📞 Support

### Getting Help
1. **Documentation**: Check this README and [CI/CD Pipeline Documentation](../../docs/deployment/CI-CD-PIPELINE.md)
2. **Issues**: Create deployment issue using GitHub template
3. **Slack**: Use appropriate alert channels
4. **Emergency**: Contact on-call engineer for production issues

### Emergency Procedures
- **Production Down**: Use emergency rollback procedures
- **Security Incident**: Follow security incident response plan
- **Data Loss**: Activate disaster recovery procedures

## 🔗 Additional Resources

- [Detailed CI/CD Pipeline Documentation](../../docs/deployment/CI-CD-PIPELINE.md)
- [Security Guidelines](../../docs/security/)
- [Infrastructure Documentation](../../docs/infrastructure/)
- [Monitoring Setup](../../docs/monitoring/)
- [Emergency Procedures](../../docs/emergency-procedures.md)

---

**Need Help?** 
- 📚 [View Documentation](../../docs/)
- 🎫 [Create Issue](../../issues/new/choose)
- 💬 [Slack #devops](https://yourteam.slack.com/channels/devops)