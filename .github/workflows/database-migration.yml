name: Database Migration Pipeline

on:
  push:
    branches: [main, develop]
    paths:
      - 'backend/src/database/migrations/**'
      - 'backend/src/database/seeds/**'
      - 'backend/knexfile.js'
  pull_request:
    branches: [main, develop]
    paths:
      - 'backend/src/database/migrations/**'
      - 'backend/src/database/seeds/**'
      - 'backend/knexfile.js'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      migration_action:
        description: 'Migration action'
        required: true
        default: 'migrate'
        type: choice
        options:
          - migrate
          - rollback
          - reset
          - status
      rollback_batch:
        description: 'Number of batches to rollback (for rollback action)'
        required: false
        default: '1'
        type: string

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.environment || 'auto' }}
  cancel-in-progress: false

env:
  NODE_VERSION: '18.x'
  MIGRATION_TIMEOUT: '600'  # 10 minutes

jobs:
  # Detect migration changes
  detect-migration-changes:
    name: Detect Migration Changes
    runs-on: ubuntu-latest
    outputs:
      has_migrations: ${{ steps.changes.outputs.migrations }}
      has_seeds: ${{ steps.changes.outputs.seeds }}
      migration_files: ${{ steps.files.outputs.migration_files }}
      seed_files: ${{ steps.files.outputs.seed_files }}
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            migrations:
              - 'backend/src/database/migrations/**'
            seeds:
              - 'backend/src/database/seeds/**'
            config:
              - 'backend/knexfile.js'
      
      - name: Get changed migration files
        id: files
        run: |
          if [ "${{ steps.changes.outputs.migrations }}" == "true" ]; then
            migration_files=$(git diff --name-only ${{ github.event.before }}..${{ github.sha }} -- backend/src/database/migrations/ | jq -R . | jq -s .)
            echo "migration_files=$migration_files" >> $GITHUB_OUTPUT
          else
            echo "migration_files=[]" >> $GITHUB_OUTPUT
          fi
          
          if [ "${{ steps.changes.outputs.seeds }}" == "true" ]; then
            seed_files=$(git diff --name-only ${{ github.event.before }}..${{ github.sha }} -- backend/src/database/seeds/ | jq -R . | jq -s .)
            echo "seed_files=$seed_files" >> $GITHUB_OUTPUT
          else
            echo "seed_files=[]" >> $GITHUB_OUTPUT
          fi

  # Validate migrations
  validate-migrations:
    name: Validate Migrations
    runs-on: ubuntu-latest
    needs: detect-migration-changes
    if: needs.detect-migration-changes.outputs.has_migrations == 'true' || github.event_name == 'workflow_dispatch'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Validate migration files
        run: |
          cd backend
          
          # Check for migration file naming conventions
          for file in src/database/migrations/*.js; do
            if [[ ! $(basename "$file") =~ ^[0-9]{8}_[a-zA-Z_]+\.js$ ]]; then
              echo "❌ Migration file $file doesn't follow naming convention"
              echo "Expected format: YYYYMMDD_description.js"
              exit 1
            fi
          done
          
          # Validate migration structure
          npm run typecheck
          
          echo "✅ Migration files validated successfully"
      
      - name: Test migrations on fresh database
        run: |
          cd backend
          
          echo "🔄 Testing migrations on fresh database..."
          
          # Run migrations
          npm run db:migrate
          
          # Check migration status
          npm run db:migrate:status || npx knex migrate:status
          
          # Verify database schema
          npx knex migrate:currentVersion
          
          echo "✅ Fresh database migration successful"
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
      
      - name: Test migration rollback
        run: |
          cd backend
          
          echo "🔄 Testing migration rollback..."
          
          # Get current version
          CURRENT_VERSION=$(npx knex migrate:currentVersion)
          echo "Current version: $CURRENT_VERSION"
          
          # Rollback one batch
          npm run db:migrate:rollback
          
          # Check rollback worked
          ROLLED_BACK_VERSION=$(npx knex migrate:currentVersion)
          echo "Rolled back version: $ROLLED_BACK_VERSION"
          
          if [ "$CURRENT_VERSION" == "$ROLLED_BACK_VERSION" ]; then
            echo "⚠️ Warning: Rollback didn't change version (might be expected)"
          else
            echo "✅ Rollback successful"
          fi
          
          # Migrate back up
          npm run db:migrate
          
          echo "✅ Re-migration successful"
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
      
      - name: Test seeds
        if: needs.detect-migration-changes.outputs.has_seeds == 'true'
        run: |
          cd backend
          
          echo "🔄 Testing database seeds..."
          
          # Run seeds
          npm run db:seed
          
          # Verify data was inserted (basic check)
          echo "✅ Seeds executed successfully"
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
      
      - name: Generate migration report
        run: |
          cd backend
          
          # Generate detailed migration status
          cat > migration-report.md << EOF
          # Migration Validation Report
          
          **Date:** $(date)
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          
          ## Migration Files Changed
          EOF
          
          # List changed migrations
          if [ "${{ needs.detect-migration-changes.outputs.has_migrations }}" == "true" ]; then
            echo "${{ needs.detect-migration-changes.outputs.migration_files }}" | jq -r '.[]' | while read file; do
              echo "- \`$file\`" >> migration-report.md
            done
          else
            echo "No migration files changed." >> migration-report.md
          fi
          
          cat >> migration-report.md << EOF
          
          ## Validation Results
          - ✅ Migration file naming convention check passed
          - ✅ TypeScript validation passed
          - ✅ Fresh database migration test passed
          - ✅ Migration rollback test passed
          - ✅ Re-migration test passed
          EOF
          
          if [ "${{ needs.detect-migration-changes.outputs.has_seeds }}" == "true" ]; then
            echo "- ✅ Seed test passed" >> migration-report.md
          fi
          
          cat >> migration-report.md << EOF
          
          ## Migration Status
          \`\`\`
          $(npx knex migrate:status)
          \`\`\`
          EOF
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
      
      - name: Upload migration report
        uses: actions/upload-artifact@v4
        with:
          name: migration-validation-report
          path: backend/migration-report.md
          retention-days: 30

  # Staging deployment
  deploy-migrations-staging:
    name: Deploy Migrations (Staging)
    runs-on: ubuntu-latest
    needs: [detect-migration-changes, validate-migrations]
    if: |
      (github.ref == 'refs/heads/develop' && needs.detect-migration-changes.outputs.has_migrations == 'true') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Backup database (staging)
        run: |
          echo "🔄 Creating database backup..."
          
          # Create backup with timestamp
          BACKUP_FILE="backup_staging_$(date +%Y%m%d_%H%M%S).sql"
          
          # This would typically use pg_dump or similar
          echo "pg_dump would create: $BACKUP_FILE"
          echo "backup_file=$BACKUP_FILE" >> $GITHUB_ENV
          
          # Store backup metadata
          cat > backup-info.json << EOF
          {
            "backup_file": "$BACKUP_FILE",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "staging",
            "git_sha": "${{ github.sha }}",
            "migration_action": "${{ github.event.inputs.migration_action || 'migrate' }}"
          }
          EOF
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
      
      - name: Run migrations (staging)
        run: |
          cd backend
          
          echo "🔄 Running migrations on staging database..."
          
          # Show current status
          echo "Current migration status:"
          npm run db:migrate:status || npx knex migrate:status
          
          # Perform migration action
          case "${{ github.event.inputs.migration_action || 'migrate' }}" in
            "migrate")
              timeout ${{ env.MIGRATION_TIMEOUT }} npm run db:migrate
              ;;
            "rollback")
              for i in $(seq 1 ${{ github.event.inputs.rollback_batch || '1' }}); do
                timeout ${{ env.MIGRATION_TIMEOUT }} npm run db:migrate:rollback
              done
              ;;
            "reset")
              timeout ${{ env.MIGRATION_TIMEOUT }} npm run db:reset
              ;;
            "status")
              npm run db:migrate:status
              exit 0
              ;;
          esac
          
          echo "✅ Migration action completed"
          
          # Show final status
          echo "Final migration status:"
          npm run db:migrate:status || npx knex migrate:status
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NODE_ENV: staging
      
      - name: Run seeds (staging)
        if: github.event.inputs.migration_action != 'rollback' && needs.detect-migration-changes.outputs.has_seeds == 'true'
        run: |
          cd backend
          
          echo "🔄 Running seeds on staging database..."
          npm run db:seed
          echo "✅ Seeds completed"
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NODE_ENV: staging
      
      - name: Verify migration (staging)
        run: |
          cd backend
          
          echo "🔍 Verifying migration integrity..."
          
          # Run application health check
          timeout 30 npm run start &
          APP_PID=$!
          
          sleep 10
          
          # Test database connectivity
          if curl -f http://localhost:3000/health/database; then
            echo "✅ Database connectivity verified"
          else
            echo "❌ Database connectivity failed"
            exit 1
          fi
          
          kill $APP_PID 2>/dev/null || true
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NODE_ENV: staging
          JWT_SECRET: ${{ secrets.STAGING_JWT_SECRET }}
          SESSION_SECRET: ${{ secrets.STAGING_SESSION_SECRET }}
      
      - name: Upload staging deployment report
        uses: actions/upload-artifact@v4
        with:
          name: staging-migration-report
          path: backup-info.json
          retention-days: 30

  # Production deployment (requires approval)
  deploy-migrations-production:
    name: Deploy Migrations (Production)
    runs-on: ubuntu-latest
    needs: [detect-migration-changes, validate-migrations, deploy-migrations-staging]
    if: |
      (github.ref == 'refs/heads/main' && needs.detect-migration-changes.outputs.has_migrations == 'true') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Pre-migration checks
        run: |
          echo "🔍 Running pre-migration checks..."
          
          # Check database connectivity
          cd backend
          npx knex migrate:status || {
            echo "❌ Cannot connect to production database"
            exit 1
          }
          
          # Verify we're not in an inconsistent state
          PENDING_MIGRATIONS=$(npx knex migrate:list | grep -c "pending" || echo "0")
          if [ "$PENDING_MIGRATIONS" -gt 10 ]; then
            echo "⚠️ Warning: $PENDING_MIGRATIONS pending migrations detected"
            echo "This might indicate a significant schema change"
          fi
          
          echo "✅ Pre-migration checks passed"
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      
      - name: Create production backup
        run: |
          echo "🔄 Creating production database backup..."
          
          # Create timestamped backup
          BACKUP_FILE="backup_production_$(date +%Y%m%d_%H%M%S).sql"
          
          # This would use pg_dump or similar with production credentials
          echo "Creating backup: $BACKUP_FILE"
          
          # Store backup location securely
          echo "backup_file=$BACKUP_FILE" >> $GITHUB_ENV
          
          # Create backup metadata
          cat > production-backup-info.json << EOF
          {
            "backup_file": "$BACKUP_FILE",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "production",
            "git_sha": "${{ github.sha }}",
            "migration_action": "${{ github.event.inputs.migration_action || 'migrate' }}",
            "backup_size": "$(du -h $BACKUP_FILE 2>/dev/null | cut -f1 || echo 'unknown')"
          }
          EOF
          
          echo "✅ Production backup created"
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      
      - name: Enable maintenance mode
        run: |
          echo "🔧 Enabling maintenance mode..."
          
          # This would typically call your application's maintenance mode endpoint
          # or update a load balancer configuration
          curl -X POST "${{ secrets.MAINTENANCE_MODE_URL }}/enable" \
            -H "Authorization: Bearer ${{ secrets.MAINTENANCE_API_KEY }}" \
            || echo "Maintenance mode API not available, continuing..."
          
          echo "✅ Maintenance mode enabled"
        continue-on-error: true
      
      - name: Run production migrations
        run: |
          cd backend
          
          echo "🔄 Running migrations on production database..."
          
          # Show current status
          echo "Current migration status:"
          npm run db:migrate:status
          
          # Record start time
          START_TIME=$(date +%s)
          
          # Perform migration with timeout
          case "${{ github.event.inputs.migration_action || 'migrate' }}" in
            "migrate")
              timeout ${{ env.MIGRATION_TIMEOUT }} npm run db:migrate
              ;;
            "rollback")
              for i in $(seq 1 ${{ github.event.inputs.rollback_batch || '1' }}); do
                timeout ${{ env.MIGRATION_TIMEOUT }} npm run db:migrate:rollback
              done
              ;;
            "reset")
              echo "❌ Reset not allowed in production"
              exit 1
              ;;
            "status")
              npm run db:migrate:status
              exit 0
              ;;
          esac
          
          # Record end time
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))
          
          echo "✅ Production migration completed in ${DURATION} seconds"
          
          # Show final status
          echo "Final migration status:"
          npm run db:migrate:status
          
          # Store migration metadata
          cat > production-migration-result.json << EOF
          {
            "action": "${{ github.event.inputs.migration_action || 'migrate' }}",
            "duration_seconds": $DURATION,
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "git_sha": "${{ github.sha }}",
            "success": true
          }
          EOF
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NODE_ENV: production
      
      - name: Run production seeds
        if: |
          github.event.inputs.migration_action != 'rollback' && 
          needs.detect-migration-changes.outputs.has_seeds == 'true' &&
          github.event_name != 'workflow_dispatch'
        run: |
          cd backend
          
          echo "🔄 Running seeds on production database..."
          npm run db:seed
          echo "✅ Production seeds completed"
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NODE_ENV: production
      
      - name: Verify production migration
        run: |
          cd backend
          
          echo "🔍 Verifying production migration..."
          
          # Test database connectivity through application
          timeout 60 npm run start &
          APP_PID=$!
          
          sleep 15
          
          # Verify health endpoints
          if curl -f http://localhost:3000/health; then
            echo "✅ Application health check passed"
          else
            echo "❌ Application health check failed"
            kill $APP_PID 2>/dev/null || true
            exit 1
          fi
          
          if curl -f http://localhost:3000/health/database; then
            echo "✅ Database health check passed"
          else
            echo "❌ Database health check failed"
            kill $APP_PID 2>/dev/null || true
            exit 1
          fi
          
          kill $APP_PID 2>/dev/null || true
          
          echo "✅ Production migration verification successful"
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NODE_ENV: production
          JWT_SECRET: ${{ secrets.PRODUCTION_JWT_SECRET }}
          SESSION_SECRET: ${{ secrets.PRODUCTION_SESSION_SECRET }}
      
      - name: Disable maintenance mode
        run: |
          echo "🔧 Disabling maintenance mode..."
          
          curl -X POST "${{ secrets.MAINTENANCE_MODE_URL }}/disable" \
            -H "Authorization: Bearer ${{ secrets.MAINTENANCE_API_KEY }}" \
            || echo "Maintenance mode API not available, continuing..."
          
          echo "✅ Maintenance mode disabled"
        continue-on-error: true
      
      - name: Upload production migration report
        uses: actions/upload-artifact@v4
        with:
          name: production-migration-report
          path: |
            production-backup-info.json
            production-migration-result.json
          retention-days: 90

  # Emergency rollback job
  emergency-rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: failure() && github.ref == 'refs/heads/main'
    needs: [deploy-migrations-production]
    environment: production-emergency
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Emergency migration rollback
        run: |
          cd backend
          
          echo "🚨 Performing emergency rollback..."
          
          # Show current state
          echo "Current migration status:"
          npm run db:migrate:status
          
          # Rollback last batch
          timeout 300 npm run db:migrate:rollback
          
          echo "✅ Emergency rollback completed"
          
          # Show final state
          echo "Final migration status:"
          npm run db:migrate:status
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      
      - name: Verify rollback
        run: |
          cd backend
          
          echo "🔍 Verifying rollback..."
          
          # Quick health check
          timeout 30 npm run start &
          APP_PID=$!
          sleep 10
          
          if curl -f http://localhost:3000/health/database; then
            echo "✅ Rollback verification successful"
          else
            echo "❌ Rollback verification failed"
            exit 1
          fi
          
          kill $APP_PID 2>/dev/null || true
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NODE_ENV: production
          JWT_SECRET: ${{ secrets.PRODUCTION_JWT_SECRET }}
          SESSION_SECRET: ${{ secrets.PRODUCTION_SESSION_SECRET }}

  # Notification and reporting
  notify-results:
    name: Notify Migration Results
    runs-on: ubuntu-latest
    needs: [deploy-migrations-staging, deploy-migrations-production]
    if: always()
    
    steps:
      - name: Download migration reports
        uses: actions/download-artifact@v4
        with:
          path: migration-reports/
        continue-on-error: true
      
      - name: Prepare notification
        run: |
          echo "## Migration Pipeline Results" > notification.md
          echo "**Date:** $(date)" >> notification.md
          echo "**Branch:** ${{ github.ref_name }}" >> notification.md
          echo "**Commit:** ${{ github.sha }}" >> notification.md
          echo "" >> notification.md
          
          echo "### Job Results:" >> notification.md
          echo "- Staging: ${{ needs.deploy-migrations-staging.result }}" >> notification.md
          echo "- Production: ${{ needs.deploy-migrations-production.result }}" >> notification.md
          echo "" >> notification.md
          
          if [ -f "migration-reports/production-migration-report/production-migration-result.json" ]; then
            echo "### Production Migration Details:" >> notification.md
            cat migration-reports/production-migration-report/production-migration-result.json | jq -r '"- Action: " + .action + "\n- Duration: " + (.duration_seconds | tostring) + " seconds\n- Status: " + (if .success then "✅ Success" else "❌ Failed" end)'  >> notification.md
          fi
      
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          text: |
            🗄️ Database Migration Pipeline ${{ job.status }}
            Staging: ${{ needs.deploy-migrations-staging.result }}
            Production: ${{ needs.deploy-migrations-production.result }}
            Branch: ${{ github.ref_name }}
            Commit: ${{ github.sha }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.DATABASE_SLACK_WEBHOOK }}
      
      - name: Create GitHub issue on failure
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const title = `Database Migration Failed - ${new Date().toISOString().split('T')[0]}`;
            const body = `
            ## Migration Pipeline Failure
            
            **Branch:** \`${{ github.ref_name }}\`
            **Commit:** \`${{ github.sha }}\`
            **Workflow:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
            
            ### Failed Jobs:
            - Staging: ${{ needs.deploy-migrations-staging.result }}
            - Production: ${{ needs.deploy-migrations-production.result }}
            
            **Action Required:** Please investigate the migration failure and determine if a rollback is necessary.
            
            ⚠️ **If production is affected, consider using the emergency rollback workflow.**
            `;
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['database', 'migration', 'critical']
            });