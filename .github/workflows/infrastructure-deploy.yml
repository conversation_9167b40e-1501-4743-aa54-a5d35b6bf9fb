name: Infrastructure Deployment

on:
  push:
    branches: [main]
    paths:
      - 'infrastructure/**'
  pull_request:
    branches: [main]
    paths:
      - 'infrastructure/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      action:
        description: 'Terraform action'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      force_replace:
        description: 'Force replace resources (dangerous)'
        required: false
        default: false
        type: boolean

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.environment || 'auto' }}
  cancel-in-progress: false

env:
  TERRAFORM_VERSION: '1.6.0'
  KUBECTL_VERSION: '1.28.0'
  HELM_VERSION: '3.12.0'
  AWS_REGION: 'us-east-1'

jobs:
  # Plan infrastructure changes
  terraform-plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    outputs:
      plan_summary: ${{ steps.plan.outputs.summary }}
      has_changes: ${{ steps.plan.outputs.has_changes }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Init
        working-directory: infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.TERRAFORM_STATE_BUCKET }}" \
            -backend-config="key=${{ github.event.inputs.environment || 'staging' }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}" \
            -backend-config="dynamodb_table=${{ secrets.TERRAFORM_LOCK_TABLE }}"
      
      - name: Terraform Validate
        working-directory: infrastructure/terraform
        run: terraform validate
      
      - name: Terraform Format Check
        working-directory: infrastructure/terraform
        run: terraform fmt -check -recursive
      
      - name: Terraform Plan
        id: plan
        working-directory: infrastructure/terraform
        run: |
          set -e
          
          # Determine environment
          ENV="${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'staging') }}"
          
          # Set variables based on environment
          TF_VAR_FILE="terraform.${ENV}.tfvars"
          
          # Create plan
          terraform plan \
            -var="environment=${ENV}" \
            -var-file="${TF_VAR_FILE}" \
            -out="terraform-${ENV}.plan" \
            -detailed-exitcode \
            -no-color | tee terraform-plan-output.txt
          
          PLAN_EXIT_CODE=${PIPESTATUS[0]}
          
          # Parse plan results
          if [ $PLAN_EXIT_CODE -eq 0 ]; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
            SUMMARY="✅ No infrastructure changes detected"
          elif [ $PLAN_EXIT_CODE -eq 2 ]; then
            echo "has_changes=true" >> $GITHUB_OUTPUT
            
            # Extract resource changes
            ADD_COUNT=$(grep -c "will be created" terraform-plan-output.txt || echo "0")
            CHANGE_COUNT=$(grep -c "will be updated in-place" terraform-plan-output.txt || echo "0")
            DESTROY_COUNT=$(grep -c "will be destroyed" terraform-plan-output.txt || echo "0")
            REPLACE_COUNT=$(grep -c "must be replaced" terraform-plan-output.txt || echo "0")
            
            SUMMARY="📋 Infrastructure changes detected:\n"
            SUMMARY+="- **Add:** ${ADD_COUNT} resources\n"
            SUMMARY+="- **Change:** ${CHANGE_COUNT} resources\n"
            SUMMARY+="- **Destroy:** ${DESTROY_COUNT} resources\n"
            SUMMARY+="- **Replace:** ${REPLACE_COUNT} resources"
          else
            echo "❌ Terraform plan failed with exit code $PLAN_EXIT_CODE"
            exit $PLAN_EXIT_CODE
          fi
          
          echo "summary=${SUMMARY}" >> $GITHUB_OUTPUT
          echo "environment=${ENV}" >> $GITHUB_ENV
      
      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v4
        with:
          name: terraform-plan-${{ env.environment }}
          path: |
            infrastructure/terraform/terraform-*.plan
            infrastructure/terraform/terraform-plan-output.txt
          retention-days: 30
      
      - name: Comment PR with plan results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const planOutput = fs.readFileSync('infrastructure/terraform/terraform-plan-output.txt', 'utf8');
            
            const comment = `
            ## Terraform Plan Results 🏗️
            
            ${{ steps.plan.outputs.summary }}
            
            <details>
            <summary>View Full Plan</summary>
            
            \`\`\`terraform
            ${planOutput.substring(0, 60000)}${planOutput.length > 60000 ? '\n... (truncated)' : ''}
            \`\`\`
            
            </details>
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Apply infrastructure changes
  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: terraform-plan
    if: |
      (github.ref == 'refs/heads/main' && needs.terraform-plan.outputs.has_changes == 'true') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'apply')
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'staging') }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
          terraform_wrapper: false
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Download Terraform Plan
        uses: actions/download-artifact@v4
        with:
          name: terraform-plan-${{ github.event.inputs.environment || 'staging' }}
          path: infrastructure/terraform/
      
      - name: Terraform Init
        working-directory: infrastructure/terraform
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.TERRAFORM_STATE_BUCKET }}" \
            -backend-config="key=${{ github.event.inputs.environment || 'staging' }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}" \
            -backend-config="dynamodb_table=${{ secrets.TERRAFORM_LOCK_TABLE }}"
      
      - name: Pre-apply validation
        working-directory: infrastructure/terraform
        run: |
          echo "🔍 Running pre-apply validation..."
          
          # Check if plan file exists and is valid
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          PLAN_FILE="terraform-${ENV}.plan"
          
          if [ ! -f "$PLAN_FILE" ]; then
            echo "❌ Plan file $PLAN_FILE not found"
            exit 1
          fi
          
          # Show plan summary
          terraform show -no-color "$PLAN_FILE"
          
          echo "✅ Pre-apply validation passed"
      
      - name: Terraform Apply
        working-directory: infrastructure/terraform
        run: |
          set -e
          
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          PLAN_FILE="terraform-${ENV}.plan"
          
          echo "🚀 Applying Terraform plan for $ENV environment..."
          
          # Apply with timeout
          timeout 1800 terraform apply \
            -auto-approve \
            -no-color \
            "$PLAN_FILE" | tee terraform-apply-output.txt
          
          echo "✅ Terraform apply completed successfully"
          
          # Capture outputs
          terraform output -json > terraform-outputs.json
      
      - name: Upload Terraform Apply Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: terraform-apply-${{ github.event.inputs.environment || 'staging' }}
          path: |
            infrastructure/terraform/terraform-apply-output.txt
            infrastructure/terraform/terraform-outputs.json
          retention-days: 90

  # Deploy Kubernetes configurations
  deploy-kubernetes:
    name: Deploy Kubernetes
    runs-on: ubuntu-latest
    needs: [terraform-plan, terraform-apply]
    if: |
      always() && 
      (needs.terraform-apply.result == 'success' || 
       (github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'apply'))
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'staging') }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: ${{ env.HELM_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Download Terraform outputs
        uses: actions/download-artifact@v4
        with:
          name: terraform-apply-${{ github.event.inputs.environment || 'staging' }}
          path: terraform-outputs/
        continue-on-error: true
      
      - name: Configure kubectl
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          CLUSTER_NAME="ss-platform-${ENV}"
          
          # Update kubeconfig
          aws eks update-kubeconfig \
            --region ${{ env.AWS_REGION }} \
            --name "$CLUSTER_NAME"
          
          # Test connection
          kubectl cluster-info
          kubectl get nodes
      
      - name: Deploy Kubernetes resources
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="ss-platform-${ENV}"
          
          echo "🚀 Deploying Kubernetes resources to $ENV environment..."
          
          # Create namespace if it doesn't exist
          kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
          
          # Apply ConfigMaps and Secrets first
          if [ -d "infrastructure/kubernetes/config/" ]; then
            kubectl apply -f infrastructure/kubernetes/config/ --namespace="$NAMESPACE"
          fi
          
          # Apply main Kubernetes manifests
          find infrastructure/kubernetes -name '*.yaml' -not -path '*/config/*' -not -path '*/helm/*' | \
          while read manifest; do
            echo "Applying $manifest..."
            envsubst < "$manifest" | kubectl apply -f - --namespace="$NAMESPACE"
          done
          
          # Wait for deployments to be ready
          echo "⏳ Waiting for deployments to be ready..."
          kubectl wait --for=condition=available --timeout=600s \
            deployment --all --namespace="$NAMESPACE"
          
          echo "✅ Kubernetes deployment completed"
        env:
          ENVIRONMENT: ${{ github.event.inputs.environment || 'staging' }}
          IMAGE_TAG: ${{ github.sha }}
      
      - name: Deploy Helm charts
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="ss-platform-${ENV}"
          
          if [ -d "infrastructure/helm" ]; then
            echo "🚀 Deploying Helm charts..."
            
            # Add common Helm repositories
            helm repo add stable https://charts.helm.sh/stable
            helm repo add bitnami https://charts.bitnami.com/bitnami
            helm repo update
            
            # Deploy each Helm chart
            find infrastructure/helm -name 'Chart.yaml' -exec dirname {} \; | \
            while read chart_dir; do
              chart_name=$(basename "$chart_dir")
              
              echo "Deploying Helm chart: $chart_name"
              
              helm upgrade --install "$chart_name" "$chart_dir" \
                --namespace="$NAMESPACE" \
                --create-namespace \
                --wait \
                --timeout=600s \
                --values="$chart_dir/values-${ENV}.yaml"
            done
            
            echo "✅ Helm deployment completed"
          else
            echo "ℹ️ No Helm charts found, skipping..."
          fi
      
      - name: Verify deployment
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="ss-platform-${ENV}"
          
          echo "🔍 Verifying deployment..."
          
          # Check all pods are running
          kubectl get pods --namespace="$NAMESPACE"
          
          # Check services
          kubectl get services --namespace="$NAMESPACE"
          
          # Check ingresses
          kubectl get ingresses --namespace="$NAMESPACE"
          
          # Run connectivity tests
          echo "Running connectivity tests..."
          kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl \
            --namespace="$NAMESPACE" \
            -- curl -f http://backend-service:3000/health || echo "Backend health check failed"
          
          echo "✅ Deployment verification completed"

  # Infrastructure monitoring setup
  setup-monitoring:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: [terraform-apply, deploy-kubernetes]
    if: |
      always() && 
      needs.deploy-kubernetes.result == 'success'
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'staging') }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: ${{ env.HELM_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Configure kubectl
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          CLUSTER_NAME="ss-platform-${ENV}"
          
          aws eks update-kubeconfig \
            --region ${{ env.AWS_REGION }} \
            --name "$CLUSTER_NAME"
      
      - name: Deploy Prometheus
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="monitoring-${ENV}"
          
          echo "🚀 Deploying Prometheus monitoring stack..."
          
          # Create monitoring namespace
          kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
          
          # Add Prometheus Helm repository
          helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
          helm repo update
          
          # Install Prometheus stack
          helm upgrade --install prometheus-stack prometheus-community/kube-prometheus-stack \
            --namespace="$NAMESPACE" \
            --create-namespace \
            --wait \
            --timeout=600s \
            --values="infrastructure/monitoring/prometheus/values-${ENV}.yaml"
          
          echo "✅ Prometheus deployment completed"
      
      - name: Deploy Grafana dashboards
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="monitoring-${ENV}"
          
          echo "🚀 Deploying Grafana dashboards..."
          
          # Apply custom dashboards
          if [ -d "infrastructure/monitoring/grafana/dashboards/" ]; then
            kubectl create configmap grafana-dashboards \
              --from-file=infrastructure/monitoring/grafana/dashboards/ \
              --namespace="$NAMESPACE" \
              --dry-run=client -o yaml | kubectl apply -f -
          fi
          
          echo "✅ Grafana dashboards deployed"
      
      - name: Configure alerts
        run: |
          ENV="${{ github.event.inputs.environment || 'staging' }}"
          NAMESPACE="monitoring-${ENV}"
          
          echo "🚀 Configuring alerts..."
          
          # Apply alert rules
          if [ -f "infrastructure/monitoring/alerts/alerts.yml" ]; then
            kubectl apply -f infrastructure/monitoring/alerts/alerts.yml \
              --namespace="$NAMESPACE"
          fi
          
          echo "✅ Alerts configured"

  # Rollback capability
  rollback-infrastructure:
    name: Rollback Infrastructure
    runs-on: ubuntu-latest
    if: failure() && github.ref == 'refs/heads/main'
    needs: [terraform-apply, deploy-kubernetes]
    environment: production-emergency
    
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.before }}  # Checkout previous version
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Emergency rollback
        working-directory: infrastructure/terraform
        run: |
          echo "🚨 Performing emergency infrastructure rollback..."
          
          terraform init \
            -backend-config="bucket=${{ secrets.TERRAFORM_STATE_BUCKET }}" \
            -backend-config="key=production/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}" \
            -backend-config="dynamodb_table=${{ secrets.TERRAFORM_LOCK_TABLE }}"
          
          # Plan rollback
          terraform plan \
            -var="environment=production" \
            -var-file="terraform.production.tfvars" \
            -out="terraform-rollback.plan"
          
          # Apply rollback
          terraform apply -auto-approve terraform-rollback.plan
          
          echo "✅ Emergency rollback completed"

  # Notification and reporting
  notify-deployment-results:
    name: Notify Deployment Results
    runs-on: ubuntu-latest
    needs: [terraform-plan, terraform-apply, deploy-kubernetes, setup-monitoring]
    if: always()
    
    steps:
      - name: Prepare notification
        run: |
          ENV="${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'staging') }}"
          
          cat > notification.md << EOF
          ## Infrastructure Deployment Results 🏗️
          
          **Environment:** $ENV
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}
          **Triggered by:** ${{ github.event_name }}
          
          ### Job Results:
          - **Terraform Plan:** ${{ needs.terraform-plan.result }}
          - **Terraform Apply:** ${{ needs.terraform-apply.result }}
          - **Kubernetes Deploy:** ${{ needs.deploy-kubernetes.result }}
          - **Monitoring Setup:** ${{ needs.setup-monitoring.result }}
          
          ### Changes Summary:
          ${{ needs.terraform-plan.outputs.plan_summary }}
          EOF
      
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          text: |
            🏗️ Infrastructure Deployment ${{ job.status }}
            Environment: ${{ github.event.inputs.environment || 'auto' }}
            Terraform: ${{ needs.terraform-apply.result }}
            Kubernetes: ${{ needs.deploy-kubernetes.result }}
            Branch: ${{ github.ref_name }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.INFRASTRUCTURE_SLACK_WEBHOOK }}
      
      - name: Create deployment record
        uses: actions/github-script@v7
        if: needs.terraform-apply.result == 'success'
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: '${{ github.event.inputs.environment || "staging" }}',
              description: 'Infrastructure deployment via GitHub Actions',
              auto_merge: false
            });
            
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.data.id,
              state: '${{ needs.deploy-kubernetes.result == "success" ? "success" : "failure" }}',
              description: 'Infrastructure deployment completed'
            });