name: Performance Testing & Monitoring

on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types: [opened, synchronize]
  workflow_dispatch:
    inputs:
      test_duration:
        description: 'Test duration (minutes)'
        required: false
        default: '10'
        type: string
      test_intensity:
        description: 'Test intensity'
        required: false
        default: 'medium'
        type: choice
        options:
          - light
          - medium
          - heavy
          - stress

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '18.x'
  TEST_DURATION: ${{ github.event.inputs.test_duration || '10' }}
  TEST_INTENSITY: ${{ github.event.inputs.test_intensity || 'medium' }}

jobs:
  # Setup and prepare test environment
  setup-environment:
    name: Setup Test Environment
    runs-on: ubuntu-latest
    outputs:
      test_url: ${{ steps.setup.outputs.test_url }}
      test_config: ${{ steps.config.outputs.config }}
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build applications
        run: npm run build
      
      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
      
      - name: Start applications
        id: setup
        run: |
          # Start backend
          npm run start --workspace=backend &
          BACKEND_PID=$!
          echo "backend_pid=$BACKEND_PID" >> $GITHUB_ENV
          
          # Wait for backend to be ready
          timeout 60 bash -c 'until curl -f http://localhost:3000/health; do sleep 2; done'
          
          # Start frontend
          npm run preview --workspace=frontend &
          FRONTEND_PID=$!
          echo "frontend_pid=$FRONTEND_PID" >> $GITHUB_ENV
          
          # Wait for frontend to be ready
          timeout 60 bash -c 'until curl -f http://localhost:3001; do sleep 2; done'
          
          echo "test_url=http://localhost:3001" >> $GITHUB_OUTPUT
        env:
          NODE_ENV: production
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret
          SESSION_SECRET: test_session_secret
      
      - name: Generate test configuration
        id: config
        run: |
          cat > test-config.json << EOF
          {
            "baseURL": "http://localhost:3001",
            "apiURL": "http://localhost:3000",
            "duration": "${{ env.TEST_DURATION }}m",
            "intensity": "${{ env.TEST_INTENSITY }}",
            "scenarios": {
              "light": { "rps": 5, "users": 10 },
              "medium": { "rps": 20, "users": 50 },
              "heavy": { "rps": 50, "users": 100 },
              "stress": { "rps": 100, "users": 200 }
            }
          }
          EOF
          
          echo "config=$(cat test-config.json | jq -c .)" >> $GITHUB_OUTPUT
      
      - name: Upload test configuration
        uses: actions/upload-artifact@v4
        with:
          name: test-config
          path: test-config.json
          retention-days: 1

  # Load testing with Artillery
  load-testing:
    name: Load Testing
    runs-on: ubuntu-latest
    needs: setup-environment
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Start test environment
        run: |
          npm run build
          npm run db:migrate
          npm run start --workspace=backend &
          sleep 30
          npm run preview --workspace=frontend &
          sleep 10
        env:
          NODE_ENV: production
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret
          SESSION_SECRET: test_session_secret
      
      - name: Create Artillery configuration
        run: |
          config='${{ needs.setup-environment.outputs.test_config }}'
          intensity=$(echo $config | jq -r '.intensity')
          duration=$(echo $config | jq -r '.duration')
          rps=$(echo $config | jq -r ".scenarios.$intensity.rps")
          users=$(echo $config | jq -r ".scenarios.$intensity.users")
          
          cat > artillery-config.yml << EOF
          config:
            target: 'http://localhost:3001'
            phases:
              - duration: ${duration}
                arrivalRate: ${rps}
                maxVusers: ${users}
            plugins:
              metrics-by-endpoint: {}
              publish-metrics:
                - type: statsd
                  host: localhost
                  port: 8125
                  prefix: artillery
            processor: "./load-test-processors.js"
          
          scenarios:
            - name: "Homepage Load Test"
              weight: 30
              flow:
                - get:
                    url: "/"
                - think: 2
            
            - name: "API Health Check"
              weight: 20
              flow:
                - get:
                    url: "/api/health"
                - think: 1
            
            - name: "User Authentication Flow"
              weight: 25
              flow:
                - post:
                    url: "/api/auth/register"
                    json:
                      email: "test{{ \$randomString() }}@example.com"
                      password: "TestPassword123!"
                      name: "Test User"
                - think: 2
                - post:
                    url: "/api/auth/login"
                    json:
                      email: "{{ email }}"
                      password: "TestPassword123!"
                    capture:
                      - json: "\$.token"
                        as: "authToken"
                - think: 1
            
            - name: "Service Usage Flow"
              weight: 25
              flow:
                - post:
                    url: "/api/auth/login"
                    json:
                      email: "<EMAIL>"
                      password: "DemoPassword123!"
                    capture:
                      - json: "\$.token"
                        as: "authToken"
                - get:
                    url: "/api/services"
                    headers:
                      Authorization: "Bearer {{ authToken }}"
                - think: 3
                - get:
                    url: "/api/analytics/usage"
                    headers:
                      Authorization: "Bearer {{ authToken }}"
                - think: 2
          EOF
      
      - name: Run Artillery load test
        run: |
          npx artillery run artillery-config.yml \
            --output artillery-report.json \
            --config artillery-config.yml
      
      - name: Generate Artillery HTML report
        run: |
          npx artillery report artillery-report.json \
            --output artillery-report.html
      
      - name: Upload Artillery results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: load-test-results
          path: |
            artillery-report.json
            artillery-report.html
            artillery-config.yml
          retention-days: 30

  # Browser performance testing with Playwright
  browser-performance:
    name: Browser Performance Testing
    runs-on: ubuntu-latest
    needs: setup-environment
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps chromium
      
      - name: Start test environment
        run: |
          npm run build
          npm run db:migrate
          npm run start --workspace=backend &
          sleep 30
          npm run preview --workspace=frontend &
          sleep 10
        env:
          NODE_ENV: production
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret
          SESSION_SECRET: test_session_secret
      
      - name: Create performance test script
        run: |
          cat > performance-test.js << 'EOF'
          const { chromium } = require('playwright');
          const fs = require('fs');
          
          async function runPerformanceTests() {
            const browser = await chromium.launch();
            const context = await browser.newContext();
            const page = await context.newPage();
            
            // Enable performance metrics collection
            await context.tracing.start({ screenshots: true, snapshots: true });
            
            const results = {
              timestamp: new Date().toISOString(),
              tests: []
            };
            
            // Test 1: Homepage load performance
            console.log('Testing homepage performance...');
            const homepageStart = Date.now();
            await page.goto('http://localhost:3001', { waitUntil: 'networkidle' });
            
            const homepageMetrics = await page.evaluate(() => {
              const navigation = performance.getEntriesByType('navigation')[0];
              return {
                loadTime: navigation.loadEventEnd - navigation.loadEventStart,
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
                firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
                totalTime: Date.now() - performance.timeOrigin
              };
            });
            
            results.tests.push({
              name: 'Homepage Load',
              metrics: homepageMetrics,
              duration: Date.now() - homepageStart
            });
            
            // Test 2: Dashboard load performance (after login)
            console.log('Testing dashboard performance...');
            await page.goto('http://localhost:3001/login');
            await page.fill('[name="email"]', '<EMAIL>');
            await page.fill('[name="password"]', 'DemoPassword123!');
            
            const dashboardStart = Date.now();
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            
            const dashboardMetrics = await page.evaluate(() => {
              const navigation = performance.getEntriesByType('navigation')[0];
              return {
                loadTime: navigation.loadEventEnd - navigation.loadEventStart,
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                resourceCount: performance.getEntriesByType('resource').length
              };
            });
            
            results.tests.push({
              name: 'Dashboard Load',
              metrics: dashboardMetrics,
              duration: Date.now() - dashboardStart
            });
            
            // Test 3: Services page performance
            console.log('Testing services page performance...');
            const servicesStart = Date.now();
            await page.goto('http://localhost:3001/services');
            await page.waitForLoadState('networkidle');
            
            const servicesMetrics = await page.evaluate(() => {
              const navigation = performance.getEntriesByType('navigation')[0];
              return {
                loadTime: navigation.loadEventEnd - navigation.loadEventStart,
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                resourceCount: performance.getEntriesByType('resource').length
              };
            });
            
            results.tests.push({
              name: 'Services Page Load',
              metrics: servicesMetrics,
              duration: Date.now() - servicesStart
            });
            
            // Stop tracing and save
            await context.tracing.stop({ path: 'performance-trace.zip' });
            
            // Save results
            fs.writeFileSync('performance-results.json', JSON.stringify(results, null, 2));
            
            await browser.close();
            return results;
          }
          
          runPerformanceTests().catch(console.error);
          EOF
      
      - name: Run browser performance tests
        run: node performance-test.js
      
      - name: Generate performance report
        run: |
          cat > performance-report.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
            <title>Performance Test Report</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .metric { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
              .good { background-color: #d4edda; }
              .warning { background-color: #fff3cd; }
              .danger { background-color: #f8d7da; }
              table { width: 100%; border-collapse: collapse; }
              th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
            </style>
          </head>
          <body>
            <h1>Browser Performance Test Report</h1>
            <div id="results"></div>
            
            <script>
              const results = EOF
              cat performance-results.json >> performance-report.html
              cat >> performance-report.html << 'EOF'
              ;
              
              const container = document.getElementById('results');
              container.innerHTML = `
                <p><strong>Test Run:</strong> ${results.timestamp}</p>
                <table>
                  <tr>
                    <th>Test</th>
                    <th>Load Time (ms)</th>
                    <th>DOM Content Loaded (ms)</th>
                    <th>First Paint (ms)</th>
                    <th>Duration (ms)</th>
                    <th>Status</th>
                  </tr>
                  ${results.tests.map(test => {
                    const loadTime = test.metrics.loadTime || 0;
                    const status = loadTime < 2000 ? 'good' : loadTime < 5000 ? 'warning' : 'danger';
                    return `
                      <tr class="${status}">
                        <td>${test.name}</td>
                        <td>${loadTime.toFixed(2)}</td>
                        <td>${(test.metrics.domContentLoaded || 0).toFixed(2)}</td>
                        <td>${(test.metrics.firstPaint || 0).toFixed(2)}</td>
                        <td>${test.duration}</td>
                        <td>${status}</td>
                      </tr>
                    `;
                  }).join('')}
                </table>
              `;
            </script>
          </body>
          </html>
          EOF
      
      - name: Upload browser performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: browser-performance-results
          path: |
            performance-results.json
            performance-report.html
            performance-trace.zip
          retention-days: 30

  # Lighthouse performance audit
  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: setup-environment
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci
          npm install -g lighthouse
      
      - name: Start test environment
        run: |
          npm run build
          npm run db:migrate
          npm run start --workspace=backend &
          sleep 30
          npm run preview --workspace=frontend &
          sleep 10
        env:
          NODE_ENV: production
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret
          SESSION_SECRET: test_session_secret
      
      - name: Run Lighthouse audit
        run: |
          # Run Lighthouse on different pages
          lighthouse http://localhost:3001 \
            --output=html \
            --output=json \
            --output-path=lighthouse-homepage \
            --chrome-flags="--headless --no-sandbox --disable-gpu"
          
          lighthouse http://localhost:3001/services \
            --output=html \
            --output=json \
            --output-path=lighthouse-services \
            --chrome-flags="--headless --no-sandbox --disable-gpu"
      
      - name: Generate Lighthouse summary
        run: |
          # Create a summary from Lighthouse JSON results
          cat > lighthouse-summary.js << 'EOF'
          const fs = require('fs');
          
          function generateSummary(filename, pageName) {
            try {
              const data = JSON.parse(fs.readFileSync(filename + '.report.json', 'utf8'));
              const categories = data.lhr.categories;
              
              return {
                page: pageName,
                performance: Math.round(categories.performance.score * 100),
                accessibility: Math.round(categories.accessibility.score * 100),
                bestPractices: Math.round(categories['best-practices'].score * 100),
                seo: Math.round(categories.seo.score * 100),
                metrics: {
                  fcp: data.lhr.audits['first-contentful-paint'].displayValue,
                  lcp: data.lhr.audits['largest-contentful-paint'].displayValue,
                  cls: data.lhr.audits['cumulative-layout-shift'].displayValue,
                  tbt: data.lhr.audits['total-blocking-time'].displayValue
                }
              };
            } catch (error) {
              console.error('Error processing', filename, error);
              return null;
            }
          }
          
          const results = [
            generateSummary('lighthouse-homepage', 'Homepage'),
            generateSummary('lighthouse-services', 'Services Page')
          ].filter(Boolean);
          
          fs.writeFileSync('lighthouse-summary.json', JSON.stringify(results, null, 2));
          
          // Generate HTML summary
          const html = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Lighthouse Performance Summary</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .score { display: inline-block; padding: 5px 10px; border-radius: 5px; color: white; }
              .good { background-color: #0cce6b; }
              .average { background-color: #ffa500; }
              .poor { background-color: #ff4e42; }
              table { width: 100%; border-collapse: collapse; margin: 20px 0; }
              th, td { padding: 10px; text-align: left; border: 1px solid #ddd; }
            </style>
          </head>
          <body>
            <h1>Lighthouse Performance Summary</h1>
            <table>
              <tr>
                <th>Page</th>
                <th>Performance</th>
                <th>Accessibility</th>
                <th>Best Practices</th>
                <th>SEO</th>
                <th>FCP</th>
                <th>LCP</th>
                <th>CLS</th>
                <th>TBT</th>
              </tr>
              ${results.map(result => `
                <tr>
                  <td>${result.page}</td>
                  <td><span class="score ${result.performance >= 90 ? 'good' : result.performance >= 50 ? 'average' : 'poor'}">${result.performance}</span></td>
                  <td><span class="score ${result.accessibility >= 90 ? 'good' : result.accessibility >= 50 ? 'average' : 'poor'}">${result.accessibility}</span></td>
                  <td><span class="score ${result.bestPractices >= 90 ? 'good' : result.bestPractices >= 50 ? 'average' : 'poor'}">${result.bestPractices}</span></td>
                  <td><span class="score ${result.seo >= 90 ? 'good' : result.seo >= 50 ? 'average' : 'poor'}">${result.seo}</span></td>
                  <td>${result.metrics.fcp}</td>
                  <td>${result.metrics.lcp}</td>
                  <td>${result.metrics.cls}</td>
                  <td>${result.metrics.tbt}</td>
                </tr>
              `).join('')}
            </table>
          </body>
          </html>
          `;
          
          fs.writeFileSync('lighthouse-summary.html', html);
          EOF
          
          node lighthouse-summary.js
      
      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-results
          path: |
            lighthouse-*.html
            lighthouse-*.json
            lighthouse-summary.*
          retention-days: 30

  # Performance monitoring and alerting
  performance-analysis:
    name: Performance Analysis
    runs-on: ubuntu-latest
    needs: [load-testing, browser-performance, lighthouse-audit]
    if: always()
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download all performance results
        uses: actions/download-artifact@v4
        with:
          path: performance-results/
      
      - name: Analyze performance trends
        run: |
          # Create performance analysis script
          cat > analyze-performance.js << 'EOF'
          const fs = require('fs');
          const path = require('path');
          
          function analyzeResults() {
            const analysis = {
              timestamp: new Date().toISOString(),
              summary: {
                status: 'unknown',
                alerts: [],
                recommendations: []
              },
              metrics: {}
            };
            
            // Analyze Artillery results
            try {
              const artilleryData = JSON.parse(fs.readFileSync('performance-results/load-test-results/artillery-report.json', 'utf8'));
              const summary = artilleryData.aggregate;
              
              analysis.metrics.loadTesting = {
                rps: summary.rps?.mean || 0,
                responseTime: {
                  mean: summary.latency?.mean || 0,
                  p95: summary.latency?.p95 || 0,
                  p99: summary.latency?.p99 || 0
                },
                errorRate: ((summary.counters?.['errors.total'] || 0) / (summary.counters?.['http.responses'] || 1)) * 100,
                totalRequests: summary.counters?.['http.responses'] || 0
              };
              
              // Performance alerts
              if (analysis.metrics.loadTesting.errorRate > 5) {
                analysis.summary.alerts.push(`High error rate: ${analysis.metrics.loadTesting.errorRate.toFixed(2)}%`);
              }
              if (analysis.metrics.loadTesting.responseTime.p95 > 2000) {
                analysis.summary.alerts.push(`High P95 response time: ${analysis.metrics.loadTesting.responseTime.p95}ms`);
              }
              
            } catch (e) {
              console.log('Artillery results not found or invalid');
            }
            
            // Analyze Browser Performance results
            try {
              const browserData = JSON.parse(fs.readFileSync('performance-results/browser-performance-results/performance-results.json', 'utf8'));
              
              analysis.metrics.browserPerformance = {
                tests: browserData.tests.map(test => ({
                  name: test.name,
                  loadTime: test.metrics.loadTime || 0,
                  domContentLoaded: test.metrics.domContentLoaded || 0,
                  firstPaint: test.metrics.firstPaint || 0
                }))
              };
              
              // Check for slow page loads
              browserData.tests.forEach(test => {
                if ((test.metrics.loadTime || 0) > 3000) {
                  analysis.summary.alerts.push(`Slow page load for ${test.name}: ${test.metrics.loadTime}ms`);
                }
              });
              
            } catch (e) {
              console.log('Browser performance results not found or invalid');
            }
            
            // Analyze Lighthouse results
            try {
              const lighthouseData = JSON.parse(fs.readFileSync('performance-results/lighthouse-results/lighthouse-summary.json', 'utf8'));
              
              analysis.metrics.lighthouse = lighthouseData.map(result => ({
                page: result.page,
                scores: {
                  performance: result.performance,
                  accessibility: result.accessibility,
                  bestPractices: result.bestPractices,
                  seo: result.seo
                },
                metrics: result.metrics
              }));
              
              // Check for low Lighthouse scores
              lighthouseData.forEach(result => {
                if (result.performance < 80) {
                  analysis.summary.alerts.push(`Low Lighthouse performance score for ${result.page}: ${result.performance}/100`);
                }
                if (result.accessibility < 90) {
                  analysis.summary.alerts.push(`Low accessibility score for ${result.page}: ${result.accessibility}/100`);
                }
              });
              
            } catch (e) {
              console.log('Lighthouse results not found or invalid');
            }
            
            // Determine overall status
            if (analysis.summary.alerts.length === 0) {
              analysis.summary.status = 'good';
            } else if (analysis.summary.alerts.length <= 2) {
              analysis.summary.status = 'warning';
            } else {
              analysis.summary.status = 'critical';
            }
            
            // Generate recommendations
            if (analysis.metrics.loadTesting?.errorRate > 1) {
              analysis.summary.recommendations.push('Investigate and fix API errors');
            }
            if (analysis.metrics.loadTesting?.responseTime.p95 > 1000) {
              analysis.summary.recommendations.push('Optimize database queries and API responses');
            }
            analysis.summary.recommendations.push('Consider implementing CDN for static assets');
            analysis.summary.recommendations.push('Enable gzip compression for better performance');
            
            return analysis;
          }
          
          const analysis = analyzeResults();
          fs.writeFileSync('performance-analysis.json', JSON.stringify(analysis, null, 2));
          
          console.log('Performance Analysis Summary:');
          console.log(`Status: ${analysis.summary.status}`);
          console.log(`Alerts: ${analysis.summary.alerts.length}`);
          if (analysis.summary.alerts.length > 0) {
            analysis.summary.alerts.forEach(alert => console.log(`- ${alert}`));
          }
          
          // Exit with error if critical issues
          if (analysis.summary.status === 'critical') {
            process.exit(1);
          }
          EOF
          
          node analyze-performance.js
      
      - name: Generate performance dashboard
        run: |
          cat > performance-dashboard.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
            <title>Performance Dashboard</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .dashboard { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
              .card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
              .status-good { border-left: 4px solid #28a745; }
              .status-warning { border-left: 4px solid #ffc107; }
              .status-critical { border-left: 4px solid #dc3545; }
              .alert { padding: 10px; margin: 5px 0; background: #f8f9fa; border-left: 4px solid #dc3545; }
              .metric { display: flex; justify-content: space-between; margin: 10px 0; }
            </style>
          </head>
          <body>
            <h1>Performance Monitoring Dashboard</h1>
            
            <div class="dashboard">
              <div class="card status-good" id="overview">
                <h3>Performance Overview</h3>
                <div id="overview-content"></div>
              </div>
              
              <div class="card">
                <h3>Load Testing Metrics</h3>
                <canvas id="loadTestChart" width="400" height="200"></canvas>
              </div>
              
              <div class="card">
                <h3>Browser Performance</h3>
                <canvas id="browserChart" width="400" height="200"></canvas>
              </div>
              
              <div class="card">
                <h3>Lighthouse Scores</h3>
                <canvas id="lighthouseChart" width="400" height="200"></canvas>
              </div>
            </div>
            
            <div class="card">
              <h3>Performance Alerts</h3>
              <div id="alerts-content"></div>
            </div>
            
            <script>
              // Load and display performance data
              const analysisData = EOF
              cat performance-analysis.json >> performance-dashboard.html
              cat >> performance-dashboard.html << 'EOF'
              ;
              
              // Update overview card
              const overviewCard = document.getElementById('overview');
              overviewCard.className = `card status-${analysisData.summary.status}`;
              document.getElementById('overview-content').innerHTML = `
                <div class="metric">
                  <span>Status:</span>
                  <span><strong>${analysisData.summary.status.toUpperCase()}</strong></span>
                </div>
                <div class="metric">
                  <span>Alerts:</span>
                  <span>${analysisData.summary.alerts.length}</span>
                </div>
                <div class="metric">
                  <span>Test Date:</span>
                  <span>${new Date(analysisData.timestamp).toLocaleString()}</span>
                </div>
              `;
              
              // Display alerts
              document.getElementById('alerts-content').innerHTML = 
                analysisData.summary.alerts.length > 0 
                  ? analysisData.summary.alerts.map(alert => `<div class="alert">${alert}</div>`).join('')
                  : '<p>No performance alerts detected.</p>';
              
              // Create charts if data is available
              if (analysisData.metrics.loadTesting) {
                const ctx1 = document.getElementById('loadTestChart').getContext('2d');
                new Chart(ctx1, {
                  type: 'bar',
                  data: {
                    labels: ['RPS', 'Avg Response', 'P95 Response', 'Error Rate (%)'],
                    datasets: [{
                      data: [
                        analysisData.metrics.loadTesting.rps,
                        analysisData.metrics.loadTesting.responseTime.mean,
                        analysisData.metrics.loadTesting.responseTime.p95,
                        analysisData.metrics.loadTesting.errorRate
                      ],
                      backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
                    }]
                  },
                  options: { responsive: true, legend: { display: false } }
                });
              }
              
              if (analysisData.metrics.lighthouse) {
                const ctx3 = document.getElementById('lighthouseChart').getContext('2d');
                new Chart(ctx3, {
                  type: 'radar',
                  data: {
                    labels: ['Performance', 'Accessibility', 'Best Practices', 'SEO'],
                    datasets: analysisData.metrics.lighthouse.map((result, index) => ({
                      label: result.page,
                      data: [
                        result.scores.performance,
                        result.scores.accessibility,
                        result.scores.bestPractices,
                        result.scores.seo
                      ],
                      borderColor: index === 0 ? '#007bff' : '#28a745',
                      fill: false
                    }))
                  },
                  options: { 
                    responsive: true,
                    scale: { ticks: { beginAtZero: true, max: 100 } }
                  }
                });
              }
            </script>
          </body>
          </html>
          EOF
      
      - name: Upload performance analysis
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-analysis
          path: |
            performance-analysis.json
            performance-dashboard.html
          retention-days: 90
      
      - name: Comment PR with performance results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const analysis = JSON.parse(fs.readFileSync('performance-analysis.json', 'utf8'));
            
            const comment = `
            ## Performance Test Results 🚀
            
            **Status:** ${analysis.summary.status === 'good' ? '✅ Good' : analysis.summary.status === 'warning' ? '⚠️ Warning' : '❌ Critical'}
            **Alerts:** ${analysis.summary.alerts.length}
            
            ${analysis.summary.alerts.length > 0 ? '### Alerts:\n' + analysis.summary.alerts.map(alert => `- ${alert}`).join('\n') : ''}
            
            ### Metrics Summary:
            ${analysis.metrics.loadTesting ? `
            **Load Testing:**
            - RPS: ${analysis.metrics.loadTesting.rps.toFixed(2)}
            - Avg Response Time: ${analysis.metrics.loadTesting.responseTime.mean.toFixed(2)}ms
            - P95 Response Time: ${analysis.metrics.loadTesting.responseTime.p95.toFixed(2)}ms
            - Error Rate: ${analysis.metrics.loadTesting.errorRate.toFixed(2)}%
            ` : ''}
            
            ${analysis.metrics.lighthouse ? `
            **Lighthouse Scores:**
            ${analysis.metrics.lighthouse.map(result => `
            - ${result.page}: Performance ${result.scores.performance}/100, Accessibility ${result.scores.accessibility}/100
            `).join('')}
            ` : ''}
            
            View detailed results in the workflow artifacts.
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
      
      - name: Alert on performance degradation
        if: failure() || github.event_name == 'schedule'
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            🔥 Performance Alert!
            Repository: ${{ github.repository }}
            Status: Critical performance issues detected
            View results: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.PERFORMANCE_SLACK_WEBHOOK }}