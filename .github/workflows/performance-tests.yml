name: Performance Tests

on:
  # Run on push to main branch
  push:
    branches: [ main ]
  
  # Run on pull requests to main
  pull_request:
    branches: [ main ]
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - baseline
          - stress
          - chaos
          - k6
          - artillery
      
      load_level:
        description: 'Load testing level'
        required: false
        default: 'medium'
        type: choice
        options:
          - light
          - medium
          - heavy
      
      duration:
        description: 'Test duration in minutes'
        required: false
        default: '5'
        type: string

  # Scheduled runs (nightly performance regression testing)
  schedule:
    - cron: '0 2 * * *' # Every day at 2 AM UTC

env:
  NODE_VERSION: '18.x'
  API_BASE_URL: 'http://localhost:3000'
  POSTGRES_URL: 'postgresql://postgres:postgres@localhost:5432/ai_platform_test'
  REDIS_URL: 'redis://localhost:6379'
  NODE_ENV: 'test'

jobs:
  # Setup and build
  setup:
    runs-on: ubuntu-latest
    outputs:
      should-run-performance: ${{ steps.check-changes.outputs.should-run }}
      test-type: ${{ github.event.inputs.test_type || 'baseline' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Check for performance-related changes
        id: check-changes
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]] || [[ "${{ github.event_name }}" == "schedule" ]]; then
            echo "should-run=true" >> $GITHUB_OUTPUT
            echo "Manual or scheduled run - running performance tests"
          else
            # Check if performance-critical files changed
            CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
            if echo "$CHANGED_FILES" | grep -E "(backend/src/|tests/performance/|package.*\.json|\.github/workflows/performance)" > /dev/null; then
              echo "should-run=true" >> $GITHUB_OUTPUT
              echo "Performance-related files changed - running performance tests"
            else
              echo "should-run=false" >> $GITHUB_OUTPUT
              echo "No performance-related changes - skipping performance tests"
            fi
          fi

  # Baseline performance testing
  baseline-performance:
    needs: setup
    if: needs.setup.outputs.should-run-performance == 'true' && (needs.setup.outputs.test-type == 'all' || needs.setup.outputs.test-type == 'baseline')
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          npm run build:backend

      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed

      - name: Start backend server
        run: |
          npm run dev:backend &
          sleep 30 # Wait for server to start
          curl -f $API_BASE_URL/health || exit 1

      - name: Run baseline performance tests
        id: baseline-tests
        run: |
          mkdir -p tests/performance/reports
          npm run performance:baseline
          echo "baseline-completed=true" >> $GITHUB_OUTPUT

      - name: Upload baseline results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: baseline-performance-results
          path: |
            tests/performance/reports/baseline-*.json
            tests/performance/reports/baseline-*.html
          retention-days: 30

      - name: Comment baseline results on PR
        if: github.event_name == 'pull_request' && steps.baseline-tests.outputs.baseline-completed == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            try {
              const reportDir = 'tests/performance/reports';
              const files = fs.readdirSync(reportDir);
              const jsonFile = files.find(f => f.startsWith('baseline-') && f.endsWith('.json'));
              
              if (jsonFile) {
                const report = JSON.parse(fs.readFileSync(path.join(reportDir, jsonFile), 'utf8'));
                const summary = report.summary;
                
                const comment = `## 📊 Baseline Performance Results
            
            | Metric | Value | Status |
            |--------|-------|--------|
            | P50 Response Time | ${Math.round(summary.p50)}ms | ${summary.p50 <= 100 ? '✅' : '⚠️'} |
            | P95 Response Time | ${Math.round(summary.p95)}ms | ${summary.p95 <= 500 ? '✅' : '⚠️'} |
            | P99 Response Time | ${Math.round(summary.p99)}ms | ${summary.p99 <= 1000 ? '✅' : '⚠️'} |
            | Throughput | ${Math.round(summary.throughput)} req/sec | ${summary.throughput >= 100 ? '✅' : '⚠️'} |
            | Error Rate | ${(summary.errorRate * 100).toFixed(2)}% | ${summary.errorRate <= 0.01 ? '✅' : '❌'} |
            
            **Total Requests:** ${report.totalRequests}
            **Test Duration:** ${Math.round(report.duration / 1000)}s
            `;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            } catch (error) {
              console.log('Could not post baseline results comment:', error);
            }

  # Jest performance tests
  jest-performance:
    needs: setup
    if: needs.setup.outputs.should-run-performance == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          npm run build:backend

      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed

      - name: Start backend server
        run: |
          npm run dev:backend &
          sleep 30
          curl -f $API_BASE_URL/health || exit 1

      - name: Run Jest performance tests
        run: |
          npm run test:performance
        env:
          JEST_JUNIT_OUTPUT_DIR: tests/performance/reports
          JEST_JUNIT_OUTPUT_NAME: jest-performance-results.xml

      - name: Upload Jest performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: jest-performance-results
          path: |
            tests/performance/reports/jest-performance-results.xml
          retention-days: 30

  # K6 load testing
  k6-load-tests:
    needs: setup
    if: needs.setup.outputs.should-run-performance == 'true' && (needs.setup.outputs.test-type == 'all' || needs.setup.outputs.test-type == 'k6')
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Install dependencies and build
        run: |
          npm ci
          npm run build:backend

      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed

      - name: Start backend server
        run: |
          npm run dev:backend &
          sleep 30
          curl -f $API_BASE_URL/health || exit 1

      - name: Run K6 load tests
        run: |
          mkdir -p tests/performance/reports
          k6 run tests/performance/scenarios/k6-load-test.js \
            --out json=tests/performance/reports/k6-results.json \
            --summary-export tests/performance/reports/k6-summary.json
        env:
          API_BASE: ${{ env.API_BASE_URL }}

      - name: Upload K6 results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: k6-load-test-results
          path: |
            tests/performance/reports/k6-*.json
            tests/performance/reports/k6-*.html
          retention-days: 30

  # Artillery load testing
  artillery-load-tests:
    needs: setup
    if: needs.setup.outputs.should-run-performance == 'true' && (needs.setup.outputs.test-type == 'all' || needs.setup.outputs.test-type == 'artillery')
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies and build
        run: |
          npm ci
          npm run build:backend

      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed

      - name: Start backend server
        run: |
          npm run dev:backend &
          sleep 30
          curl -f $API_BASE_URL/health || exit 1

      - name: Run Artillery load tests
        run: |
          mkdir -p tests/performance/reports
          npx artillery run tests/performance/scenarios/artillery-advanced.yml \
            --output tests/performance/reports/artillery-results.json
          npx artillery report tests/performance/reports/artillery-results.json \
            --output tests/performance/reports/artillery-report.html
        env:
          BASE_URL: ${{ env.API_BASE_URL }}

      - name: Upload Artillery results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: artillery-load-test-results
          path: |
            tests/performance/reports/artillery-*.json
            tests/performance/reports/artillery-*.html
          retention-days: 30

  # Stress testing
  stress-tests:
    needs: setup
    if: needs.setup.outputs.should-run-performance == 'true' && (needs.setup.outputs.test-type == 'all' || needs.setup.outputs.test-type == 'stress')
    runs-on: ubuntu-latest
    timeout-minutes: 35
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies and build
        run: |
          npm ci
          npm run build:backend

      - name: Setup database
        run: |
          npm run db:migrate
          npm run db:seed

      - name: Start backend server
        run: |
          npm run dev:backend &
          sleep 30
          curl -f $API_BASE_URL/health || exit 1

      - name: Run stress tests
        id: stress-tests
        run: |
          mkdir -p tests/performance/reports
          npm run test:stress
          echo "stress-completed=true" >> $GITHUB_OUTPUT

      - name: Upload stress test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: stress-test-results
          path: |
            tests/performance/reports/stress-test-*.json
            tests/performance/reports/stress-test-*.html
          retention-days: 30

  # Performance report generation
  generate-performance-report:
    needs: [baseline-performance, jest-performance, k6-load-tests, artillery-load-tests, stress-tests]
    if: always() && needs.setup.outputs.should-run-performance == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download all performance artifacts
        uses: actions/download-artifact@v4
        with:
          path: tests/performance/reports
          merge-multiple: true

      - name: Generate comprehensive performance report
        run: |
          npm run performance:report

      - name: Upload comprehensive report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: comprehensive-performance-report
          path: |
            tests/performance/reports/comprehensive-report-*.html
            tests/performance/reports/comprehensive-report-*.json
            tests/performance/reports/executive-summary-*.txt
            tests/performance/reports/performance-summary-*.csv
          retention-days: 90

      - name: Comment comprehensive results on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            try {
              const reportDir = 'tests/performance/reports';
              const files = fs.readdirSync(reportDir);
              const summaryFile = files.find(f => f.startsWith('executive-summary-') && f.endsWith('.txt'));
              
              if (summaryFile) {
                const summary = fs.readFileSync(path.join(reportDir, summaryFile), 'utf8');
                
                const comment = `## 🏁 Comprehensive Performance Test Results
            
            ${summary}
            
            📊 **Full reports available in the workflow artifacts.**
            `;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            } catch (error) {
              console.log('Could not post comprehensive results comment:', error);
            }

  # Performance regression check
  performance-regression-check:
    needs: generate-performance-report
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download performance report
        uses: actions/download-artifact@v4
        with:
          name: comprehensive-performance-report
          path: tests/performance/reports

      - name: Check for performance regressions
        id: regression-check
        run: |
          # This would compare against baseline from main branch
          # For now, we'll do basic threshold checking
          node -e "
          const fs = require('fs');
          const files = fs.readdirSync('tests/performance/reports');
          const jsonFile = files.find(f => f.startsWith('comprehensive-report-') && f.endsWith('.json'));
          
          if (jsonFile) {
            const report = JSON.parse(fs.readFileSync('tests/performance/reports/' + jsonFile, 'utf8'));
            const score = report.executive_summary.performance_score;
            const criticalIssues = report.executive_summary.critical_issues.length;
            
            console.log('Performance Score:', score);
            console.log('Critical Issues:', criticalIssues);
            
            if (score < 60) {
              console.log('PERFORMANCE_FAILED=true');
              process.exit(1);
            } else if (score < 80 || criticalIssues > 0) {
              console.log('PERFORMANCE_WARNING=true');
            } else {
              console.log('PERFORMANCE_PASSED=true');
            }
          }
          "

      - name: Fail on performance regression
        if: failure()
        run: |
          echo "❌ Performance regression detected!"
          echo "Performance score is below acceptable threshold."
          exit 1