name: Production Deployment

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

concurrency:
  group: ${{ github.workflow }}-${{ github.event.inputs.deploy_environment || 'production' }}
  cancel-in-progress: false

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  TERRAFORM_VERSION: '1.6.0'
  KUBECTL_VERSION: '1.28.0'
  HELM_VERSION: '3.12.0'

jobs:
  # Pre-deployment validation
  pre-deploy-validation:
    name: Pre-Deploy Validation
    runs-on: ubuntu-latest
    outputs:
      deploy_environment: ${{ steps.env.outputs.environment }}
      image_tag: ${{ steps.meta.outputs.tag }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Determine environment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.deploy_environment }}" >> $GITHUB_OUTPUT
          else
            echo "environment=production" >> $GITHUB_OUTPUT
          fi
      
      - name: Generate image tag
        id: meta
        run: |
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)
          echo "tag=main-${SHORT_SHA}" >> $GITHUB_OUTPUT
      
      - name: Check required secrets
        run: |
          required_secrets=("AWS_ACCESS_KEY_ID" "AWS_SECRET_ACCESS_KEY" "KUBE_CONFIG_DATA")
          for secret in "${required_secrets[@]}"; do
            if [ -z "${!secret}" ]; then
              echo "❌ Required secret $secret is not set"
              exit 1
            fi
          done
          echo "✅ All required secrets are available"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA }}

  # Infrastructure provisioning
  terraform-apply:
    name: Apply Terraform
    runs-on: ubuntu-latest
    needs: pre-deploy-validation
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    defaults:
      run:
        working-directory: infrastructure/terraform
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}
      
      - name: Terraform Init
        run: terraform init
      
      - name: Terraform Plan
        run: |
          terraform plan \
            -var="environment=${{ needs.pre-deploy-validation.outputs.deploy_environment }}" \
            -var="image_tag=${{ needs.pre-deploy-validation.outputs.image_tag }}" \
            -out=tfplan
      
      - name: Terraform Apply
        run: terraform apply -auto-approve tfplan
      
      - name: Save Terraform outputs
        id: tf-output
        run: |
          echo "cluster_name=$(terraform output -raw cluster_name)" >> $GITHUB_OUTPUT
          echo "cluster_endpoint=$(terraform output -raw cluster_endpoint)" >> $GITHUB_OUTPUT

  # Database migrations
  database-migration:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [pre-deploy-validation, terraform-apply]
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NODE_ENV: production
      
      - name: Verify migration status
        run: |
          npx knex migrate:status
        working-directory: backend
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

  # Blue-Green deployment strategy
  deploy-blue-green:
    name: Blue-Green Deployment
    runs-on: ubuntu-latest
    needs: [pre-deploy-validation, terraform-apply, database-migration]
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: ${{ env.HELM_VERSION }}
      
      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_DATA }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
          kubectl config current-context
      
      - name: Prepare deployment manifests
        run: |
          # Replace image tags in Kubernetes manifests
          find infrastructure/kubernetes -name '*.yaml' -exec \
            sed -i "s|:latest|:${{ needs.pre-deploy-validation.outputs.image_tag }}|g" {} \;
          
          # Set environment-specific values
          find infrastructure/kubernetes -name '*.yaml' -exec \
            sed -i "s|ss-platform|ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}|g" {} \;
      
      - name: Deploy to blue environment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Create namespace if it doesn't exist
          kubectl create namespace ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue --dry-run=client -o yaml | kubectl apply -f -
          
          # Deploy to blue environment
          kubectl apply -f infrastructure/kubernetes/ \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue
          
          # Wait for deployment to be ready
          kubectl wait --for=condition=available --timeout=600s \
            deployment/backend deployment/frontend \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue
      
      - name: Run smoke tests on blue environment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get service URL
          BLUE_URL=$(kubectl get service frontend-service \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue \
            -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
          
          # Run smoke tests
          echo "Running smoke tests against: $BLUE_URL"
          curl -f "$BLUE_URL/health" || exit 1
          curl -f "$BLUE_URL/api/health" || exit 1
      
      - name: Switch traffic to blue environment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update ingress to point to blue environment
          kubectl patch ingress main-ingress \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }} \
            --type='json' \
            -p='[{"op": "replace", "path": "/spec/rules/0/http/paths/0/backend/service/name", "value": "frontend-service-blue"}]'
          
          echo "✅ Traffic switched to blue environment"
      
      - name: Cleanup old green environment
        run: |
          export KUBECONFIG=kubeconfig
          
          # Delete green environment after successful blue deployment
          kubectl delete namespace ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-green \
            --ignore-not-found=true
          
          # Rename blue to green for next deployment
          kubectl get namespace ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue \
            -o yaml | \
            sed 's/ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue/ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-green/' | \
            kubectl apply -f -
          
          kubectl delete namespace ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}-blue

  # Canary deployment (alternative strategy)
  deploy-canary:
    name: Canary Deployment
    runs-on: ubuntu-latest
    needs: [pre-deploy-validation, terraform-apply, database-migration]
    if: false  # Disabled by default - enable if preferred over blue-green
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Deploy canary version (10% traffic)
        run: |
          export KUBECONFIG=kubeconfig
          
          # Deploy canary with 10% traffic
          kubectl apply -f infrastructure/kubernetes/canary/
          
          # Configure traffic splitting
          kubectl patch service main-service \
            --type='json' \
            -p='[{"op": "replace", "path": "/spec/selector", "value": {"app": "backend", "version": "canary"}}]'
      
      - name: Monitor canary metrics
        run: |
          echo "Monitoring canary deployment for 5 minutes..."
          sleep 300
          
          # Check error rates and performance metrics
          # This would integrate with your monitoring system
          echo "✅ Canary metrics are healthy"
      
      - name: Promote canary to stable
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update all traffic to canary version
          kubectl patch deployment backend \
            --type='json' \
            -p='[{"op": "replace", "path": "/spec/template/metadata/labels/version", "value": "stable"}]'

  # Post-deployment verification
  post-deploy-verification:
    name: Post-Deploy Verification
    runs-on: ubuntu-latest
    needs: [pre-deploy-validation, deploy-blue-green]
    if: always()
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_DATA }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
      
      - name: Check deployment status
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "=== Deployment Status ==="
          kubectl get deployments \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          
          echo "=== Pod Status ==="
          kubectl get pods \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          
          echo "=== Service Status ==="
          kubectl get services \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
      
      - name: Run comprehensive health checks
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get application URL
          APP_URL=$(kubectl get ingress main-ingress \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }} \
            -o jsonpath='{.spec.rules[0].host}')
          
          echo "Running health checks against: https://$APP_URL"
          
          # API health check
          curl -f "https://$APP_URL/api/health" || exit 1
          
          # Database connectivity check
          curl -f "https://$APP_URL/api/health/database" || exit 1
          
          # Redis connectivity check  
          curl -f "https://$APP_URL/api/health/redis" || exit 1
          
          echo "✅ All health checks passed"
      
      - name: Run performance benchmarks
        run: |
          # Run basic performance tests post-deployment
          echo "Running performance benchmarks..."
          npx artillery quick --count 100 --num 10 "https://$APP_URL/api/health"
      
      - name: Update deployment status
        run: |
          # Update deployment record
          echo "deployment_id=${{ github.run_id }}" >> $GITHUB_ENV
          echo "deployment_time=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_ENV
          echo "git_sha=${{ github.sha }}" >> $GITHUB_ENV

  # Rollback mechanism
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure()
    needs: [pre-deploy-validation, deploy-blue-green, post-deploy-verification]
    environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: ${{ env.KUBECTL_VERSION }}
      
      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_DATA }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
      
      - name: Rollback to previous version
        run: |
          export KUBECONFIG=kubeconfig
          
          echo "🔄 Rolling back deployment..."
          
          # Rollback backend deployment
          kubectl rollout undo deployment/backend \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          
          # Rollback frontend deployment
          kubectl rollout undo deployment/frontend \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          
          # Wait for rollback to complete
          kubectl rollout status deployment/backend \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          kubectl rollout status deployment/frontend \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }}
          
          echo "✅ Rollback completed successfully"
      
      - name: Verify rollback
        run: |
          export KUBECONFIG=kubeconfig
          
          # Run basic health checks after rollback
          APP_URL=$(kubectl get ingress main-ingress \
            --namespace=ss-platform-${{ needs.pre-deploy-validation.outputs.deploy_environment }} \
            -o jsonpath='{.spec.rules[0].host}')
          
          curl -f "https://$APP_URL/api/health" || exit 1
          echo "✅ Rollback verification successful"

  # Notification
  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [pre-deploy-validation, deploy-blue-green, post-deploy-verification]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          text: |
            🚀 Production Deployment ${{ job.status }}
            Environment: ${{ needs.pre-deploy-validation.outputs.deploy_environment }}
            Image Tag: ${{ needs.pre-deploy-validation.outputs.image_tag }}
            Git SHA: ${{ github.sha }}
            Actor: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
      
      - name: Create GitHub deployment
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: '${{ needs.pre-deploy-validation.outputs.deploy_environment }}',
              description: 'Automated deployment via GitHub Actions',
              auto_merge: false,
              required_contexts: []
            });