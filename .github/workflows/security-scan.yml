name: Security Scanning

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io

jobs:
  # Static Application Security Testing (SAST)
  sast-scan:
    name: SAST Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: typescript, javascript
          queries: security-and-quality
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build applications
        run: npm run build
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: '/language:typescript'
      
      - name: Run Semgrep SAST
        uses: semgrep/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/nodejs
            p/typescript
            p/react
            p/docker
            p/kubernetes
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

  # Dependency vulnerability scanning
  dependency-scan:
    name: Dependency Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run npm audit (Backend)
        run: |
          cd backend
          npm audit --audit-level=high --json > ../backend-audit.json || true
          npm audit --audit-level=high
        continue-on-error: true
      
      - name: Run npm audit (Frontend)
        run: |
          cd frontend
          npm audit --audit-level=high --json > ../frontend-audit.json || true
          npm audit --audit-level=high
        continue-on-error: true
      
      - name: Run Snyk vulnerability scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json-file-output=snyk-results.json
        continue-on-error: true
      
      - name: Upload Snyk results to GitHub
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: snyk-results.sarif
      
      - name: Run OWASP Dependency Check
        run: |
          # Download and run OWASP Dependency Check
          wget -O dependency-check.zip https://github.com/jeremylong/DependencyCheck/releases/download/v8.4.0/dependency-check-8.4.0-release.zip
          unzip dependency-check.zip
          
          ./dependency-check/bin/dependency-check.sh \
            --project "AI Services Platform" \
            --scan . \
            --format JSON \
            --format HTML \
            --out dependency-check-report \
            --nvdApiKey ${{ secrets.NVD_API_KEY }} \
            --suppression dependency-check-suppressions.xml
        continue-on-error: true
      
      - name: Upload dependency check results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dependency-check-results
          path: dependency-check-report/
          retention-days: 30

  # Container security scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      packages: read
    
    strategy:
      matrix:
        image: [backend, frontend]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Build container image
        run: |
          docker build -t test-image:latest -f ${{ matrix.image }}/Dockerfile.prod ./${{ matrix.image }}/
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'test-image:latest'
          format: 'sarif'
          output: 'trivy-${{ matrix.image }}-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.image }}-results.sarif'
          category: 'trivy-${{ matrix.image }}'
      
      - name: Run Grype vulnerability scanner
        run: |
          # Install Grype
          curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
          
          # Scan image
          grype test-image:latest -o json > grype-${{ matrix.image }}-results.json
          grype test-image:latest -o table
        continue-on-error: true
      
      - name: Run Dockle security linter
        run: |
          # Install Dockle
          curl -L -o dockle.deb https://github.com/goodwithtech/dockle/releases/download/v0.4.11/dockle_0.4.11_Linux-64bit.deb
          sudo dpkg -i dockle.deb
          
          # Scan image
          dockle --format json --output dockle-${{ matrix.image }}-results.json test-image:latest
          dockle test-image:latest
        continue-on-error: true
      
      - name: Upload container scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: container-scan-${{ matrix.image }}
          path: |
            grype-${{ matrix.image }}-results.json
            dockle-${{ matrix.image }}-results.json
          retention-days: 30

  # Infrastructure security scanning
  infrastructure-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Checkov on Terraform
        uses: bridgecrewio/checkov-action@master
        with:
          directory: infrastructure/terraform
          framework: terraform
          output_format: sarif
          output_file_path: checkov-terraform.sarif
          soft_fail: true
      
      - name: Upload Checkov results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-terraform.sarif
          category: 'checkov-terraform'
      
      - name: Run Checkov on Kubernetes
        uses: bridgecrewio/checkov-action@master
        with:
          directory: infrastructure/kubernetes
          framework: kubernetes
          output_format: sarif
          output_file_path: checkov-kubernetes.sarif
          soft_fail: true
      
      - name: Upload Kubernetes Checkov results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-kubernetes.sarif
          category: 'checkov-kubernetes'
      
      - name: Run Kubesec on Kubernetes manifests
        run: |
          # Install Kubesec
          curl -sSX GET https://v2.kubesec.io/version
          
          # Scan Kubernetes manifests
          find infrastructure/kubernetes -name '*.yaml' -o -name '*.yml' | while read file; do
            echo "Scanning $file"
            curl -sSX POST https://v2.kubesec.io/scan -T "$file" | jq .
          done
        continue-on-error: true
      
      - name: Run Terraform security with tfsec
        run: |
          # Install tfsec
          curl -s https://raw.githubusercontent.com/aquasecurity/tfsec/master/scripts/install_linux.sh | bash
          
          # Scan Terraform files
          tfsec infrastructure/terraform --format json > tfsec-results.json
          tfsec infrastructure/terraform
        continue-on-error: true
      
      - name: Upload infrastructure scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: infrastructure-security-scan
          path: |
            tfsec-results.json
          retention-days: 30

  # Secrets scanning
  secrets-scan:
    name: Secrets Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Run GitLeaks
        run: |
          # Install GitLeaks
          wget https://github.com/gitleaks/gitleaks/releases/download/v8.18.0/gitleaks_8.18.0_linux_x64.tar.gz
          tar xzf gitleaks_8.18.0_linux_x64.tar.gz
          
          # Scan for secrets
          ./gitleaks detect --source . --report-path gitleaks-report.json --report-format json
          ./gitleaks detect --source . --report-path gitleaks-report.sarif --report-format sarif
        continue-on-error: true
      
      - name: Upload GitLeaks results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: gitleaks-report.sarif
          category: 'gitleaks'
      
      - name: Run TruffleHog
        run: |
          # Install TruffleHog
          curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sh -s -- -b /usr/local/bin
          
          # Scan for secrets
          trufflehog git file://. --json > trufflehog-results.json
        continue-on-error: true
      
      - name: Upload secrets scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: secrets-scan-results
          path: |
            gitleaks-report.json
            trufflehog-results.json
          retention-days: 30

  # Dynamic Application Security Testing (DAST)
  dast-scan:
    name: DAST Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies and build
        run: |
          npm ci
          npm run build
      
      - name: Start application
        run: |
          npm run db:migrate
          npm run start --workspace=backend &
          sleep 30
          npm run preview --workspace=frontend &
          sleep 10
        env:
          NODE_ENV: production
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test_jwt_secret
          SESSION_SECRET: test_session_secret
      
      - name: Run OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.10.0
        with:
          target: 'http://localhost:3001'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
      
      - name: Run OWASP ZAP Full Scan
        uses: zaproxy/action-full-scan@v0.8.0
        with:
          target: 'http://localhost:3001'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
        continue-on-error: true
      
      - name: Upload DAST results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dast-scan-results
          path: |
            report_html.html
            report_json.json
          retention-days: 30

  # Compliance scanning
  compliance-scan:
    name: Compliance Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run CIS Docker Benchmark
        run: |
          # Download and run Docker Bench Security
          git clone https://github.com/docker/docker-bench-security.git
          cd docker-bench-security
          sudo sh docker-bench-security.sh -l docker-bench-results.log
        continue-on-error: true
      
      - name: Run CIS Kubernetes Benchmark with kube-bench
        run: |
          # Install kube-bench
          curl -L https://github.com/aquasecurity/kube-bench/releases/download/v0.6.15/kube-bench_0.6.15_linux_amd64.tar.gz -o kube-bench.tar.gz
          tar -xzf kube-bench.tar.gz
          
          # This would normally run against a real cluster
          echo "Kube-bench would run against Kubernetes cluster in production"
        continue-on-error: true
      
      - name: Generate compliance report
        run: |
          echo "# Security Compliance Report" > compliance-report.md
          echo "Generated on: $(date)" >> compliance-report.md
          echo "## Scans Completed:" >> compliance-report.md
          echo "- SAST Analysis" >> compliance-report.md
          echo "- Dependency Vulnerability Scan" >> compliance-report.md
          echo "- Container Security Scan" >> compliance-report.md
          echo "- Infrastructure Security Scan" >> compliance-report.md
          echo "- Secrets Detection" >> compliance-report.md
          echo "- DAST Analysis (scheduled runs)" >> compliance-report.md
          echo "- Compliance Checks" >> compliance-report.md
      
      - name: Upload compliance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: compliance-scan-results
          path: |
            compliance-report.md
            docker-bench-security/docker-bench-results.log
          retention-days: 90

  # Security summary and reporting
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [sast-scan, dependency-scan, container-scan, infrastructure-scan, secrets-scan]
    if: always()
    permissions:
      contents: read
      security-events: write
      issues: write
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: security-results/
      
      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "**Scan Date:** $(date)" >> security-summary.md
          echo "**Git SHA:** ${{ github.sha }}" >> security-summary.md
          echo "**Workflow:** ${{ github.workflow }}" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Scan Results" >> security-summary.md
          echo "| Scan Type | Status | Artifacts |" >> security-summary.md
          echo "|-----------|--------|-----------|" >> security-summary.md
          echo "| SAST Analysis | ${{ needs.sast-scan.result }} | CodeQL, Semgrep |" >> security-summary.md
          echo "| Dependency Scan | ${{ needs.dependency-scan.result }} | npm audit, Snyk, OWASP |" >> security-summary.md
          echo "| Container Scan | ${{ needs.container-scan.result }} | Trivy, Grype, Dockle |" >> security-summary.md
          echo "| Infrastructure Scan | ${{ needs.infrastructure-scan.result }} | Checkov, tfsec, Kubesec |" >> security-summary.md
          echo "| Secrets Scan | ${{ needs.secrets-scan.result }} | GitLeaks, TruffleHog |" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Next Steps" >> security-summary.md
          echo "- Review security findings in GitHub Security tab" >> security-summary.md
          echo "- Address high and critical vulnerabilities" >> security-summary.md
          echo "- Update dependencies with known vulnerabilities" >> security-summary.md
          echo "- Review infrastructure configurations" >> security-summary.md
      
      - name: Upload security summary
        uses: actions/upload-artifact@v4
        with:
          name: security-summary
          path: security-summary.md
          retention-days: 90
      
      - name: Create security issue (if failures)
        if: |
          needs.sast-scan.result == 'failure' ||
          needs.dependency-scan.result == 'failure' ||
          needs.container-scan.result == 'failure' ||
          needs.infrastructure-scan.result == 'failure' ||
          needs.secrets-scan.result == 'failure'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary.md', 'utf8');
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `Security Scan Failures - ${new Date().toISOString().split('T')[0]}`,
              body: `## Security Scan Results\n\n${summary}\n\n**Action Required:** Please review and address the security findings.`,
              labels: ['security', 'high-priority']
            });
      
      - name: Notify security team
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            🚨 Security scan failures detected!
            Repository: ${{ github.repository }}
            Workflow: ${{ github.workflow }}
            SHA: ${{ github.sha }}
            Please review security findings immediately.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SECURITY_SLACK_WEBHOOK }}