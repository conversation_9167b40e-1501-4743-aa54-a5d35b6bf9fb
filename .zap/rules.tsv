# ZAP Scanning Rules Configuration
# This file defines rules for OWASP ZAP security scanning

# Format: RULE_ID	RULE_NAME	THRESHOLD	ACTION
# Actions: IGNORE, INFO, LOW, MEDIUM, HIGH
# Thresholds: OFF, LOW, MEDIUM, HIGH

# Authentication and Session Management
10010	Cookie No HttpOnly Flag	MEDIUM	HIGH
10011	Cookie Without Secure Flag	MEDIUM	HIGH
10015	Incomplete or No Cache-control Header	LOW	MEDIUM
10016	Web Browser XSS Protection Not Enabled	LOW	MEDIUM
10017	Cross-Domain JavaScript Source File Inclusion	LOW	INFO
10019	Content-Type Header Missing	LOW	MEDIUM
10020	Missing Anti-clickjacking Header	LOW	MEDIUM
10021	X-Content-Type-Options Header Missing	LOW	MEDIUM
10023	Information Disclosure - Debug Error Messages	MEDIUM	HIGH
10024	Information Disclosure - Sensitive Information in URL	MEDIUM	HIGH
10025	Information Disclosure - Sensitive Information in HTTP Referrer Header	LOW	MEDIUM
10026	HTTP Parameter Override	MEDIUM	MEDIUM
10027	Information Disclosure - Suspicious Comments	LOW	INFO
10028	Open Redirect	MEDIUM	HIGH
10029	Cookie Poisoning	MEDIUM	HIGH
10030	User Controllable Charset	MEDIUM	MEDIUM
10031	User Controllable HTML Element Attribute (Potential XSS)	MEDIUM	HIGH
10032	Viewstate	LOW	INFO
10033	Directory Browsing	MEDIUM	MEDIUM
10034	Heartbleed OpenSSL Vulnerability	HIGH	HIGH
10035	Strict-Transport-Security Header	LOW	MEDIUM
10036	HTTP Server Response Header	LOW	INFO
10037	Server Leaks Information via X-Powered-By HTTP Response Header Field(s)	LOW	INFO
10038	Content Security Policy (CSP) Header Not Set	MEDIUM	HIGH
10039	X-Backend-Server Header Information Leak	LOW	INFO
10040	Secure Pages Include Mixed Content	MEDIUM	HIGH
10041	HTTP to HTTPS Insecure Transition in Form Post	MEDIUM	HIGH
10042	HTTPS to HTTP Insecure Transition in Form Post	MEDIUM	HIGH
10043	User Controllable JavaScript Event (XSS)	HIGH	HIGH
10044	Big Redirect Detected (Potential Sensitive Information Leak)	LOW	MEDIUM
10045	Source Code Disclosure - /WEB-INF folder	HIGH	HIGH
10046	Source Code Disclosure - Git	MEDIUM	HIGH
10047	Source Code Disclosure - SVN	MEDIUM	HIGH
10048	Remote Code Execution - Shell Shock	HIGH	HIGH
10049	Content Cacheability	LOW	INFO
10050	Retrieved from Cache	LOW	INFO
10051	Relative Path Confusion	MEDIUM	MEDIUM
10052	X-ChromeLogger-Data (XCLD) Header Information Leak	MEDIUM	MEDIUM
10054	Cookie without SameSite Attribute	LOW	MEDIUM
10055	CSP Scanner	MEDIUM	HIGH
10056	X-Debug-Token Information Leak	MEDIUM	MEDIUM
10057	Username Hash Found	LOW	INFO
10061	X-AspNet-Version Response Header	LOW	INFO
10062	PII Disclosure	MEDIUM	HIGH
10063	Permissions Policy Header Not Set	LOW	MEDIUM
10096	Timestamp Disclosure	LOW	INFO
10097	Hash Disclosure	LOW	INFO
10098	Cross-Domain Misconfiguration	MEDIUM	HIGH
10099	Source Code Disclosure	HIGH	HIGH

# SQL Injection
40018	SQL Injection	HIGH	HIGH
40019	SQL Injection - MySQL	HIGH	HIGH
40020	SQL Injection - Hypersonic SQL	HIGH	HIGH
40021	SQL Injection - Oracle	HIGH	HIGH
40022	SQL Injection - PostgreSQL	HIGH	HIGH
40023	Possible Username Enumeration	MEDIUM	MEDIUM
40024	SQL Injection - SQLite	HIGH	HIGH
40025	Proxy Disclosure	MEDIUM	MEDIUM
40026	Cross Site Scripting (Reflected)	HIGH	HIGH
40027	Cross Site Scripting (Persistent)	HIGH	HIGH
40028	Cross Site Scripting (Persistent) - Prime	HIGH	HIGH
40029	Cross Site Scripting (Persistent) - Spider	HIGH	HIGH

# Custom rules for AI Services Platform
90001	API Key in URL	HIGH	HIGH
90002	Sensitive Data in Response	MEDIUM	HIGH
90003	Weak JWT Configuration	HIGH	HIGH
90004	Database Connection String Exposure	HIGH	HIGH
90005	Admin Panel Accessible	HIGH	HIGH

# Ignored rules for development/testing
10108	Reverse Tabnabbing	LOW	IGNORE
10109	Modern Web Application	LOW	IGNORE

# Custom ignore patterns for legitimate application behavior
# These should be reviewed periodically
50001	Health Check Endpoints	LOW	IGNORE
50002	API Documentation Endpoints	LOW	IGNORE
50003	Metrics Endpoints (Authenticated)	LOW	INFO