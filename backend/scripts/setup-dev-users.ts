#!/usr/bin/env ts-node

/**
 * Setup Development Test Users Script
 * 
 * This script creates test users for development purposes
 * Usage: npm run setup:dev-users
 */

import { Database } from '../src/database/database';
import { seed, testUsers } from '../src/database/seeds/dev_test_users';

async function setupDevUsers() {
  console.log('🚀 Setting up development test users...\n');

  // Check environment
  if (process.env.NODE_ENV === 'production') {
    console.error('❌ Cannot run development setup in production environment');
    process.exit(1);
  }

  try {
    // Initialize database
    const database = Database.getInstance();
    await database.connect();
    const knex = database.getKnex();

    // Run the seeder
    await seed(knex);

    console.log('✅ Development setup completed successfully!\n');
    
    // Show quick access information
    console.log('🔑 Quick Login Options:');
    console.log('======================');
    console.log('');
    
    console.log('Option 1: Use the development bypass endpoint');
    console.log('POST /api/auth/dev/test-login');
    console.log('Body: { "email": "<EMAIL>" } (optional - <NAME_EMAIL>)');
    console.log('');
    
    console.log('Option 2: Use regular login with any of these accounts:');
    testUsers.slice(0, 3).forEach(user => {
      console.log(`   Email: ${user.email}`);
      console.log(`   Password: ${user.password}`);
      console.log(`   Role: ${user.role}`);
      console.log('');
    });

    console.log('Option 3: Create all test users via API');
    console.log('POST /api/auth/dev/create-test-users');
    console.log('(No body required - creates all test users)');
    console.log('');
    
    console.log('📝 Frontend Integration:');
    console.log('========================');
    console.log('Add this to your frontend development environment:');
    console.log('');
    console.log('// Quick development login');
    console.log('const devLogin = async () => {');
    console.log('  const response = await fetch("/api/auth/dev/test-login", {');
    console.log('    method: "POST",');
    console.log('    headers: { "Content-Type": "application/json" },');
    console.log('    body: JSON.stringify({ email: "<EMAIL>" })');
    console.log('  });');
    console.log('  const data = await response.json();');
    console.log('  localStorage.setItem("accessToken", data.data.accessToken);');
    console.log('  window.location.reload();');
    console.log('};');
    console.log('');

    // Close database connection
    await knex.destroy();

  } catch (error) {
    console.error('❌ Error during development setup:', error);
    process.exit(1);
  }
}

// Run the setup
setupDevUsers().catch(console.error);