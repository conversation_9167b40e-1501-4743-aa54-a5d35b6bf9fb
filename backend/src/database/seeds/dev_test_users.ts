import { K<PERSON> } from 'knex';
import { PasswordUtils } from '../../utils/password';

/**
 * Development Test Users Seeder
 * Creates test users for development and testing purposes
 * Only runs in development/test environments
 */

const testUsers = [
  {
    id: '********-0000-4000-8000-********0001',
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    first_name: 'Admin',
    last_name: 'User',
    role: 'admin',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'AI Services Admin',
      phone_number: '******-0001',
      timezone: 'America/New_York',
    },
    preferences: {
      theme: 'dark',
      email_notifications: true,
      push_notifications: true,
      marketing_emails: false,
      two_factor_auth: false,
      session_timeout: 60,
      auto_logout: true,
      default_dashboard: 'admin',
    },
  },
  {
    id: '********-0000-4000-8000-********0002',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    first_name: 'Test',
    last_name: 'User',
    role: 'user',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'Test Company',
      phone_number: '******-0002',
      timezone: 'America/Los_Angeles',
    },
    preferences: {
      theme: 'light',
      email_notifications: true,
      push_notifications: false,
      marketing_emails: false,
      two_factor_auth: false,
      session_timeout: 30,
      auto_logout: false,
      default_dashboard: 'overview',
    },
  },
  {
    id: '********-0000-4000-8000-********0003',
    email: '<EMAIL>',
    password: 'UserPassword123!',
    first_name: 'John',
    last_name: 'Doe',
    role: 'user',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'Acme Corporation',
      phone_number: '******-0003',
      timezone: 'America/Chicago',
      bio: 'Software developer interested in AI and automation.',
    },
    preferences: {
      theme: 'system',
      email_notifications: true,
      push_notifications: true,
      marketing_emails: true,
      two_factor_auth: false,
      session_timeout: 45,
      auto_logout: true,
      default_dashboard: 'services',
    },
  },
  {
    id: '********-0000-4000-8000-********0004',
    email: '<EMAIL>',
    password: 'UserPassword123!',
    first_name: 'Jane',
    last_name: 'Smith',
    role: 'user',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'TechCorp Inc',
      phone_number: '******-0004',
      timezone: 'America/New_York',
      bio: 'Product manager focused on AI-driven solutions.',
    },
    preferences: {
      theme: 'dark',
      email_notifications: false,
      push_notifications: true,
      marketing_emails: false,
      two_factor_auth: false,
      session_timeout: 90,
      auto_logout: false,
      default_dashboard: 'workflows',
    },
  },
  {
    id: '********-0000-4000-8000-********0005',
    email: '<EMAIL>',
    password: 'PremiumPassword123!',
    first_name: 'Premium',
    last_name: 'User',
    role: 'premium',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'Premium Enterprise Corp',
      phone_number: '******-0005',
      timezone: 'Europe/London',
      bio: 'Enterprise customer with premium subscription.',
    },
    preferences: {
      theme: 'dark',
      email_notifications: true,
      push_notifications: true,
      marketing_emails: true,
      two_factor_auth: false,
      session_timeout: 120,
      auto_logout: false,
      default_dashboard: 'analytics',
    },
  },
  {
    id: '********-0000-4000-8000-********0006',
    email: '<EMAIL>',
    password: 'DevPassword123!',
    first_name: 'Developer',
    last_name: 'Test',
    role: 'developer',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'AI Services Development',
      phone_number: '******-0006',
      timezone: 'America/Los_Angeles',
      bio: 'Internal developer account for testing API integrations.',
    },
    preferences: {
      theme: 'dark',
      email_notifications: false,
      push_notifications: false,
      marketing_emails: false,
      two_factor_auth: false,
      session_timeout: 480,
      auto_logout: false,
      default_dashboard: 'api',
    },
  },
  {
    id: '********-0000-4000-8000-************',
    email: '<EMAIL>',
    password: 'SS-t00clever',
    first_name: 'Rain',
    last_name: 'Admin',
    role: 'admin',
    status: 'active',
    email_verified_at: new Date(),
    profile: {
      company_name: 'SingleSource Solutions',
      phone_number: '******-0007',
      timezone: 'America/New_York',
      bio: 'Primary administrator and system owner.',
    },
    preferences: {
      theme: 'dark',
      email_notifications: true,
      push_notifications: true,
      marketing_emails: false,
      two_factor_auth: false,
      session_timeout: 480,
      auto_logout: false,
      default_dashboard: 'admin',
    },
  },
];

export async function seed(knex: Knex): Promise<void> {
  // Only run in development or test environments
  if (process.env.NODE_ENV === 'production') {
    console.log('⚠️  Skipping test user seeder in production environment');
    return;
  }

  console.log('🌱 Seeding development test users...');

  try {
    // Initialize password utils
    const passwordUtils = PasswordUtils.getInstance();

    // Clear existing test users (optional - uncomment to reset)
    // await knex('users').whereIn('email', testUsers.map(u => u.email)).del();

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await knex('users').where('email', userData.email).first();
        
        if (existingUser) {
          console.log(`   User ${userData.email} already exists, skipping...`);
          continue;
        }

        // Hash password
        const passwordHash = await passwordUtils.hashPassword(userData.password);

        // Create user
        await knex('users').insert({
          id: userData.id,
          email: userData.email,
          password_hash: passwordHash,
          first_name: userData.first_name,
          last_name: userData.last_name,
          role: userData.role,
          status: userData.status,
          profile: JSON.stringify(userData.profile),
          preferences: JSON.stringify(userData.preferences),
          settings: JSON.stringify({}),
          email_verified_at: userData.email_verified_at,
          last_login_at: null,
          created_at: new Date(),
          updated_at: new Date(),
        });

        console.log(`   ✓ Created test user: ${userData.email} (${userData.role})`);
      } catch (userError) {
        console.error(`   ✗ Failed to create user ${userData.email}:`, userError);
      }
    }

    console.log('🎉 Test users seeding completed!');
    console.log('');
    console.log('📋 Available Test Users:');
    console.log('========================');
    testUsers.forEach(user => {
      console.log(`   ${user.email} (${user.role})`);
      console.log(`   Password: ${user.password}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error seeding test users:', error);
    throw error;
  }
}

// Export test users for use in other places
export { testUsers };