# Crush Setup Guide for <PERSON> Max Account

## Quick Setup Steps

### Step 1: Get Your Claude Session Token

1. **Open [claude.ai](https://claude.ai) in your browser**
2. **Make sure you're logged in to your Claude Max account**
3. **Open Developer Tools:**
   - Press `F12` OR
   - Right-click → "Inspect" OR  
   - Press `Cmd+Option+I` (Mac)

4. **Get the session token:**
   - Click "Application" tab (Chrome) or "Storage" tab (Firefox)
   - Expand "Cookies" in left sidebar
   - Click "https://claude.ai"
   - Find cookie named `sessionKey` 
   - Copy the entire value (long string)

### Step 2: Set Up Environment Variable

```bash
# Add to your shell profile
echo 'export ANTHROPIC_SESSION_TOKEN="your_session_token_here"' >> ~/.zprofile

# Or set for current session
export ANTHROPIC_SESSION_TOKEN="your_session_token_here"
```

### Step 3: Run Crush Setup

```bash
# Navigate to your project directory
cd /Users/<USER>/development/ss_site

# Run Crush interactively to configure
~/go/bin/crush
```

### Step 4: Select Claude Model

When Crush starts:
1. Use ↑/↓ arrows to navigate to a <PERSON> model
2. Choose one of these (included with your Claude Max):
   - **Claude Sonnet 4** (recommended)
   - **Claude Opus 4.1** (most capable)
   - **Claude 3.5 Sonnet (New)** (fast and reliable)
3. Press Enter to select

### Step 5: Authentication

When prompted for API key:
- If you set the environment variable, it should use your session token
- If it still asks for API key, you may need to:
  - Check if session token is correctly set
  - Try browser-based authentication if available
  - Use the session token as the API key

## Alternative: Manual Configuration

If the session token doesn't work, you can:

1. **Get an API key from Anthropic Console:**
   - Go to [console.anthropic.com](https://console.anthropic.com)
   - Create an API key
   - Note: This will use API credits separate from your Claude Max subscription

2. **Set the API key:**
   ```bash
   export ANTHROPIC_API_KEY="your_api_key_here"
   ```

## Troubleshooting

### If Crush says "no providers configured":
```bash
# Run interactive setup
~/go/bin/crush
```

### If session token doesn't work:
1. Make sure you copied the entire sessionKey value
2. Check that you're logged into the correct Claude account
3. Try refreshing claude.ai and getting a new session token

### If you get authentication errors:
1. Verify your Claude Max subscription is active
2. Try logging out and back into claude.ai
3. Get a fresh session token

## Usage After Setup

Once configured, you can use Crush:

```bash
# Interactive mode
crush

# Single prompt
crush run "your question here"

# With debug logging
crush -d

# In specific directory
crush -c /path/to/project
```

## Models Available with Claude Max

✅ **Included with your subscription:**
- Claude Opus 4.1
- Claude Opus 4  
- Claude Sonnet 4
- Claude 3.7 Sonnet
- Claude 3.5 Haiku
- Claude 3.5 Sonnet (Old)
- Claude 3.5 Sonnet (New)

❌ **Not included (need separate API keys):**
- All OpenAI models (GPT-5, GPT-4.1, etc.)
