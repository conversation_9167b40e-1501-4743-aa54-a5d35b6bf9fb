# Development Plan: Codebase Transformation

## Current State Analysis

### What We Have
1. **Backend**: 8 AI services fully implemented and working
2. **Authentication**: Basic JWT auth with email/password
3. **Database**: User management, API keys, basic subscriptions
4. **Frontend**: Single dashboard app with routing
5. **Infrastructure**: Docker, monitoring, testing setup

### What Needs Transformation
1. **Frontend**: From single app to multi-app architecture
2. **Auth**: Add OAuth providers (Google, GitHub)
3. **User Roles**: Implement comprehensive RBAC system
4. **Dashboard**: From static to dynamic/modular
5. **New Features**: Voice agent, landing page, service registry

## Transformation Roadmap

### Week 1-2: Architecture Refactoring

#### 1.1 Frontend Restructure
```bash
# Current structure
/frontend
  /src
    /pages
    /components
    /stores

# New structure
/frontend
  /apps
    /landing      # New Next.js app for public site
    /portal       # Existing React app becomes client portal
    /admin        # New admin dashboard app
    /employee     # New employee dashboard app
  /packages
    /ui           # Shared component library
    /voice-agent  # WebGL voice agent package
    /auth         # Shared auth logic
    /types        # Shared TypeScript types
```

**Action Items:**
```bash
# Create new workspace structure
mkdir -p frontend/apps/{landing,portal,admin,employee}
mkdir -p frontend/packages/{ui,voice-agent,auth,types}

# Move existing frontend to portal
mv frontend/src frontend/apps/portal/src
mv frontend/package.json frontend/apps/portal/

# Update root package.json for Turborepo
npm install -D turbo
```

#### 1.2 Backend Module Organization
```bash
# New backend modules
mkdir -p backend/src/modules/{consultation,requirements,orchestration,provisioning}
mkdir -p backend/src/modules/admin/{service-registry,rbac}
```

### Week 3-4: Authentication Enhancement

#### 2.1 OAuth Implementation
```typescript
// backend/src/modules/auth/providers/oauth.config.ts
export const oauthProviders = {
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: '/api/auth/google/callback'
  },
  github: {
    clientId: process.env.GITHUB_CLIENT_ID,
    clientSecret: process.env.GITHUB_CLIENT_SECRET,
    callbackURL: '/api/auth/github/callback'
  }
}

// Implement Passport.js strategies
npm install passport passport-google-oauth20 passport-github2
```

#### 2.2 Role-Based Access Control
```typescript
// backend/src/modules/admin/rbac/roles.ts
export enum Role {
  CLIENT = 'client',
  EMPLOYEE = 'employee',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export const permissions = {
  [Role.CLIENT]: ['read:own_dashboard', 'read:own_projects'],
  [Role.EMPLOYEE]: ['read:assigned_clients', 'use:authorized_services'],
  [Role.ADMIN]: ['manage:all_clients', 'manage:services', 'manage:employees'],
  [Role.SUPER_ADMIN]: ['*']
}
```

### Week 5-6: Database Schema Updates

```sql
-- Add to migrations
ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT 'client';
ALTER TABLE users ADD COLUMN oauth_provider VARCHAR(50);
ALTER TABLE users ADD COLUMN oauth_id VARCHAR(255);

-- New tables (as specified in scope document)
CREATE TABLE consultations (...);
CREATE TABLE client_dashboards (...);
CREATE TABLE service_registry (...);
CREATE TABLE employee_permissions (...);
```

### Week 7-8: Landing Page Development

#### 3.1 Create Next.js Landing App
```bash
cd frontend/apps/landing
npx create-next-app@latest . --typescript --tailwind --app
```

#### 3.2 Landing Page Components
```typescript
// frontend/apps/landing/src/components/sections/
- HeroSection.tsx         # Main value prop with particle effects
- ServicesShowcase.tsx    # Beautiful cards for 8 services
- HowItWorks.tsx         # Voice agent explanation
- CaseStudies.tsx        # Success stories
- CTASection.tsx         # Start Now button
```

### Week 9-12: Voice Agent Development

#### 4.1 WebGL Particle Face
```typescript
// frontend/packages/voice-agent/src/ParticleFace.tsx
import * as THREE from 'three'
import { useThree, useFrame } from '@react-three/fiber'

export const ParticleFace = () => {
  // Implement particle system for realistic face
  // Use morph targets for lip sync
  // Add ambient animations
}
```

#### 4.2 Voice Integration
```typescript
// frontend/packages/voice-agent/src/VoiceEngine.ts
class VoiceEngine {
  private recognition: SpeechRecognition
  private synthesis: SpeechSynthesis
  private websocket: WebSocket
  
  async startConsultation(userId: string) {
    // Connect to backend consultation engine
    // Stream audio to Whisper API
    // Get responses from GPT-4
    // Synthesize speech with ElevenLabs
  }
}
```

### Week 13-16: Modular Dashboard System

#### 5.1 Widget Registry
```typescript
// frontend/packages/ui/src/dashboard/WidgetRegistry.ts
interface Widget {
  id: string
  component: React.ComponentType
  defaultProps: Record<string, any>
  permissions: string[]
}

export class WidgetRegistry {
  private widgets = new Map<string, Widget>()
  
  register(widget: Widget) {
    this.widgets.set(widget.id, widget)
  }
  
  getForUser(userRole: Role): Widget[] {
    // Return widgets based on permissions
  }
}
```

#### 5.2 Dashboard Provisioning
```typescript
// backend/src/modules/provisioning/DashboardProvisioner.ts
export class DashboardProvisioner {
  async createDashboard(
    userId: string, 
    requirements: ExtractedRequirements
  ): Promise<DashboardConfig> {
    // Analyze requirements
    // Select appropriate widgets
    // Configure widget properties
    // Save to database
    // Return configuration
  }
}
```

### Week 17-20: Service Integration Platform

#### 6.1 Service Registry UI
```typescript
// frontend/apps/admin/src/pages/ServiceRegistry.tsx
export const ServiceRegistry = () => {
  // Drag-drop interface for services
  // Integration wizard
  // Health monitoring dashboard
  // Usage analytics
}
```

#### 6.2 Integration Framework
```typescript
// backend/src/modules/admin/service-registry/IntegrationManager.ts
export class IntegrationManager {
  async addSDKService(config: SDKConfig) {
    // Validate npm package
    // Install dependencies
    // Register in database
    // Set up health checks
  }
  
  async addAPIService(config: APIConfig) {
    // Validate endpoints
    // Set up API gateway
    // Configure rate limiting
    // Register in database
  }
  
  async addMCPService(config: MCPConfig) {
    // Establish WebSocket connection
    // Validate protocol
    // Register handlers
    // Register in database
  }
}
```

## Implementation Priority

### Immediate Actions (This Week)
1. Create the architecture document ✅
2. Set up Turborepo for monorepo management
3. Create database migration for new tables
4. Start OAuth implementation

### Short-term (Next 2 Weeks)
1. Build landing page with "Start Now" flow
2. Implement RBAC system
3. Create basic voice agent prototype
4. Set up modular dashboard architecture

### Medium-term (Next Month)
1. Complete voice agent with WebGL face
2. Build admin dashboard
3. Implement service registry
4. Create employee dashboard

### Long-term (Next Quarter)
1. Polish all user interfaces
2. Optimize performance
3. Add advanced analytics
4. Prepare for scale

## Technical Decisions

### Frontend Stack
- **Monorepo**: Turborepo for build orchestration
- **Landing**: Next.js 14 with App Router
- **Dashboards**: Keep existing React/Vite setup
- **Voice Agent**: Three.js + React Three Fiber
- **Shared UI**: Radix UI + Custom design system

### Backend Enhancements
- **API Gateway**: Add Kong or custom gateway
- **Queue System**: Bull for background jobs
- **Real-time**: Enhance WebSocket service
- **Caching**: Redis for session management

### Infrastructure Updates
- **CDN**: CloudFlare for landing page
- **Media**: S3 for voice recordings
- **Search**: Elasticsearch for consultations
- **Monitoring**: Enhance with Datadog

## Migration Strategy

### Phase 1: Non-breaking Changes
- Add new tables without touching existing
- Create new frontend apps alongside existing
- Implement OAuth as additional option
- Build voice agent as separate module

### Phase 2: Gradual Migration
- Move existing users to new role system
- Transition dashboards to modular system
- Migrate services to registry pattern
- Update authentication flows

### Phase 3: Cleanup
- Remove deprecated code
- Optimize database queries
- Consolidate duplicate logic
- Update documentation

## Risk Mitigation

### Technical Risks
- **Voice Agent Complexity**: Start with simple version
- **Performance**: Implement caching early
- **Integration Issues**: Comprehensive testing
- **Scalability**: Design for horizontal scaling

### Business Risks
- **User Migration**: Gradual rollout with fallbacks
- **Service Disruption**: Feature flags for new features
- **Data Loss**: Comprehensive backup strategy
- **Security**: Regular penetration testing

## Success Criteria

### Technical Metrics
- Landing page loads < 2s
- Voice agent latency < 500ms
- Dashboard renders < 1s
- 99.9% uptime for services

### Business Metrics
- 50% reduction in consultation time
- 80% client satisfaction score
- 90% successful project completion
- 30% increase in revenue per client

## Next Steps

1. **Review and approve** this development plan
2. **Set up project tracking** in Linear/Jira
3. **Allocate resources** for each phase
4. **Begin Phase 1** implementation
5. **Schedule weekly reviews** for progress

This transformation will position the platform as a cutting-edge AI consultancy service, differentiating it from direct service providers and creating a unique value proposition in the B2B market.