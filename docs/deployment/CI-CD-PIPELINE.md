# CI/CD Pipeline Documentation

## Overview

This document describes the comprehensive CI/CD pipeline implemented for the AI Services Platform using GitHub Actions. The pipeline provides automated testing, security scanning, building, and deployment across multiple environments with proper approval workflows and rollback mechanisms.

## Pipeline Architecture

```mermaid
graph TB
    A[Code Push/PR] --> B[CI Pipeline]
    B --> C[Code Quality & Testing]
    C --> D[Security Scanning]
    D --> E[Build & Push Images]
    E --> F{Branch Check}
    
    F -->|develop| G[Deploy to Staging]
    F -->|main| H[Deploy to Production]
    
    G --> I[Staging Tests]
    I --> J[Performance Testing]
    
    H --> K[Infrastructure Deployment]
    K --> L[Database Migration]
    L --> M[Blue-Green Deployment]
    M --> N[Post-Deploy Verification]
    
    N --> O{Success?}
    O -->|No| P[Automatic Rollback]
    O -->|Yes| Q[Monitoring & Alerts]
```

## Workflows Overview

### 1. CI Pipeline (`ci.yml`)

**Triggers:**
- Pull requests to `main` or `develop`
- Pushes to `develop` branch
- Manual workflow dispatch

**Jobs:**
- **detect-changes**: Smart detection of what changed to optimize pipeline execution
- **code-quality**: ESLint, TypeScript checks, Prettier formatting
- **backend-tests**: Jest unit tests with PostgreSQL and Redis services
- **frontend-tests**: Vitest tests for React components
- **integration-tests**: End-to-end tests with Playwright
- **build-images**: Multi-platform Docker image builds
- **infrastructure-validation**: Terraform and Kubernetes manifest validation
- **security-scan**: Trivy vulnerability scanning
- **performance-test**: Load testing with Artillery

### 2. Production Deployment (`production-deploy.yml`)

**Triggers:**
- Pushes to `main` branch
- Manual workflow dispatch with environment selection

**Jobs:**
- **pre-deploy-validation**: Environment and secrets validation
- **terraform-apply**: Infrastructure provisioning with AWS
- **database-migration**: Automated database schema updates
- **deploy-blue-green**: Zero-downtime deployment strategy
- **post-deploy-verification**: Health checks and smoke tests
- **rollback**: Automatic rollback on failure

### 3. Security Scanning (`security-scan.yml`)

**Triggers:**
- Daily scheduled runs
- Pushes to main branches
- Manual dispatch

**Scans:**
- **SAST**: CodeQL and Semgrep static analysis
- **Dependency Scan**: npm audit, Snyk, OWASP Dependency Check
- **Container Scan**: Trivy, Grype, Dockle for container security
- **Infrastructure Scan**: Checkov, tfsec, Kubesec
- **Secrets Scan**: GitLeaks and TruffleHog
- **DAST**: OWASP ZAP dynamic application testing

### 4. Performance Monitoring (`performance-monitoring.yml`)

**Triggers:**
- Every 6 hours (scheduled)
- Pushes to `main`
- Pull requests with performance impact

**Tests:**
- **Load Testing**: Artillery-based API load testing
- **Browser Performance**: Playwright performance metrics
- **Lighthouse Audit**: Web performance scoring
- **Performance Analysis**: Trend analysis and alerting

### 5. Database Migration (`database-migration.yml`)

**Triggers:**
- Changes to migration files
- Manual workflow dispatch

**Features:**
- **Validation**: Migration file structure and rollback testing
- **Staging Deployment**: Automated staging database updates
- **Production Deployment**: Approval-gated production migrations
- **Backup & Recovery**: Automated database backups
- **Emergency Rollback**: Automatic rollback on failure

### 6. Infrastructure Deployment (`infrastructure-deploy.yml`)

**Triggers:**
- Changes to infrastructure code
- Manual workflow dispatch

**Capabilities:**
- **Terraform Planning**: Infrastructure change preview
- **Terraform Apply**: AWS resource provisioning
- **Kubernetes Deployment**: Application deployment to EKS
- **Monitoring Setup**: Prometheus and Grafana deployment
- **Rollback Support**: Emergency infrastructure rollback

## Environment Strategy

### Development Environment
- **Purpose**: Feature development and initial testing
- **Deployment**: Automatic on feature branch pushes
- **Resources**: Minimal resource allocation
- **Data**: Synthetic test data

### Staging Environment
- **Purpose**: Pre-production testing and QA validation
- **Deployment**: Automatic on `develop` branch
- **Resources**: Production-like but scaled down
- **Data**: Sanitized production data snapshots

### Production Environment
- **Purpose**: Live customer-facing application
- **Deployment**: Approval-gated from `main` branch
- **Resources**: Full production capacity
- **Data**: Live customer data with full compliance

## Secrets Management

### Required Secrets

#### AWS Configuration
- `AWS_ACCESS_KEY_ID`: AWS access credentials
- `AWS_SECRET_ACCESS_KEY`: AWS secret credentials
- `TERRAFORM_STATE_BUCKET`: S3 bucket for Terraform state
- `TERRAFORM_LOCK_TABLE`: DynamoDB table for Terraform locking

#### Database Configuration
- `STAGING_DATABASE_URL`: Staging database connection string
- `PRODUCTION_DATABASE_URL`: Production database connection string

#### Application Secrets
- `STAGING_JWT_SECRET`: JWT signing key for staging
- `PRODUCTION_JWT_SECRET`: JWT signing key for production
- `STAGING_SESSION_SECRET`: Session secret for staging
- `PRODUCTION_SESSION_SECRET`: Session secret for production

#### Kubernetes Configuration
- `KUBE_CONFIG_DATA`: Base64-encoded kubeconfig

#### Security Tools
- `SNYK_TOKEN`: Snyk API token
- `SEMGREP_APP_TOKEN`: Semgrep authentication
- `NVD_API_KEY`: NIST vulnerability database access

#### Notifications
- `SLACK_WEBHOOK`: General notifications
- `SECURITY_SLACK_WEBHOOK`: Security alerts
- `PERFORMANCE_SLACK_WEBHOOK`: Performance alerts
- `DATABASE_SLACK_WEBHOOK`: Database notifications
- `INFRASTRUCTURE_SLACK_WEBHOOK`: Infrastructure notifications

### Setting Up Secrets

Use the provided script to configure all secrets:

```bash
chmod +x .github/scripts/setup-secrets.sh
./.github/scripts/setup-secrets.sh
```

## Deployment Strategies

### Blue-Green Deployment

The production deployment uses a blue-green strategy to ensure zero downtime:

1. **Deploy to Blue Environment**: New version deployed to inactive environment
2. **Smoke Tests**: Automated testing of blue environment
3. **Traffic Switch**: Load balancer switches traffic to blue
4. **Green Cleanup**: Previous green environment is decommissioned

### Canary Deployment (Alternative)

Canary deployment is available as an alternative strategy:

1. **Deploy Canary**: 10% of traffic routed to new version
2. **Monitor Metrics**: Error rates and performance metrics tracked
3. **Progressive Rollout**: Gradual increase of traffic to new version
4. **Full Rollout**: 100% traffic switched after validation

## Rollback Procedures

### Automatic Rollback

Automatic rollback is triggered on:
- Health check failures
- High error rates
- Performance degradation
- Database migration failures

### Manual Rollback

For emergency situations:

```bash
# Via GitHub Actions
gh workflow run production-deploy.yml -f environment=production -f force_deploy=true

# Via kubectl (emergency)
kubectl rollout undo deployment/backend --namespace=ss-platform-production
kubectl rollout undo deployment/frontend --namespace=ss-platform-production
```

### Database Rollback

Database rollbacks are handled through:
- Migration rollback commands
- Point-in-time recovery
- Backup restoration

## Monitoring and Alerting

### Key Metrics

- **Application Performance**: Response times, error rates, throughput
- **Infrastructure Health**: CPU, memory, disk usage
- **Security**: Vulnerability counts, failed authentications
- **Database**: Connection pool, query performance, locks

### Alert Channels

- **Slack**: Real-time notifications
- **Email**: Critical alerts
- **PagerDuty**: On-call escalation (if configured)
- **GitHub Issues**: Automatic issue creation for failures

## Security Considerations

### Container Security
- Base images scanned for vulnerabilities
- Multi-stage builds to minimize attack surface
- Non-root user execution
- Read-only root filesystem where possible

### Secrets Management
- All secrets stored in GitHub Secrets
- Automatic secret rotation (recommended)
- Least-privilege access principles

### Network Security
- Private subnets for application components
- Network policies for pod-to-pod communication
- WAF protection for public endpoints

## Performance Optimization

### Build Optimization
- Docker layer caching
- Multi-platform builds
- Parallel job execution
- Artifact caching

### Runtime Optimization
- Horizontal Pod Autoscaling
- Vertical Pod Autoscaling (if enabled)
- CDN for static assets
- Database connection pooling

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check dependency versions
   - Verify environment variables
   - Review build logs in GitHub Actions

2. **Deployment Failures**
   - Validate Kubernetes manifests
   - Check resource quotas
   - Verify secrets and ConfigMaps

3. **Test Failures**
   - Review test logs
   - Check database connectivity
   - Verify test data setup

### Debug Commands

```bash
# Check workflow status
gh run list --workflow=ci.yml

# View workflow logs
gh run view <run-id> --log

# Check Kubernetes resources
kubectl get pods --namespace=ss-platform-production
kubectl describe deployment backend --namespace=ss-platform-production

# Check application logs
kubectl logs deployment/backend --namespace=ss-platform-production
```

## Best Practices

### Code Quality
- All code must pass linting and type checking
- Minimum test coverage requirements
- Code review required for all changes

### Security
- Regular security scans
- Dependency updates
- Secrets rotation
- Access auditing

### Performance
- Performance budgets for key metrics
- Regular load testing
- Monitoring and alerting setup

## Maintenance

### Regular Tasks
- Update base images monthly
- Rotate secrets quarterly
- Review and update monitoring quarterly
- Disaster recovery testing biannually

### Upgrades
- Node.js version updates
- Kubernetes cluster upgrades
- Tool version updates (Terraform, kubectl, etc.)

## Support

For issues with the CI/CD pipeline:

1. **Check Documentation**: Review this guide and workflow files
2. **GitHub Issues**: Create deployment issue using template
3. **Slack Channels**: Use appropriate alert channels
4. **On-Call**: Contact on-call engineer for critical production issues

## Additional Resources

- [Infrastructure Documentation](../infrastructure/)
- [Security Guidelines](../security/)
- [Monitoring Setup](../monitoring/)
- [Emergency Procedures](../emergency-procedures.md)