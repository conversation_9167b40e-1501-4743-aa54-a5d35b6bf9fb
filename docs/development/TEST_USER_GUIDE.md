# Development Test Users Guide

This guide explains how to quickly set up and use test users for development and testing purposes.

## 🚀 Quick Start

### Option 1: Development Login Bypass (Fastest)

Use the development-only API endpoint to instantly log in:

```bash
curl -X POST http://localhost:3000/api/auth/dev/test-login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

**Frontend Integration:**
```javascript
const devLogin = async (email = '<EMAIL>') => {
  const response = await fetch('/api/auth/dev/test-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  });
  const data = await response.json();
  localStorage.setItem('accessToken', data.data.accessToken);
  localStorage.setItem('user', JSON.stringify(data.data.user));
  window.location.reload();
};
```

### Option 2: Create Test Users in Database

Run the setup script to create all test users:

```bash
cd backend
npm run setup:dev-users
```

Or use the API endpoint:
```bash
curl -X POST http://localhost:3000/api/auth/dev/create-test-users
```

### Option 3: Use Development UI Component

Add the DevPanel to any React component:

```jsx
import { DevPanel } from '@/components/dev/DevPanel';

function App() {
  return (
    <div>
      {/* Your app content */}
      <DevPanel position="bottom-right" />
    </div>
  );
}
```

## 👥 Available Test Users

| Email | Password | Role | Description |
|-------|----------|------|-------------|
| `<EMAIL>` | `TestPassword123!` | user | Default test user (created automatically) |
| `<EMAIL>` | `AdminPassword123!` | admin | Administrator with full access |
| `<EMAIL>` | `UserPassword123!` | user | John Doe from Acme Corp |
| `<EMAIL>` | `UserPassword123!` | user | Jane Smith from TechCorp |
| `<EMAIL>` | `PremiumPassword123!` | premium | Premium subscriber |
| `<EMAIL>` | `DevPassword123!` | developer | Internal developer account |

## 🛠️ Development Endpoints

**⚠️ These endpoints are automatically disabled in production!**

### Test Login Bypass
- **POST** `/api/auth/dev/test-login`
- **Body:** `{ "email": "<EMAIL>" }` (optional, <NAME_EMAIL>)
- **Response:** Full login response with tokens

### Create All Test Users  
- **POST** `/api/auth/dev/create-test-users`
- **Body:** None required
- **Response:** List of created users

## 🎨 Frontend Components

### DevPanel Component
A floating development panel that provides:
- Quick login buttons for all test users
- User status display
- Development tools (clear storage, reload, etc.)
- API inspection tools
- Environment information

**Props:**
- `position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'`
- `minimized?: boolean`

### DevLoginButton Component
A standalone login component for development:
- Quick login with default user
- User selection dropdown  
- Test user creation
- API endpoint information

**Props:**
- `onLogin?: (user: any) => void`
- `className?: string`

## 🔧 Backend Scripts

### Setup Development Users
```bash
npm run setup:dev-users
```
Creates all test users in the database with proper hashing.

### Database Operations
```bash
# Run all seeders (including test users)
npm run db:seed

# Reset database and create test users
npm run db:reset

# Create migration for user table
npm run db:make-migration create_users_table

# Create new seeder
npm run db:make-seed test_users
```

## 📝 Implementation Details

### Security Features
- **Production Safety**: All development endpoints return 403 in production
- **Environment Checks**: Components only render in development mode  
- **Proper Password Hashing**: Uses bcrypt for all test user passwords
- **JWT Tokens**: Generate real JWT tokens for authentication testing

### Database Structure
Test users are created with:
- Verified email addresses
- Proper password hashing
- Realistic profile data
- Default preferences
- Consistent UUIDs for easy testing

### Error Handling
- Graceful handling of existing users (skips creation)
- Detailed error messages for debugging
- Console logging for development visibility
- User-friendly error alerts in UI

## 🚨 Security Notes

1. **Production Safety**: Development endpoints are completely disabled in production
2. **Environment Variables**: Ensure `NODE_ENV=development` for local development
3. **Test Data**: All test users use obviously fake data
4. **Password Requirements**: Test passwords meet production requirements
5. **Token Security**: Uses same JWT implementation as production

## 🎯 Common Use Cases

### Frontend Development
```jsx
// Add to login page for quick testing
<DevLoginButton className="mt-4" />

// Add floating panel for any page
<DevPanel position="bottom-right" minimized />
```

### API Testing
```bash
# Get a token quickly
TOKEN=$(curl -s -X POST http://localhost:3000/api/auth/dev/test-login | jq -r '.data.accessToken')

# Use token for API calls
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/api/user/profile
```

### Integration Testing
```javascript
// In Jest tests
const loginResponse = await request(app)
  .post('/api/auth/dev/test-login')
  .send({ email: '<EMAIL>' });

const token = loginResponse.body.data.accessToken;

// Use token for authenticated requests
await request(app)
  .get('/api/admin/users')
  .set('Authorization', `Bearer ${token}`);
```

### Development Workflow
1. Start backend: `npm run dev`
2. Run `npm run setup:dev-users` (once)
3. Add `<DevPanel />` to frontend
4. Use quick login for instant authentication
5. Test features with different user roles

## 📊 Troubleshooting

### Common Issues

**"Development endpoints not available in production"**
- Check `NODE_ENV` environment variable
- Ensure not running in production mode

**"User already exists"**
- This is expected behavior - existing users are skipped
- Use the login bypass instead of creating new users

**"Database connection error"**
- Ensure PostgreSQL is running
- Check database configuration in .env
- Run database migrations first

**"Token not working"**
- Check token expiration (default: 1 hour)
- Verify token is stored correctly in localStorage
- Check for CORS issues in browser console

### Debug Commands
```bash
# Check database users
psql -d ai_services_db -c "SELECT email, role, status FROM users;"

# View server logs
npm run dev 2>&1 | grep -i "auth\|login\|token"

# Test API directly
curl -v http://localhost:3000/api/auth/dev/test-login
```

## 🔄 Updates & Maintenance

### Adding New Test Users
1. Update `testUsers` array in `dev_test_users.ts`
2. Add to frontend `DevLoginButton` component
3. Run setup script to create in database
4. Update this documentation

### Modifying User Data
1. Edit seeder file: `backend/src/database/seeds/dev_test_users.ts`
2. Clear existing users if needed
3. Run setup script to recreate

### Environment Configuration
Ensure these environment variables are set for development:
```bash
NODE_ENV=development
JWT_SECRET=your_jwt_secret
DATABASE_URL=postgresql://user:pass@localhost:5432/ai_services_db
```

---

**Remember**: These development features are for convenience during development and testing. Always use proper authentication flows in production!