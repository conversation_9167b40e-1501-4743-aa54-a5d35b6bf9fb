# Voice Agent Implementation Guide

## Quick Start Implementation

### Step 1: Backend Service Creation

Create a new Pipecat voice service alongside your existing Intervo service:

```typescript
// backend/src/services/pipecatVoiceService.ts
import { BaseService, BaseServiceConfig } from './baseService';
import { ServiceType, ServiceRequest, ServiceResponse } from '../../../shared/src/types/service';
import { WebSocketServer } from 'ws';
import fetch from 'node-fetch';

export interface PipecatVoiceRequest extends ServiceRequest {
  operation: 'start_consultation' | 'process_voice' | 'extract_requirements' | 'end_consultation';
  data: {
    sessionId?: string;
    userId?: string;
    audioStream?: Buffer;
    transcript?: string;
    metadata?: {
      language?: string;
      timezone?: string;
      previousInteractions?: number;
    };
  };
}

export class PipecatVoiceService extends BaseService {
  private pipecatUrl: string;
  private wsServer: WebSocketServer;
  
  constructor(config: BaseServiceConfig) {
    super(ServiceType.VOICE_CONSULTANT, {
      ...config,
      baseURL: process.env.PIPECAT_API_URL || 'http://localhost:8765',
      rateLimits: {
        requestsPerMinute: 30,
        requestsPerHour: 500,
        ...config.rateLimits,
      },
    });
    
    this.pipecatUrl = this.config.baseURL;
    this.initializeWebSocketServer();
  }
  
  private initializeWebSocketServer() {
    this.wsServer = new WebSocketServer({ port: 8766 });
    
    this.wsServer.on('connection', (ws, req) => {
      const sessionId = new URL(req.url!, `http://${req.headers.host}`).searchParams.get('session');
      
      ws.on('message', async (data) => {
        // Forward audio to Pipecat
        const response = await this.forwardToPipecat(sessionId!, data);
        ws.send(response);
      });
    });
  }
  
  private async forwardToPipecat(sessionId: string, audioData: any) {
    const response = await fetch(`${this.pipecatUrl}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
        'X-Session-Id': sessionId,
      },
      body: audioData,
    });
    
    return await response.arrayBuffer();
  }
}
```

### Step 2: Python Pipecat Service

Create the Python service that handles the actual voice processing:

```python
# services/pipecat_service/main.py
from fastapi import FastAPI, WebSocket, HTTPException
from pipecat.pipeline import Pipeline
from pipecat.processors.aggregators import LLMUserResponseAggregator
from pipecat.services.nvidia import NVIDIARivaASRService, NVIDIARivaTTSService
from pipecat.services.openai import OpenAILLMService
from pipecat.frames import Frame, AudioRawFrame, TextFrame
import os
import asyncio
from typing import Dict, Any

app = FastAPI()

class ConsultationPipeline:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.requirements = {}
        self.conversation_history = []
        
        # Initialize NVIDIA services
        self.asr = NVIDIARivaASRService(
            api_key=os.getenv("NVIDIA_API_KEY"),
            server_url=os.getenv("RIVA_SERVER_URL", "localhost:50051"),
            language_code="en-US",
            sample_rate=16000,
            enable_automatic_punctuation=True
        )
        
        self.tts = NVIDIARivaTTSService(
            api_key=os.getenv("NVIDIA_API_KEY"),
            server_url=os.getenv("RIVA_SERVER_URL", "localhost:50051"),
            voice_name="English-US.Female-1",
            sample_rate=22050,
            language_code="en-US"
        )
        
        # Initialize LLM with custom prompt
        self.llm = OpenAILLMService(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-4-turbo",
            system_prompt=self.get_consultation_prompt()
        )
        
        # Build pipeline
        self.pipeline = Pipeline([
            self.asr,
            LLMUserResponseAggregator(),
            self.llm,
            self.tts
        ])
    
    def get_consultation_prompt(self) -> str:
        return """
        You are an AI consultant for a platform offering 8 AI services:
        1. Velian - AI Analytics
        2. ZeroEntropy - Data Processing 
        3. Hello.cv - Resume Analysis
        4. YoinkUI - UI Generation
        5. Clueso - Search & Discovery
        6. Permut - Optimization
        7. Intervo - Chat Agents
        8. Pixelesq - Image Processing
        
        Your role is to:
        - Understand the client's business needs
        - Ask probing questions to clarify requirements
        - Recommend appropriate services
        - Extract structured requirements
        
        Be conversational, professional, and helpful.
        Focus on understanding their pain points and goals.
        """
    
    async def process_audio(self, audio_data: bytes) -> bytes:
        # Process through pipeline
        audio_frame = AudioRawFrame(audio_data, self.asr.sample_rate)
        
        async for frame in self.pipeline.process_frame(audio_frame):
            if isinstance(frame, AudioRawFrame):
                return frame.audio
            elif isinstance(frame, TextFrame):
                # Store conversation
                self.conversation_history.append({
                    'role': 'assistant',
                    'content': frame.text
                })
                # Extract requirements in parallel
                asyncio.create_task(self.extract_requirements(frame.text))
        
        return b''
    
    async def extract_requirements(self, text: str):
        # Use LLM to extract structured requirements
        extraction_prompt = f"""
        From this conversation excerpt, extract any mentioned requirements:
        "{text}"
        
        Categories to identify:
        - Industry/Domain
        - Company Size
        - Technical Requirements
        - Budget Range
        - Timeline
        - Specific Problems
        - Desired Outcomes
        
        Return as JSON.
        """
        
        # Process extraction
        # Store in self.requirements
        pass

# Session management
sessions: Dict[str, ConsultationPipeline] = {}

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()
    
    # Create or get session
    if session_id not in sessions:
        sessions[session_id] = ConsultationPipeline(session_id)
    
    pipeline = sessions[session_id]
    
    try:
        while True:
            # Receive audio data
            audio_data = await websocket.receive_bytes()
            
            # Process through pipeline
            response_audio = await pipeline.process_audio(audio_data)
            
            # Send response
            if response_audio:
                await websocket.send_bytes(response_audio)
    
    except Exception as e:
        print(f"Error in session {session_id}: {e}")
    finally:
        await websocket.close()

@app.get("/sessions/{session_id}/requirements")
async def get_requirements(session_id: str):
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "requirements": sessions[session_id].requirements,
        "conversation_length": len(sessions[session_id].conversation_history),
        "recommended_services": analyze_requirements(sessions[session_id].requirements)
    }

def analyze_requirements(requirements: dict) -> list:
    # Map requirements to services
    recommendations = []
    
    if requirements.get("needs_data_processing"):
        recommendations.append("zeroentropy")
    if requirements.get("needs_analytics"):
        recommendations.append("velian")
    if requirements.get("needs_ui"):
        recommendations.append("yoinkui")
    # ... etc
    
    return recommendations

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8765)
```

### Step 3: Docker Compose Configuration

```yaml
# docker-compose.voice.yml
version: '3.8'

services:
  riva-speech:
    image: nvcr.io/nvidia/riva/riva-speech:2.14.0
    runtime: nvidia
    ports:
      - "50051:50051"
    volumes:
      - ./riva-models:/data
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
    command: >
      bash -c "
      riva_start.sh
      --riva_model_loc=/data/models
      --riva_asr_enable=true
      --riva_tts_enable=true
      "
  
  pipecat-service:
    build: ./services/pipecat_service
    ports:
      - "8765:8765"
      - "8766:8766"
    environment:
      - NVIDIA_API_KEY=${NVIDIA_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - RIVA_SERVER_URL=riva-speech:50051
    depends_on:
      - riva-speech
    volumes:
      - ./knowledge_base:/app/knowledge_base
  
  redis-vector:
    image: redis/redis-stack:latest
    ports:
      - "6380:6379"
    volumes:
      - redis-vector-data:/data
```

### Step 4: Frontend Voice Component

```tsx
// frontend/src/components/VoiceConsultant/VoiceConsultant.tsx
import React, { useState, useEffect, useRef } from 'react';
import { Canvas } from '@react-three/fiber';
import { ParticleFace } from './ParticleFace';
import { useVoiceSession } from '../../hooks/useVoiceSession';

export const VoiceConsultant: React.FC = () => {
  const {
    startSession,
    endSession,
    isActive,
    transcript,
    audioLevel,
    requirements
  } = useVoiceSession();
  
  const [isListening, setIsListening] = useState(false);
  
  const handleStartConsultation = async () => {
    await startSession();
    setIsListening(true);
  };
  
  return (
    <div className="voice-consultant-container">
      <div className="voice-avatar">
        <Canvas camera={{ position: [0, 0, 5] }}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} />
          <ParticleFace 
            audioLevel={audioLevel}
            isActive={isActive}
          />
        </Canvas>
      </div>
      
      <div className="consultation-controls">
        {!isListening ? (
          <button 
            onClick={handleStartConsultation}
            className="start-consultation-btn"
          >
            Start AI Consultation
          </button>
        ) : (
          <button 
            onClick={endSession}
            className="end-consultation-btn"
          >
            End Consultation
          </button>
        )}
      </div>
      
      <div className="transcript-display">
        <h3>Conversation</h3>
        <div className="transcript-content">
          {transcript.map((entry, i) => (
            <div key={i} className={`message ${entry.role}`}>
              <span className="role">{entry.role}:</span>
              <span className="content">{entry.content}</span>
            </div>
          ))}
        </div>
      </div>
      
      {requirements && (
        <div className="requirements-summary">
          <h3>Extracted Requirements</h3>
          <pre>{JSON.stringify(requirements, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};
```

### Step 5: Voice Session Hook

```typescript
// frontend/src/hooks/useVoiceSession.ts
import { useState, useEffect, useRef } from 'react';

export const useVoiceSession = () => {
  const [isActive, setIsActive] = useState(false);
  const [transcript, setTranscript] = useState<Array<{role: string, content: string}>>([]);
  const [audioLevel, setAudioLevel] = useState(0);
  const [requirements, setRequirements] = useState(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  
  const startSession = async () => {
    // Get user media
    mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      } 
    });
    
    // Create audio context
    audioContextRef.current = new AudioContext();
    const source = audioContextRef.current.createMediaStreamSource(mediaStreamRef.current);
    const processor = audioContextRef.current.createScriptProcessor(4096, 1, 1);
    
    // Connect WebSocket
    const sessionId = generateSessionId();
    wsRef.current = new WebSocket(`ws://localhost:8766?session=${sessionId}`);
    
    wsRef.current.onopen = () => {
      setIsActive(true);
    };
    
    wsRef.current.onmessage = async (event) => {
      // Play response audio
      const audioData = await event.data.arrayBuffer();
      const audioBuffer = await audioContextRef.current!.decodeAudioData(audioData);
      const source = audioContextRef.current!.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current!.destination);
      source.start();
    };
    
    // Process microphone input
    processor.onaudioprocess = (e) => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        const inputData = e.inputBuffer.getChannelData(0);
        const buffer = new ArrayBuffer(inputData.length * 2);
        const view = new Int16Array(buffer);
        
        for (let i = 0; i < inputData.length; i++) {
          view[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
        }
        
        wsRef.current.send(buffer);
        
        // Calculate audio level
        const level = Math.sqrt(inputData.reduce((sum, val) => sum + val * val, 0) / inputData.length);
        setAudioLevel(level);
      }
    };
    
    source.connect(processor);
    processor.connect(audioContextRef.current.destination);
  };
  
  const endSession = () => {
    wsRef.current?.close();
    mediaStreamRef.current?.getTracks().forEach(track => track.stop());
    audioContextRef.current?.close();
    setIsActive(false);
  };
  
  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };
  
  return {
    startSession,
    endSession,
    isActive,
    transcript,
    audioLevel,
    requirements
  };
};
```

## Deployment Instructions

### 1. Environment Variables

```bash
# .env
NVIDIA_API_KEY=nvapi-xxx
OPENAI_API_KEY=sk-xxx
RIVA_SERVER_URL=localhost:50051
PIPECAT_API_URL=http://localhost:8765
DAILY_API_KEY=xxx  # For WebRTC if using Daily.co
```

### 2. Install Dependencies

```bash
# Backend Node.js
cd backend
npm install ws node-fetch

# Python service
cd services/pipecat_service
pip install pipecat-ai fastapi uvicorn nvidia-riva-client openai

# Frontend
cd frontend
npm install @react-three/fiber three
```

### 3. Start Services

```bash
# Start NVIDIA Riva (requires GPU)
docker-compose -f docker-compose.voice.yml up riva-speech

# Start Pipecat service
python services/pipecat_service/main.py

# Start main backend
npm run dev:backend

# Start frontend
npm run dev:frontend
```

## Testing the Integration

### 1. Health Check
```bash
# Check Riva
curl http://localhost:50051/health

# Check Pipecat
curl http://localhost:8765/health

# Check WebSocket
wscat -c ws://localhost:8766?session=test
```

### 2. Test Consultation Flow
1. Navigate to `/consultation` in your app
2. Click "Start AI Consultation"
3. Allow microphone access
4. Speak: "I need help processing large datasets and generating reports"
5. Observe the particle face animation
6. Check extracted requirements

## Performance Monitoring

```typescript
// backend/src/monitoring/voiceMetrics.ts
export class VoiceMetrics {
  static track(metric: string, value: number) {
    // Send to Prometheus
    prometheus.metrics.gauge(metric, value);
    
    // Log for debugging
    console.log(`[Voice Metric] ${metric}: ${value}ms`);
  }
  
  static trackLatency(session: string, latency: number) {
    this.track(`voice.latency.${session}`, latency);
    
    if (latency > 500) {
      console.warn(`High latency detected: ${latency}ms`);
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **GPU Not Available**
   ```bash
   # Check NVIDIA driver
   nvidia-smi
   
   # Install NVIDIA Container Toolkit
   distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
   curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
   ```

2. **Audio Not Working**
   - Check browser permissions
   - Verify WebRTC ICE servers
   - Test with different sample rates

3. **High Latency**
   - Use TensorRT optimization
   - Enable GPU acceleration
   - Reduce model size

## Next Steps

1. **Train Custom Models**: Fine-tune on your service documentation
2. **Add Multi-language**: Support more languages with Riva
3. **Implement Analytics**: Track consultation metrics
4. **A/B Testing**: Compare with existing flow
5. **Scale Infrastructure**: Add load balancing and redundancy