{"hash": "d6561e49", "configHash": "de7f49e8", "lockfileHash": "6086158e", "browserHash": "1201599e", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "18819500", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "eae87b3e", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "b822b73e", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f44b9eff", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "99c455f4", "needsInterop": true}, "@headlessui/react": {"src": "../../../../node_modules/@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "e4135a31", "needsInterop": false}, "@heroicons/react/20/solid": {"src": "../../../../node_modules/@heroicons/react/20/solid/esm/index.js", "file": "@heroicons_react_20_solid.js", "fileHash": "972f1d0c", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../../../node_modules/@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "0891a1fa", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../../../node_modules/@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "55144390", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "5b1127ba", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "60f18324", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0dc66478", "needsInterop": false}, "framer-motion": {"src": "../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "05a55caa", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b3c5d2dc", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "50f68b84", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "336f20b3", "needsInterop": false}, "reactflow": {"src": "../../../../node_modules/reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "8d1d3886", "needsInterop": false}, "socket.io-client": {"src": "../../../../node_modules/socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "8e15d0a2", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "5c40556b", "needsInterop": false}, "zod": {"src": "../../../../node_modules/zod/index.js", "file": "zod.js", "fileHash": "f4725dc7", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "f24202b0", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "9974541d", "needsInterop": false}}, "chunks": {"chunk-43WDTZ6H": {"file": "chunk-43WDTZ6H.js"}, "chunk-PKJM3SIU": {"file": "chunk-PKJM3SIU.js"}, "chunk-AUYUPDD5": {"file": "chunk-AUYUPDD5.js"}, "chunk-J4UD52JM": {"file": "chunk-J4UD52JM.js"}, "chunk-X5JZSKBF": {"file": "chunk-X5JZSKBF.js"}, "chunk-HXA6O6EE": {"file": "chunk-HXA6O6EE.js"}}}