import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { DevLoginButton } from '../dev/DevLoginButton';

/**
 * Example Login Page showing how to integrate development test users
 * This is an example file showing best practices
 */

export const LoginPageExample: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Regular login logic here
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        localStorage.setItem('accessToken', data.data.accessToken);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        window.location.href = '/dashboard';
      } else {
        alert(data.message);
      }
    } catch (error) {
      console.error('Login failed:', error);
      alert('Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8">
        {/* Main Login Card */}
        <Card className="p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Sign in to AI Services
            </h2>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Access your AI-powered platform
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email address
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="mt-1"
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          {/* Forgot Password Link */}
          <div className="mt-6 text-center">
            <a
              href="/auth/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400"
            >
              Forgot your password?
            </a>
          </div>

          {/* Development-only: Test Login Section */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <DevLoginButton 
                onLogin={(userData) => {
                  // Custom login handler - redirects to dashboard
                  window.location.href = '/dashboard';
                }}
                className="w-full"
              />
            </div>
          )}
        </Card>

        {/* Development Information */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
              🛠️ Development Mode
            </h3>
            <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <p>• Use the buttons above for quick login</p>
              <p>• Or try: <EMAIL> / AdminPassword123!</p>
              <p>• All test users have verified emails</p>
              <p>• Check console for login details</p>
            </div>
          </Card>
        )}

        {/* Sign Up Link */}
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?{' '}
            <a
              href="/auth/register"
              className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
            >
              Sign up for free
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

// Usage example in main App:
/*
import { DevPanel } from '@/components/dev';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<LoginPageExample />} />
        {/* other routes *//*}
      </Routes>
      
      {/* Add floating dev panel to any page *//*}
      <DevPanel position="bottom-right" minimized />
    </Router>
  );
}
*/