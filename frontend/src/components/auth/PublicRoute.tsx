import React from 'react';
import { Navigate } from 'react-router-dom';
import { authUtils } from '@/utils/auth';

interface PublicRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = '/dashboard',
}) => {
  // Use simple auth utils
  const isAuthenticated = authUtils.isAuthenticated();
  const user = authUtils.getUser();
  
  console.log('🔓 PublicRoute Check:', {
    isAuthenticated,
    redirectTo,
    userEmail: user?.email,
    hasToken: !!authUtils.getToken()
  });
  
  // Redirect to dashboard if already authenticated
  if (isAuthenticated) {
    console.log('✅ User already authenticated, redirecting to:', redirectTo);
    return <Navigate to={redirectTo} replace />;
  }
  
  console.log('👤 User not authenticated, showing public content');
  return <>{children}</>;
};