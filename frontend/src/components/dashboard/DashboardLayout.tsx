/**
 * Dashboard Layout Component - Architectural Foundation
 * Phase 1: Component Composition Structure
 * 
 * Main layout component that orchestrates all dashboard elements
 * with proper composition and separation of concerns
 */

import React from 'react';
import type { AuthenticatedUser } from '@/types/dashboard';

// ==================== LAYOUT COMPONENT INTERFACES ====================

interface DashboardLayoutProps {
  readonly user: AuthenticatedUser;
  readonly children: React.ReactNode;
  readonly sidebar?: React.ReactNode;
  readonly header?: React.ReactNode;
  readonly className?: string;
}

interface DashboardHeaderProps {
  readonly user: AuthenticatedUser;
  readonly onRefresh?: () => void;
  readonly onNotificationClick?: () => void;
  readonly onSettingsClick?: () => void;
  readonly lastRefresh?: Date;
  readonly realTimeStatus?: boolean;
}

interface DashboardSidebarProps {
  readonly isOpen: boolean;
  readonly onToggle: () => void;
  readonly currentPath: string;
}

interface DashboardMainProps {
  readonly children: React.ReactNode;
  readonly isLoading?: boolean;
  readonly error?: string;
}

// ==================== MAIN LAYOUT COMPONENT ====================

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  user,
  children,
  sidebar,
  header,
  className = ''
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden ${className}`}>
      {/* Aurora Background Layer - Managed by Glassmorphism Designer */}
      <div className="aurora-background">
        <div className="aurora-blob aurora-blob-1" />
        <div className="aurora-blob aurora-blob-2" />
        <div className="aurora-blob aurora-blob-3" />
      </div>

      {/* Main Layout Structure */}
      <div className="relative z-10 min-h-screen flex">
        {/* Sidebar - Optional */}
        {sidebar && (
          <aside className="sidebar-container">
            {sidebar}
          </aside>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Header - Optional */}
          {header && (
            <header className="dashboard-header">
              {header}
            </header>
          )}

          {/* Main Content */}
          <main className="flex-1 p-4 sm:p-6 lg:p-8">
            <div className="dashboard-container max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

// ==================== DASHBOARD HEADER COMPONENT ====================

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  user,
  onRefresh,
  onNotificationClick,
  onSettingsClick,
  lastRefresh,
  realTimeStatus = false
}) => {
  const formatLastRefresh = () => {
    if (!lastRefresh) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - lastRefresh.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (seconds < 60) return `${seconds}s ago`;
    if (minutes < 60) return `${minutes}m ago`;
    return lastRefresh.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="dashboard-header-content bg-white/5 backdrop-blur-md border-b border-white/10 px-4 sm:px-6 lg:px-8 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Welcome Section */}
        <div className="dashboard-welcome">
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-white/60 text-sm sm:text-base">
            Your AI services dashboard
          </p>
        </div>

        {/* Header Actions */}
        <div className="dashboard-actions flex items-center gap-3 sm:gap-4">
          {/* Real-time Status Indicator */}
          <div className="status-indicator flex items-center gap-2 text-sm text-white/70">
            <div className={`w-2 h-2 rounded-full ${realTimeStatus ? 'bg-green-400' : 'bg-yellow-400'}`} />
            <span className="hidden sm:inline">
              {realTimeStatus ? 'Live' : 'Polling'}
            </span>
          </div>

          {/* Last Refresh */}
          <div className="last-refresh text-xs text-white/50 hidden sm:block">
            Updated {formatLastRefresh()}
          </div>

          {/* Action Buttons */}
          <div className="action-buttons flex items-center gap-2">
            {/* Refresh Button */}
            <button
              onClick={onRefresh}
              className="action-btn p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
              aria-label="Refresh dashboard"
            >
              <RefreshIcon className="w-4 h-4 text-white/80" />
            </button>

            {/* Notifications */}
            <button
              onClick={onNotificationClick}
              className="action-btn p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200 relative"
              aria-label="View notifications"
            >
              <BellIcon className="w-4 h-4 text-white/80" />
              {/* Notification badge could go here */}
            </button>

            {/* Settings */}
            <button
              onClick={onSettingsClick}
              className="action-btn p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
              aria-label="Open settings"
            >
              <CogIcon className="w-4 h-4 text-white/80" />
            </button>

            {/* User Avatar */}
            <div className="user-avatar ml-2 sm:ml-4">
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-violet-400 to-purple-400 flex items-center justify-center text-white font-semibold text-sm sm:text-base">
                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// ==================== DASHBOARD MAIN COMPONENT ====================

export const DashboardMain: React.FC<DashboardMainProps> = ({
  children,
  isLoading = false,
  error
}) => {
  if (error) {
    return (
      <div className="dashboard-error min-h-96 flex items-center justify-center">
        <div className="error-content bg-red-500/10 border border-red-500/20 rounded-xl p-6 max-w-md mx-auto text-center">
          <div className="error-icon mb-4">
            <ExclamationIcon className="w-12 h-12 text-red-400 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Dashboard Error
          </h3>
          <p className="text-white/70 text-sm mb-4">
            {error}
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="retry-btn bg-red-500/20 hover:bg-red-500/30 text-red-300 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="dashboard-loading min-h-96 flex items-center justify-center">
        <div className="loading-content text-center">
          <div className="loading-spinner w-8 h-8 border-2 border-white/20 border-t-white/80 rounded-full animate-spin mx-auto mb-4" />
          <p className="text-white/60">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-main">
      {children}
    </div>
  );
};

// ==================== UTILITY COMPONENTS ====================

// Simple icon components (would typically import from icon library)
const RefreshIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const BellIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
  </svg>
);

const CogIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const ExclamationIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.866-.833-2.634 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
  </svg>
);

// ==================== STYLE UTILITIES ====================

/**
 * CSS classes for consistent layout styling
 * These will be enhanced by the TailwindCSS Glassmorphism Designer
 */
export const dashboardStyles = {
  // Aurora background (to be implemented by Glassmorphism Designer)
  auroraBackground: 'aurora-background absolute inset-0 pointer-events-none z-0',
  auroraBlob: 'aurora-blob absolute rounded-full opacity-70 animate-pulse',
  
  // Glass effects (to be implemented by Glassmorphism Designer)  
  glassCard: 'bg-white/5 backdrop-blur-md border border-white/10',
  glassCardHover: 'hover:bg-white/10 hover:border-white/20',
  
  // Layout containers
  dashboardContainer: 'dashboard-container space-y-6 sm:space-y-8',
  headerContainer: 'dashboard-header-content',
  mainContainer: 'dashboard-main space-y-6',
  
  // Responsive grid (to be implemented by Responsive Layout Specialist)
  servicesGrid: 'services-grid grid gap-4 sm:gap-6',
  serviceCard: 'service-card relative overflow-hidden rounded-xl',
  
  // Animation classes (to be implemented by Motion Specialist)
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  floatingCard: 'animate-floating-card'
} as const;

export default DashboardLayout;