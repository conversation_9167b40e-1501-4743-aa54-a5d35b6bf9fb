import React from 'react';
import {
  GlassmorphismCard,
  AnimatedButton,
  GradientText,
  ParticleBackground,
  GlowEffect,
  <PERSON>mmerEffect,
  NeonBorder,
  FadeInUp,
  TypewriterEffect,
  MagicCard,
  AnimatedGradient,
  HoverEffect,
  MorphingButton,
  SparklesCore,
  TextGenerateEffect
} from '@/components/ui';

export default function ModernUIDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8 relative overflow-hidden">
      {/* Background Effects */}
      <ParticleBackground count={30} color="#3B82F6" />
      <SparklesCore 
        className="absolute inset-0"
        particleDensity={40}
        particleColor="#ffffff"
      />
      
      <div className="relative z-10 max-w-6xl mx-auto space-y-12">
        {/* Header */}
        <FadeInUp className="text-center space-y-4">
          <GradientText 
            gradient="rainbow" 
            className="text-6xl font-bold"
          >
            Modern UI Library
          </GradientText>
          <TextGenerateEffect 
            words="Stunning animations, 3D effects, and glassmorphism designs"
            className="text-xl text-gray-300"
          />
        </FadeInUp>

        {/* Demo Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Glassmorphism Card */}
          <FadeInUp delay={0.2}>
            <GlassmorphismCard className="h-64 flex flex-col justify-center items-center space-y-4">
              <GradientText gradient="blue" className="text-2xl font-bold">
                Glassmorphism
              </GradientText>
              <p className="text-gray-300 text-center">
                Beautiful glass effect with backdrop blur and transparency
              </p>
              <AnimatedButton variant="glow">
                Explore
              </AnimatedButton>
            </GlassmorphismCard>
          </FadeInUp>

          {/* Magic Card */}
          <FadeInUp delay={0.4}>
            <MagicCard gradient="cosmic" className="h-64">
              <div className="flex flex-col justify-center items-center h-full space-y-4">
                <GradientText gradient="purple" className="text-2xl font-bold">
                  Magic Card
                </GradientText>
                <p className="text-gray-300 text-center">
                  Animated gradient borders with spotlight effects
                </p>
                <MorphingButton 
                  variant="gradient"
                  hoverContent="✨ Amazing!"
                >
                  Hover Me
                </MorphingButton>
              </div>
            </MagicCard>
          </FadeInUp>

          {/* Glow Effect */}
          <FadeInUp delay={0.6}>
            <GlowEffect color="purple" intensity="high" pulse>
              <div className="bg-gray-900/50 p-8 rounded-xl h-64 flex flex-col justify-center items-center space-y-4">
                <GradientText gradient="pink" className="text-2xl font-bold">
                  Glow Effect
                </GradientText>
                <p className="text-gray-300 text-center">
                  Pulsing glow animations with color variants
                </p>
              </div>
            </GlowEffect>
          </FadeInUp>

          {/* Shimmer Effect */}
          <FadeInUp delay={0.8}>
            <ShimmerEffect className="h-64">
              <div className="bg-gray-900/50 p-8 rounded-xl h-full flex flex-col justify-center items-center space-y-4">
                <GradientText gradient="green" className="text-2xl font-bold">
                  Shimmer Effect
                </GradientText>
                <p className="text-gray-300 text-center">
                  Elegant shimmer animations across surfaces
                </p>
              </div>
            </ShimmerEffect>
          </FadeInUp>

          {/* Neon Border */}
          <FadeInUp delay={1.0}>
            <NeonBorder color="cyan" width={3} rounded="xl">
              <div className="bg-gray-900/50 p-8 h-64 flex flex-col justify-center items-center space-y-4">
                <GradientText gradient="blue" className="text-2xl font-bold">
                  Neon Border
                </GradientText>
                <p className="text-gray-300 text-center">
                  Glowing animated borders in multiple colors
                </p>
              </div>
            </NeonBorder>
          </FadeInUp>

          {/* Hover Effects */}
          <FadeInUp delay={1.2}>
            <HoverEffect effect="tilt" intensity="strong">
              <div className="bg-gray-900/50 p-8 rounded-xl h-64 flex flex-col justify-center items-center space-y-4">
                <GradientText gradient="orange" className="text-2xl font-bold">
                  Hover Effects
                </GradientText>
                <p className="text-gray-300 text-center">
                  Interactive hover animations with tilt, lift, and scale
                </p>
              </div>
            </HoverEffect>
          </FadeInUp>
        </div>

        {/* Animated Gradient Section */}
        <FadeInUp delay={1.4}>
          <AnimatedGradient 
            colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
            speed="normal"
            className="rounded-2xl p-1"
          >
            <div className="bg-gray-900 rounded-2xl p-8 text-center">
              <TypewriterEffect 
                text="Welcome to the future of web interfaces"
                className="text-2xl font-bold text-white"
                speed={100}
              />
            </div>
          </AnimatedGradient>
        </FadeInUp>

        {/* Button Showcase */}
        <FadeInUp delay={1.6} className="flex flex-wrap justify-center gap-4">
          <AnimatedButton variant="primary" size="lg">
            Primary Button
          </AnimatedButton>
          <AnimatedButton variant="glow" size="lg">
            Glowing Button
          </AnimatedButton>
          <AnimatedButton variant="ghost" size="lg">
            Ghost Button
          </AnimatedButton>
        </FadeInUp>
      </div>
    </div>
  );
}