import React from 'react';
import useAuthStore from '@/stores/authStore';

export const AuthTester: React.FC = () => {
  const { user, token, isAuthenticated, isLoading, login, logout } = useAuthStore();

  const handleTestLogin = async () => {
    try {
      console.log('🧪 Testing login...');
      await login('<EMAIL>', 'SS-t00clever');
      console.log('✅ Login successful, redirecting to dashboard...');
      // Force page reload to ensure ProtectedRoute re-evaluates
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('❌ Login failed:', error);
    }
  };

  const handleTestLogout = () => {
    console.log('🧪 Testing logout...');
    logout();
    console.log('✅ Logout completed');
  };

  const handleForceAuth = () => {
    console.log('🚀 Force setting auth data using simple auth utils...');
    
    // Create test user data
    const testUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      first_name: 'Rain',
      last_name: 'User',
      role: 'admin' as const
    };
    
    const testToken = 'test-token-12345';
    
    // Import auth utils dynamically
    import('@/utils/auth').then(({ default: authUtils }) => {
      // Use simple auth utils to set auth data
      authUtils.setAuth(testUser, testToken);
      console.log('✅ Auth data set via authUtils, redirecting to dashboard...');
      
      // Redirect to dashboard
      window.location.href = '/dashboard';
    });
  };

  const checkLocalStorage = () => {
    const stored = localStorage.getItem('auth-store');
    console.log('📱 Current localStorage auth-store:', stored);
    
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        console.log('📱 Parsed auth-store:', {
          user: parsed.state?.user,
          token: !!parsed.state?.token,
          userEmail: parsed.state?.user?.email
        });
      } catch (error) {
        console.error('Failed to parse auth-store:', error);
      }
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 shadow-lg rounded-lg border max-w-sm">
      <h3 className="font-bold mb-2">🔧 Auth Tester</h3>
      
      <div className="space-y-2 text-sm">
        <div>Status: {isAuthenticated ? '✅ Authenticated' : '❌ Not Auth'}</div>
        <div>Loading: {isLoading ? '⏳' : '✅'}</div>
        <div>User: {user?.email || 'None'}</div>
        <div>Token: {token ? '✅ Exists' : '❌ Missing'}</div>
        
        <div className="space-y-1 pt-2">
          <button 
            onClick={handleTestLogin}
            className="w-full bg-blue-500 text-white px-2 py-1 rounded text-xs"
          >
            Test Login
          </button>
          <button 
            onClick={handleForceAuth}
            className="w-full bg-green-500 text-white px-2 py-1 rounded text-xs font-bold"
          >
            🚀 Force Auth
          </button>
          <button 
            onClick={handleTestLogout}
            className="w-full bg-red-500 text-white px-2 py-1 rounded text-xs"
          >
            Test Logout
          </button>
          <button 
            onClick={checkLocalStorage}
            className="w-full bg-gray-500 text-white px-2 py-1 rounded text-xs"
          >
            Check Storage
          </button>
        </div>
      </div>
    </div>
  );
};