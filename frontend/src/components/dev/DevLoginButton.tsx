import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface TestUser {
  email: string;
  role: string;
  firstName: string;
  lastName: string;
  description: string;
}

const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    role: 'user',
    firstName: 'Test',
    lastName: 'User',
    description: 'Default test user with standard permissions'
  },
  {
    email: '<EMAIL>', 
    role: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    description: 'Administrator with full system access'
  },
  {
    email: '<EMAIL>',
    role: 'user', 
    firstName: 'John',
    lastName: 'Doe',
    description: 'Regular user from Acme Corporation'
  },
  {
    email: '<EMAIL>',
    role: 'premium',
    firstName: 'Premium',
    lastName: 'User', 
    description: 'Premium subscriber with enhanced features'
  },
  {
    email: '<EMAIL>',
    role: 'developer',
    firstName: 'Developer',
    lastName: 'Test',
    description: 'Developer account for API testing'
  },
  {
    email: '<EMAIL>',
    role: 'admin',
    firstName: 'Rain',
    lastName: 'Admin',
    description: 'Primary administrator and system owner'
  }
];

interface DevLoginButtonProps {
  onLogin?: (user: any) => void;
  className?: string;
}

export const DevLoginButton: React.FC<DevLoginButtonProps> = ({ 
  onLogin,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showUsers, setShowUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState<TestUser | null>(null);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const handleQuickLogin = async (userEmail = '<EMAIL>') => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/dev/test-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: userEmail }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Store auth token
        localStorage.setItem('accessToken', data.data.accessToken);
        
        // Store user info
        localStorage.setItem('user', JSON.stringify(data.data.user));
        
        console.log('🎉 Development login successful:', data.data.user);
        
        // Call callback if provided
        if (onLogin) {
          onLogin(data.data);
        } else {
          // Default behavior - reload page
          window.location.reload();
        }
      } else {
        throw new Error(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('❌ Development login failed:', error);
      alert(`Login failed: ${error}`);
    } finally {
      setIsLoading(false);
      setShowUsers(false);
      setSelectedUser(null);
    }
  };

  const handleCreateAllUsers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/dev/create-test-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Test users created:', data.data?.users);
      alert(`Created ${data.data?.users?.length || 0} test users`);
    } catch (error) {
      console.error('❌ Failed to create test users:', error);
      alert(`Failed to create test users: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'premium': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'developer': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className={`dev-login-container ${className}`}>
      {/* Development Badge */}
      <div className="mb-4 text-center">
        <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-300">
          🚧 Development Mode
        </Badge>
      </div>

      {/* Quick Login Button */}
      <div className="space-y-3">
        <Button
          onClick={() => handleQuickLogin()}
          variant="outline"
          disabled={isLoading}
          className="w-full bg-green-50 hover:bg-green-100 border-green-200 text-green-800 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:border-green-800 dark:text-green-200"
        >
          {isLoading ? '🔄 Logging in...' : '⚡ Quick Login (Default User)'}
        </Button>

        {/* Show Users Button */}
        <Button
          onClick={() => setShowUsers(!showUsers)}
          variant="outline" 
          className="w-full"
          disabled={isLoading}
        >
          {showUsers ? '▲ Hide Test Users' : '▼ Choose Test User'}
        </Button>

        {/* Create All Users Button */}
        <Button
          onClick={handleCreateAllUsers}
          variant="outline"
          disabled={isLoading}
          className="w-full text-sm bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:border-blue-800 dark:text-blue-200"
        >
          {isLoading ? '🔄 Creating...' : '👥 Create All Test Users'}
        </Button>
      </div>

      {/* User Selection */}
      {showUsers && (
        <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
            Select Test User:
          </h4>
          <div className="space-y-2">
            {testUsers.map((user, index) => (
              <div
                key={user.email}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded-lg border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors"
                onClick={() => setSelectedUser(user)}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {user.firstName} {user.lastName}
                    </span>
                    <Badge className={getRoleBadgeColor(user.role)}>
                      {user.role}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {user.email}
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500">
                    {user.description}
                  </p>
                </div>
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleQuickLogin(user.email);
                  }}
                  size="sm"
                  disabled={isLoading}
                  className="ml-2"
                >
                  Login
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h5 className="text-xs font-semibold text-blue-800 dark:text-blue-200 mb-1">
          💡 Development Tips:
        </h5>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• All test users have verified emails</li>
          <li>• Tokens are automatically stored in localStorage</li>
          <li>• Page will reload after successful login</li>
          <li>• Use <EMAIL> for administrative features</li>
          <li>• All passwords are: [Role]Password123!</li>
        </ul>
      </div>

      {/* API Endpoints Info */}
      <details className="mt-3">
        <summary className="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
          📋 API Endpoints (Click to expand)
        </summary>
        <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono space-y-1">
          <div><strong>POST</strong> /api/auth/dev/test-login</div>
          <div><strong>POST</strong> /api/auth/dev/create-test-users</div>
        </div>
      </details>
    </div>
  );
};