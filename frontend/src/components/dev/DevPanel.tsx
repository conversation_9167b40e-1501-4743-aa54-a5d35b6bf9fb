import React, { useState } from 'react';
import { DevLoginButton } from './DevLoginButton';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

interface DevPanelProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  minimized?: boolean;
}

export const DevPanel: React.FC<DevPanelProps> = ({ 
  position = 'bottom-right',
  minimized = true 
}) => {
  const [isMinimized, setIsMinimized] = useState(minimized);
  const [currentUser, setCurrentUser] = useState(() => {
    try {
      const user = localStorage.getItem('user');
      return user ? JSON.parse(user) : null;
    } catch {
      return null;
    }
  });

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const getPositionClasses = () => {
    const base = 'fixed z-[9999]';
    switch (position) {
      case 'top-left': return `${base} top-4 left-4`;
      case 'top-right': return `${base} top-4 right-4`;
      case 'bottom-left': return `${base} bottom-4 left-4`;
      case 'bottom-right': return `${base} bottom-4 right-4`;
      default: return `${base} bottom-4 right-4`;
    }
  };

  const handleLogin = (userData: any) => {
    setCurrentUser(userData.user);
    // Don't reload the page, just update the state
  };

  const handleLogout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');
    setCurrentUser(null);
    window.location.reload();
  };

  const clearLocalStorage = () => {
    if (confirm('Clear all localStorage? This will log you out.')) {
      localStorage.clear();
      window.location.reload();
    }
  };

  return (
    <div className={getPositionClasses()}>
      {isMinimized ? (
        /* Minimized State */
        <Button
          onClick={() => setIsMinimized(false)}
          className="bg-purple-600 hover:bg-purple-700 text-white shadow-lg rounded-full w-12 h-12 flex items-center justify-center text-lg"
          title="Open Development Panel"
        >
          🛠️
        </Button>
      ) : (
        /* Expanded State */
        <Card className="w-80 shadow-xl bg-white dark:bg-gray-800 border-2 border-purple-200 dark:border-purple-700">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-purple-50 dark:bg-purple-900/20">
            <div className="flex items-center space-x-2">
              <span className="text-lg">🛠️</span>
              <h3 className="font-semibold text-purple-800 dark:text-purple-200">
                Dev Panel
              </h3>
            </div>
            <Button
              onClick={() => setIsMinimized(true)}
              variant="outline"
              size="sm"
              className="text-purple-600 border-purple-300 hover:bg-purple-100"
            >
              ✕
            </Button>
          </div>

          {/* Content */}
          <div className="p-4 space-y-4">
            {/* Current User Status */}
            {currentUser ? (
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-green-800 dark:text-green-200">
                      🟢 Logged in as:
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {currentUser.firstName} {currentUser.lastName}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      {currentUser.email} ({currentUser.role})
                    </p>
                  </div>
                  <Button
                    onClick={handleLogout}
                    size="sm"
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    Logout
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  🔒 Not logged in
                </p>
              </div>
            )}

            {/* Login Section */}
            <DevLoginButton onLogin={handleLogin} />

            {/* Development Tools */}
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                🔧 Dev Tools
              </h4>
              
              <Button
                onClick={clearLocalStorage}
                variant="outline" 
                className="w-full text-sm text-red-600 border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                🗑️ Clear LocalStorage
              </Button>

              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full text-sm"
              >
                🔄 Reload Page
              </Button>

              <Button
                onClick={() => {
                  console.log('Current localStorage:', {
                    accessToken: localStorage.getItem('accessToken'),
                    user: localStorage.getItem('user'),
                    allKeys: Object.keys(localStorage)
                  });
                }}
                variant="outline"
                className="w-full text-sm"
              >
                🔍 Log Storage to Console
              </Button>
            </div>

            {/* Quick Links */}
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                🔗 Quick Links
              </h4>
              
              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={() => window.open('/api/health', '_blank')}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  API Health
                </Button>
                
                <Button
                  onClick={() => window.open('/api/docs', '_blank')}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  API Docs
                </Button>
                
                <Button
                  onClick={() => {
                    const token = localStorage.getItem('accessToken');
                    if (token) {
                      console.log('JWT Token:', token);
                      try {
                        const payload = JSON.parse(atob(token.split('.')[1]));
                        console.log('JWT Payload:', payload);
                        console.log('Token expires:', new Date(payload.exp * 1000));
                      } catch (e) {
                        console.log('Could not decode JWT');
                      }
                    }
                  }}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Inspect JWT
                </Button>
                
                <Button
                  onClick={() => window.open('http://localhost:3000/api/auth/dev/test-login', '_blank')}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Test API
                </Button>
              </div>
            </div>

            {/* Environment Info */}
            <div className="text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-700">
              <p>Environment: {process.env.NODE_ENV}</p>
              <p>React Version: {React.version}</p>
              <p>Build: {process.env.REACT_APP_VERSION || 'dev'}</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};