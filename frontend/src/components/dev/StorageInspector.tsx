import React, { useState } from 'react';

export const StorageInspector: React.FC = () => {
  const [storageContent, setStorageContent] = useState<string>('');

  const inspectStorage = () => {
    const authStore = localStorage.getItem('auth-store');
    console.log('Raw localStorage content:', authStore);
    
    if (authStore) {
      try {
        const parsed = JSON.parse(authStore);
        console.log('Parsed structure:', parsed);
        console.log('Available keys:', Object.keys(parsed));
        console.log('Has state key:', 'state' in parsed);
        console.log('Has user directly:', 'user' in parsed);
        console.log('Has token directly:', 'token' in parsed);
        
        setStorageContent(JSON.stringify(parsed, null, 2));
      } catch (error) {
        console.error('Parse error:', error);
        setStorageContent(`Parse Error: ${error}`);
      }
    } else {
      setStorageContent('No auth-store found in localStorage');
    }
  };

  const clearStorage = () => {
    localStorage.removeItem('auth-store');
    setStorageContent('Storage cleared');
  };

  return (
    <div className="fixed bottom-4 left-4 bg-white p-4 shadow-lg rounded-lg border max-w-md">
      <h3 className="font-bold mb-2">🔍 Storage Inspector</h3>
      
      <div className="space-y-2">
        <button 
          onClick={inspectStorage}
          className="w-full bg-blue-500 text-white px-2 py-1 rounded text-xs"
        >
          Inspect Storage
        </button>
        <button 
          onClick={clearStorage}
          className="w-full bg-red-500 text-white px-2 py-1 rounded text-xs"
        >
          Clear Storage
        </button>
        
        {storageContent && (
          <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
            {storageContent}
          </pre>
        )}
      </div>
    </div>
  );
};