// Development components - automatically excluded from production builds
export { DevPanel } from './DevPanel';
export { DevLoginButton } from './DevLoginButton';

// Re-export with environment check for convenience
export const DevTools = process.env.NODE_ENV === 'development' ? {
  DevPanel: require('./DevPanel').DevPanel,
  DevLoginButton: require('./DevLoginButton').DevLoginButton,
} : {
  DevPanel: () => null,
  DevLoginButton: () => null,
};