# Modern UI Library

A comprehensive collection of modern React components inspired by Aceternity UI and Magic UI, featuring stunning animations, 3D effects, and glassmorphism designs.

## 🚀 Features

- **Glassmorphism Effects**: Beautiful glass-like components with backdrop blur
- **Framer Motion Animations**: Smooth, performant animations
- **3D Visual Effects**: Depth and dimension with modern CSS
- **Magic UI Components**: Interactive micro-animations
- **Aceternity UI Style**: Professional, polished component library
- **TypeScript Support**: Full type safety and IntelliSense
- **Tailwind CSS Integration**: Utility-first styling approach

## 📦 Components

### Core Components (Aceternity UI inspired)
- `GlassmorphismCard` - Glass effect containers
- `AnimatedButton` - Interactive buttons with hover states
- `FloatingElements` - Animated background particles
- `GradientText` - Colorful gradient text effects
- `ParticleBackground` - Dynamic particle systems

### Visual Effects
- `GlowEffect` - Pulsing glow animations
- `ShimmerEffect` - Elegant shimmer animations
- `RippleEffect` - Click ripple interactions
- `BlurredBackground` - Backdrop blur containers
- `NeonBorder` - Glowing animated borders

### Animations
- `FadeInUp` - Fade and slide up animations
- `SlideInLeft` - Side slide animations
- `ScaleAnimation` - Scale hover effects
- `RotateAnimation` - Rotation animations
- `TypewriterEffect` - Typing text animations

### Magic UI Components
- `MagicCard` - Gradient border cards with spotlight
- `AnimatedGradient` - Moving gradient backgrounds
- `HoverEffect` - Advanced hover interactions
- `MorphingButton` - Shape-changing buttons
- `FloatingDots` - Interactive dot patterns
- `InteractiveGrid` - Responsive grid layouts
- `WaveEffect` - Wave motion animations
- `ParallaxContainer` - Parallax scroll effects

### Aceternity UI Components
- `SparklesCore` - Sparkling particle effects
- `TextGenerateEffect` - Text reveal animations
- `CardContainer` - 3D card containers
- `BackgroundBeams` - Animated background beams
- `InfiniteMovingCards` - Carousel components

## 🎨 Usage Examples

### Basic Glassmorphism Card
```tsx
import { GlassmorphismCard, GradientText } from '@/components/ui';

function MyComponent() {
  return (
    <GlassmorphismCard blur="lg" opacity={0.1}>
      <GradientText gradient="blue" className="text-2xl font-bold">
        Beautiful Glass Card
      </GradientText>
      <p>Content with glassmorphism background</p>
    </GlassmorphismCard>
  );
}
```

### Magic Card with Animations
```tsx
import { MagicCard, AnimatedButton, MorphingButton } from '@/components/ui';

function InteractiveCard() {
  return (
    <MagicCard gradient="cosmic" spotlight={true}>
      <div className="space-y-4">
        <h3>Magic Card</h3>
        <AnimatedButton variant="glow">
          Click Me
        </AnimatedButton>
        <MorphingButton hoverContent="✨ Amazing!">
          Hover for Magic
        </MorphingButton>
      </div>
    </MagicCard>
  );
}
```

### Advanced Effects Combination
```tsx
import { 
  ParticleBackground, 
  GlowEffect, 
  NeonBorder, 
  TypewriterEffect,
  FadeInUp 
} from '@/components/ui';

function AdvancedDemo() {
  return (
    <div className="relative min-h-screen">
      <ParticleBackground count={50} color="#3B82F6" />
      
      <FadeInUp delay={0.5}>
        <GlowEffect color="purple" intensity="high" pulse>
          <NeonBorder color="cyan" width={3}>
            <div className="p-8 glass rounded-xl">
              <TypewriterEffect 
                text="Welcome to the future"
                speed={100}
                cursor={true}
              />
            </div>
          </NeonBorder>
        </GlowEffect>
      </FadeInUp>
    </div>
  );
}
```

## 🛠️ Installation

The components are already installed and configured. They use:
- `framer-motion` for animations
- `clsx` and `tailwind-merge` for className utilities
- Custom Tailwind CSS configuration for glassmorphism
- TypeScript for type safety

## 🎨 Tailwind CSS Extensions

The library includes custom Tailwind utilities:
- `.glass` - Standard glassmorphism effect
- `.glass-dark` - Dark theme glassmorphism
- `.glass-strong` - Intense glass effect
- `.glass-subtle` - Subtle glass effect
- `.shimmer` - Shimmer animation utility

## 🎭 Animation Presets

Pre-configured Framer Motion variants:
- `fadeInUp` - Fade in from bottom
- `fadeIn` - Simple fade animation
- `slideInLeft` - Slide from left
- `scaleIn` - Scale up animation

## 🎯 Performance Optimizations

- Optimized animation performance with Framer Motion
- Lazy loading support for complex components
- Debounced and throttled utilities included
- Responsive design utilities built-in

## 🔧 Customization

All components accept:
- `className` for additional styling
- Various size, color, and intensity props
- Animation timing and behavior controls
- Theme-aware color schemes

## 🚨 Notes

- React Three Fiber libraries are pending installation due to dependency conflicts
- All components are TypeScript-ready
- Tailwind CSS configuration includes glassmorphism utilities
- Components follow the project's design system

## 📱 Responsive Design

Components are built with mobile-first responsive design:
- Breakpoint utilities included
- Responsive animation controls
- Touch-friendly interactions
- Optimized for all screen sizes

## 🎨 Color Schemes

Service-specific colors integrated:
- Velian: `#8b5cf6`
- ZeroEntropy: `#06b6d4`
- Hello.cv: `#10b981`
- YoinkUI: `#f59e0b`
- Clueso: `#ef4444`
- Permut: `#8b5cf6`
- Intervo: `#06b6d4`
- Pixelesq: `#ec4899`

Use with `getServiceColor()` utility function.