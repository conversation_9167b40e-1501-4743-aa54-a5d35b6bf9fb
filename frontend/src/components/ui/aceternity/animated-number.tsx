import React, { useEffect, useState } from 'react';
import { motion, useSpring, useTransform } from 'framer-motion';
import { cn } from '@/utils/cn';

interface AnimatedNumberProps {
  value: number;
  className?: string;
  duration?: number;
  format?: (value: number) => string;
}

export default function AnimatedNumber({
  value,
  className,
  duration = 2,
  format = (val) => val.toLocaleString()
}: AnimatedNumberProps) {
  const [isVisible, setIsVisible] = useState(false);
  const spring = useSpring(0, { duration: duration * 1000 });
  const display = useTransform(spring, (current) => format(Math.round(current)));
  
  useEffect(() => {
    setIsVisible(true);
  }, []);
  
  useEffect(() => {
    if (isVisible) {
      spring.set(value);
    }
  }, [spring, value, isVisible]);
  
  return (
    <motion.span
      className={cn('inline-block tabular-nums', className)}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      {display}
    </motion.span>
  );
}