import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface AuroraBackgroundProps {
  children: React.ReactNode;
  className?: string;
  showRadialGradient?: boolean;
  colors?: string[];
}

export default function AuroraBackground({
  children,
  className,
  showRadialGradient = true,
  colors = ['#13FFAA', '#1E67C6', '#CE84CF', '#DD335C']
}: AuroraBackgroundProps) {
  return (
    <div className={cn('relative flex flex-col h-full w-full items-center justify-center bg-zinc-950 transition-all', className)}>
      <div className="absolute inset-0 overflow-hidden">
        <div
          className={cn(
            'pointer-events-none absolute -inset-[10px] opacity-50',
            showRadialGradient && '[mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]'
          )}
        >
          <svg
            className="absolute inset-0 h-full w-full"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <filter id="aurora">
                <feTurbulence
                  type="fractalNoise"
                  baseFrequency="0.00001"
                  numOctaves="1"
                  result="turbulence"
                  seed="3"
                />
                <feColorMatrix
                  in="turbulence"
                  type="saturate"
                  values="2"
                />
              </filter>
            </defs>
            <rect width="100%" height="100%" filter="url(#aurora)" opacity="0.6">
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="60s"
                repeatCount="indefinite"
              />
            </rect>
          </svg>
        </div>
        
        {/* Animated gradient blobs */}
        {colors.map((color, index) => (
          <motion.div
            key={index}
            className="absolute rounded-full mix-blend-screen filter blur-3xl opacity-30"
            style={{
              backgroundColor: color,
              width: `${Math.random() * 400 + 200}px`,
              height: `${Math.random() * 400 + 200}px`,
            }}
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              repeatType: 'reverse',
              delay: index * 2,
            }}
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
          />
        ))}
      </div>
      
      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center">
        {children}
      </div>
    </div>
  );
}