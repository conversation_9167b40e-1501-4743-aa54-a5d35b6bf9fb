import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
}

interface BentoGridItemProps {
  children: React.ReactNode;
  className?: string;
  colSpan?: 1 | 2 | 3 | 4;
  rowSpan?: 1 | 2 | 3 | 4;
}

export function BentoGrid({ children, className }: BentoGridProps) {
  return (
    <div
      className={cn(
        'grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto',
        className
      )}
    >
      {children}
    </div>
  );
}

export function BentoGridItem({
  children,
  className,
  colSpan = 1,
  rowSpan = 1
}: BentoGridItemProps) {
  return (
    <motion.div
      className={cn(
        'relative group/bento',
        colSpan === 2 && 'md:col-span-2',
        colSpan === 3 && 'md:col-span-3',
        colSpan === 4 && 'md:col-span-4',
        rowSpan === 2 && 'md:row-span-2',
        rowSpan === 3 && 'md:row-span-3',
        rowSpan === 4 && 'md:row-span-4',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="h-full w-full">
        {children}
      </div>
    </motion.div>
  );
}