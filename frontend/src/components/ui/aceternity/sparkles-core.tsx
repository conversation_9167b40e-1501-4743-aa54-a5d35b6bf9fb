import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface SparklesCoreProps {
  id?: string;
  className?: string;
  background?: string;
  minSize?: number;
  maxSize?: number;
  particleDensity?: number;
  particleColor?: string;
  speed?: number;
}

export default function SparklesCore({
  id = 'sparkles',
  className,
  background = 'transparent',
  minSize = 1,
  maxSize = 3,
  particleDensity = 50,
  particleColor = '#ffffff',
  speed = 1
}: SparklesCoreProps) {
  const sparkles = useMemo(() => {
    return Array.from({ length: particleDensity }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * (maxSize - minSize) + minSize,
      duration: (Math.random() * 3 + 2) / speed,
      delay: Math.random() * 2,
      opacity: Math.random() * 0.8 + 0.2
    }));
  }, [particleDensity, minSize, maxSize, speed]);

  return (
    <div
      id={id}
      className={cn('relative w-full h-full overflow-hidden', className)}
      style={{ background }}
    >
      {sparkles.map((sparkle) => (
        <motion.div
          key={sparkle.id}
          className="absolute rounded-full pointer-events-none"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`,
            width: sparkle.size,
            height: sparkle.size,
            backgroundColor: particleColor,
            boxShadow: `0 0 ${sparkle.size * 2}px ${particleColor}`,
          }}
          initial={{ 
            opacity: 0,
            scale: 0
          }}
          animate={{
            opacity: [0, sparkle.opacity, 0],
            scale: [0, 1, 0],
            y: [0, -50, -100],
          }}
          transition={{
            duration: sparkle.duration,
            repeat: Infinity,
            delay: sparkle.delay,
            ease: "easeOut"
          }}
        />
      ))}
    </div>
  );
}