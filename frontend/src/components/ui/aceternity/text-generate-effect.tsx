import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface TextGenerateEffectProps {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}

export default function TextGenerateEffect({
  words,
  className,
  filter = true,
  duration = 1
}: TextGenerateEffectProps) {
  const [scope, setScope] = useState<string[]>([]);
  const wordsArray = words.split(' ');

  useEffect(() => {
    const timer = setTimeout(() => {
      setScope(wordsArray);
    }, 100);

    return () => clearTimeout(timer);
  }, [words]);

  const renderWords = () => {
    return (
      <motion.div>
        {wordsArray.map((word, idx) => (
          <motion.span
            key={word + idx}
            className={cn(
              'opacity-0',
              filter && 'filter blur-sm'
            )}
            animate={
              scope.includes(word)
                ? {
                    opacity: 1,
                    filter: filter ? 'blur(0px)' : 'none',
                  }
                : {}
            }
            transition={{
              duration: duration,
              delay: idx * 0.2,
              ease: 'easeInOut'
            }}
          >
            {word}{' '}
          </motion.span>
        ))}
      </motion.div>
    );
  };

  return (
    <div className={cn('font-bold', className)}>
      <div className="my-4">
        <div className="text-black dark:text-white leading-snug tracking-wide">
          {renderWords()}
        </div>
      </div>
    </div>
  );
}