import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface FadeInUpProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  distance?: number;
}

export default function FadeInUp({
  children,
  className,
  delay = 0,
  duration = 0.5,
  distance = 30
}: FadeInUpProps) {
  return (
    <motion.div
      className={cn(className)}
      initial={{ opacity: 0, y: distance }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration, delay, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
}