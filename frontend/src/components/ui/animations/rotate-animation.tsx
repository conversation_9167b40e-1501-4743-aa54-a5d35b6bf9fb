import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface RotateAnimationProps {
  children: React.ReactNode;
  className?: string;
  rotation?: number;
  duration?: number;
  continuous?: boolean;
  direction?: 'clockwise' | 'counterclockwise';
  trigger?: 'hover' | 'always';
}

export default function RotateAnimation({
  children,
  className,
  rotation = 360,
  duration = 2,
  continuous = false,
  direction = 'clockwise',
  trigger = 'hover'
}: RotateAnimationProps) {
  const rotationValue = direction === 'counterclockwise' ? -rotation : rotation;

  if (continuous) {
    return (
      <motion.div
        className={cn(className)}
        animate={{ rotate: rotationValue }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "linear"
        }}
      >
        {children}
      </motion.div>
    );
  }

  if (trigger === 'always') {
    return (
      <motion.div
        className={cn(className)}
        initial={{ rotate: 0 }}
        animate={{ rotate: rotationValue }}
        transition={{ duration, ease: "easeInOut" }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={cn(className)}
      whileHover={{ rotate: rotationValue }}
      transition={{ duration, ease: "easeInOut" }}
    >
      {children}
    </motion.div>
  );
}