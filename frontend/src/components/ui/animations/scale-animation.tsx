import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface ScaleAnimationProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  scale?: number;
  trigger?: 'hover' | 'tap' | 'always';
}

export default function ScaleAnimation({
  children,
  className,
  delay = 0,
  duration = 0.3,
  scale = 1.05,
  trigger = 'hover'
}: ScaleAnimationProps) {
  const animationProps = {
    initial: { scale: 1 },
    transition: { duration, delay, ease: "easeOut" }
  };

  if (trigger === 'always') {
    return (
      <motion.div
        className={cn(className)}
        {...animationProps}
        animate={{ scale }}
      >
        {children}
      </motion.div>
    );
  }

  if (trigger === 'tap') {
    return (
      <motion.div
        className={cn(className)}
        {...animationProps}
        whileTap={{ scale }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={cn(className)}
      {...animationProps}
      whileHover={{ scale }}
    >
      {children}
    </motion.div>
  );
}