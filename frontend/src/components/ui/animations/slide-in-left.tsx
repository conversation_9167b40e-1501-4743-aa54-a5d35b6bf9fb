import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface SlideInLeftProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  distance?: number;
}

export default function SlideInLeft({
  children,
  className,
  delay = 0,
  duration = 0.5,
  distance = 50
}: SlideInLeftProps) {
  return (
    <motion.div
      className={cn(className)}
      initial={{ opacity: 0, x: -distance }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration, delay, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
}