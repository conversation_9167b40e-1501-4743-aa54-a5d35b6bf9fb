import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface TypewriterEffectProps {
  text: string;
  className?: string;
  speed?: number;
  cursor?: boolean;
  cursorClassName?: string;
  onComplete?: () => void;
}

export default function TypewriterEffect({
  text,
  className,
  speed = 50,
  cursor = true,
  cursorClassName,
  onComplete
}: TypewriterEffectProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  return (
    <span className={cn(className)}>
      {displayedText}
      {cursor && (
        <motion.span
          className={cn('inline-block', cursorClassName)}
          animate={{ opacity: [1, 0, 1] }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          |
        </motion.span>
      )}
    </span>
  );
}