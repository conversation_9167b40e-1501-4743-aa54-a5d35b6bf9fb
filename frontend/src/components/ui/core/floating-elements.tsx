import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface FloatingElementsProps {
  count?: number;
  className?: string;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
}

const sizeClasses = {
  sm: 'w-2 h-2',
  md: 'w-4 h-4',
  lg: 'w-6 h-6'
};

export default function FloatingElements({
  count = 6,
  className,
  color = 'bg-blue-400',
  size = 'md'
}: FloatingElementsProps) {
  const elements = Array.from({ length: count }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2
  }));

  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className={cn(
            'absolute rounded-full opacity-20',
            sizeClasses[size],
            color
          )}
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 0.3, 0],
            y: [0, -50, -100],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            delay: element.delay,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
}