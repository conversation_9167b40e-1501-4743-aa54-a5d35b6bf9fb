import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface GlassmorphismCardProps {
  children: React.ReactNode;
  className?: string;
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  opacity?: number;
  borderOpacity?: number;
  gradient?: boolean;
}

const blurClasses = {
  sm: 'backdrop-blur-sm',
  md: 'backdrop-blur-md',
  lg: 'backdrop-blur-lg',
  xl: 'backdrop-blur-xl'
};

export default function GlassmorphismCard({
  children,
  className,
  blur = 'md',
  opacity = 0.1,
  borderOpacity = 0.2,
  gradient = true
}: GlassmorphismCardProps) {
  return (
    <motion.div
      className={cn(
        'relative rounded-xl border p-6',
        blurClasses[blur],
        gradient ? 'bg-gradient-to-br from-white/10 to-white/5' : `bg-white/${Math.round(opacity * 100)}`,
        `border-white/${Math.round(borderOpacity * 100)}`,
        'shadow-[0_8px_32px_rgba(31,38,135,0.37)]',
        'before:absolute before:inset-0 before:rounded-xl before:p-[1px]',
        'before:bg-gradient-to-br before:from-white/20 before:to-transparent',
        'before:-z-10',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
}