import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface GradientTextProps {
  children: React.ReactNode;
  className?: string;
  gradient?: 'blue' | 'purple' | 'pink' | 'green' | 'orange' | 'rainbow';
  animate?: boolean;
}

const gradientClasses = {
  blue: 'bg-gradient-to-r from-blue-400 to-blue-600',
  purple: 'bg-gradient-to-r from-purple-400 to-purple-600',
  pink: 'bg-gradient-to-r from-pink-400 to-pink-600',
  green: 'bg-gradient-to-r from-green-400 to-green-600',
  orange: 'bg-gradient-to-r from-orange-400 to-orange-600',
  rainbow: 'bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-blue-400 to-purple-400'
};

export default function GradientText({
  children,
  className,
  gradient = 'blue',
  animate = false
}: GradientTextProps) {
  const textElement = (
    <span
      className={cn(
        'bg-clip-text text-transparent font-bold',
        gradientClasses[gradient],
        animate && 'animate-pulse',
        className
      )}
    >
      {children}
    </span>
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {textElement}
      </motion.div>
    );
  }

  return textElement;
}