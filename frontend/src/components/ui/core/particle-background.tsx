import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface ParticleBackgroundProps {
  count?: number;
  className?: string;
  color?: string;
  speed?: 'slow' | 'normal' | 'fast';
}

export default function ParticleBackground({
  count = 50,
  className,
  color = '#3B82F6',
  speed = 'normal'
}: ParticleBackgroundProps) {
  const speedMultiplier = {
    slow: 0.5,
    normal: 1,
    fast: 1.5
  }[speed];

  const particles = useMemo(() => {
    return Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      duration: (Math.random() * 10 + 10) / speedMultiplier,
      delay: Math.random() * 5,
      opacity: Math.random() * 0.5 + 0.1
    }));
  }, [count, speedMultiplier]);

  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: particle.size,
            height: particle.size,
            backgroundColor: color,
            opacity: particle.opacity,
          }}
          animate={{
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            scale: [1, Math.random() + 0.5, 1],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            repeatType: "reverse",
            delay: particle.delay,
            ease: "linear"
          }}
        />
      ))}
    </div>
  );
}