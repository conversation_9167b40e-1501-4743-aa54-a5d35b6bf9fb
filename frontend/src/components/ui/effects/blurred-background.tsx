import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface BlurredBackgroundProps {
  children: React.ReactNode;
  className?: string;
  blur?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  overlay?: boolean;
  overlayOpacity?: number;
}

const blurClasses = {
  sm: 'backdrop-blur-sm',
  md: 'backdrop-blur-md',
  lg: 'backdrop-blur-lg',
  xl: 'backdrop-blur-xl',
  '2xl': 'backdrop-blur-2xl',
  '3xl': 'backdrop-blur-3xl'
};

export default function BlurredBackground({
  children,
  className,
  blur = 'lg',
  overlay = true,
  overlayOpacity = 0.1
}: BlurredBackgroundProps) {
  return (
    <motion.div
      className={cn(
        'relative',
        blurClasses[blur],
        overlay && `bg-black/${Math.round(overlayOpacity * 100)}`,
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
}