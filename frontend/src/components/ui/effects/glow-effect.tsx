import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface GlowEffectProps {
  children: React.ReactNode;
  className?: string;
  color?: 'blue' | 'purple' | 'pink' | 'green' | 'orange';
  intensity?: 'low' | 'medium' | 'high';
  pulse?: boolean;
}

const colorClasses = {
  blue: 'shadow-blue-500/50',
  purple: 'shadow-purple-500/50',
  pink: 'shadow-pink-500/50',
  green: 'shadow-green-500/50',
  orange: 'shadow-orange-500/50'
};

const intensityClasses = {
  low: 'shadow-lg',
  medium: 'shadow-xl',
  high: 'shadow-2xl'
};

export default function GlowEffect({
  children,
  className,
  color = 'blue',
  intensity = 'medium',
  pulse = false
}: GlowEffectProps) {
  return (
    <motion.div
      className={cn(
        'relative',
        intensityClasses[intensity],
        colorClasses[color],
        pulse && 'animate-pulse',
        className
      )}
      animate={pulse ? {
        boxShadow: [
          `0 0 20px ${color === 'blue' ? 'rgba(59, 130, 246, 0.5)' : 
                      color === 'purple' ? 'rgba(147, 51, 234, 0.5)' :
                      color === 'pink' ? 'rgba(236, 72, 153, 0.5)' :
                      color === 'green' ? 'rgba(34, 197, 94, 0.5)' :
                      'rgba(249, 115, 22, 0.5)'}`,
          `0 0 40px ${color === 'blue' ? 'rgba(59, 130, 246, 0.3)' : 
                      color === 'purple' ? 'rgba(147, 51, 234, 0.3)' :
                      color === 'pink' ? 'rgba(236, 72, 153, 0.3)' :
                      color === 'green' ? 'rgba(34, 197, 94, 0.3)' :
                      'rgba(249, 115, 22, 0.3)'}`,
          `0 0 20px ${color === 'blue' ? 'rgba(59, 130, 246, 0.5)' : 
                      color === 'purple' ? 'rgba(147, 51, 234, 0.5)' :
                      color === 'pink' ? 'rgba(236, 72, 153, 0.5)' :
                      color === 'green' ? 'rgba(34, 197, 94, 0.5)' :
                      'rgba(249, 115, 22, 0.5)'}`
        ]
      } : {}}
      transition={{
        duration: 2,
        repeat: pulse ? Infinity : 0,
        repeatType: "reverse"
      }}
    >
      {children}
    </motion.div>
  );
}