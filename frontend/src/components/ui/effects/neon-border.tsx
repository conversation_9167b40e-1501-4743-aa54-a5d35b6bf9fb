import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface NeonBorderProps {
  children: React.ReactNode;
  className?: string;
  color?: 'blue' | 'purple' | 'pink' | 'green' | 'orange' | 'cyan';
  width?: 1 | 2 | 3 | 4;
  animate?: boolean;
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

const colorClasses = {
  blue: 'border-blue-400 shadow-[0_0_10px_#3b82f6]',
  purple: 'border-purple-400 shadow-[0_0_10px_#8b5cf6]',
  pink: 'border-pink-400 shadow-[0_0_10px_#ec4899]',
  green: 'border-green-400 shadow-[0_0_10px_#22c55e]',
  orange: 'border-orange-400 shadow-[0_0_10px_#f97316]',
  cyan: 'border-cyan-400 shadow-[0_0_10px_#06b6d4]'
};

const widthClasses = {
  1: 'border',
  2: 'border-2',
  3: 'border-4',
  4: 'border-8'
};

const roundedClasses = {
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full'
};

export default function NeonBorder({
  children,
  className,
  color = 'blue',
  width = 2,
  animate = true,
  rounded = 'lg'
}: NeonBorderProps) {
  return (
    <motion.div
      className={cn(
        'relative',
        widthClasses[width],
        colorClasses[color],
        roundedClasses[rounded],
        className
      )}
      animate={animate ? {
        boxShadow: [
          `0 0 10px ${color === 'blue' ? '#3b82f6' :
                      color === 'purple' ? '#8b5cf6' :
                      color === 'pink' ? '#ec4899' :
                      color === 'green' ? '#22c55e' :
                      color === 'orange' ? '#f97316' :
                      '#06b6d4'}`,
          `0 0 20px ${color === 'blue' ? '#3b82f6' :
                      color === 'purple' ? '#8b5cf6' :
                      color === 'pink' ? '#ec4899' :
                      color === 'green' ? '#22c55e' :
                      color === 'orange' ? '#f97316' :
                      '#06b6d4'}`,
          `0 0 10px ${color === 'blue' ? '#3b82f6' :
                      color === 'purple' ? '#8b5cf6' :
                      color === 'pink' ? '#ec4899' :
                      color === 'green' ? '#22c55e' :
                      color === 'orange' ? '#f97316' :
                      '#06b6d4'}`
        ]
      } : {}}
      transition={{
        duration: 2,
        repeat: animate ? Infinity : 0,
        repeatType: "reverse"
      }}
    >
      {children}
    </motion.div>
  );
}