import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface ShimmerEffectProps {
  children: React.ReactNode;
  className?: string;
  speed?: 'slow' | 'normal' | 'fast';
  direction?: 'left' | 'right';
}

export default function ShimmerEffect({
  children,
  className,
  speed = 'normal',
  direction = 'right'
}: ShimmerEffectProps) {
  const duration = {
    slow: 3,
    normal: 2,
    fast: 1
  }[speed];

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {children}
      <motion.div
        className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        initial={{
          x: direction === 'left' ? '100%' : '-100%'
        }}
        animate={{
          x: direction === 'left' ? '-100%' : '100%'
        }}
        transition={{
          duration,
          repeat: Infinity,
          repeatType: "loop",
          ease: "linear"
        }}
      />
    </div>
  );
}