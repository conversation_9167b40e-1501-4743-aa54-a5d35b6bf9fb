import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface AnimatedGradientProps {
  children: React.ReactNode;
  className?: string;
  colors?: string[];
  speed?: 'slow' | 'normal' | 'fast';
  direction?: 'x' | 'y' | 'xy';
}

export default function AnimatedGradient({
  children,
  className,
  colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c'],
  speed = 'normal',
  direction = 'xy'
}: AnimatedGradientProps) {
  const gradientStyle = {
    background: `linear-gradient(45deg, ${colors.join(', ')})`,
    backgroundSize: '400% 400%'
  };

  const animationClass = {
    slow: `animate-gradient-${direction}`,
    normal: `animate-gradient-${direction}`,
    fast: `animate-gradient-${direction}`
  }[speed];

  return (
    <motion.div
      className={cn(
        'relative overflow-hidden',
        animationClass,
        className
      )}
      style={gradientStyle}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
}