import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface HoverEffectProps {
  children: React.ReactNode;
  className?: string;
  effect?: 'lift' | 'glow' | 'tilt' | 'scale';
  intensity?: 'subtle' | 'medium' | 'strong';
}

export default function HoverEffect({
  children,
  className,
  effect = 'lift',
  intensity = 'medium'
}: HoverEffectProps) {
  const [isHovered, setIsHovered] = useState(false);

  const intensityValues = {
    subtle: { lift: 5, glow: 10, tilt: 5, scale: 1.02 },
    medium: { lift: 10, glow: 20, tilt: 10, scale: 1.05 },
    strong: { lift: 15, glow: 30, tilt: 15, scale: 1.08 }
  };

  const getAnimation = () => {
    const values = intensityValues[intensity];
    
    switch (effect) {
      case 'lift':
        return isHovered ? { y: -values.lift, transition: { duration: 0.2 } } : {};
      case 'glow':
        return isHovered ? { 
          boxShadow: `0 ${values.glow}px ${values.glow * 2}px rgba(59, 130, 246, 0.3)`,
          transition: { duration: 0.2 }
        } : {};
      case 'tilt':
        return isHovered ? { 
          rotateX: values.tilt,
          rotateY: values.tilt,
          transition: { duration: 0.2 }
        } : {};
      case 'scale':
        return isHovered ? { 
          scale: values.scale,
          transition: { duration: 0.2 }
        } : {};
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={cn('cursor-pointer', className)}
      animate={getAnimation()}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </motion.div>
  );
}