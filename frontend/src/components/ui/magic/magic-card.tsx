import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface MagicCardProps {
  children: React.ReactNode;
  className?: string;
  gradient?: 'aurora' | 'cosmic' | 'ocean' | 'sunset';
  spotlight?: boolean;
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl';
}

const gradientClasses = {
  aurora: 'bg-aurora',
  cosmic: 'bg-cosmic',
  ocean: 'bg-ocean',
  sunset: 'bg-sunset'
};

const radiusClasses = {
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl'
};

export default function MagicCard({
  children,
  className,
  gradient = 'aurora',
  spotlight = true,
  borderRadius = 'lg'
}: MagicCardProps) {
  return (
    <motion.div
      className={cn(
        'relative p-[2px] group',
        radiusClasses[borderRadius],
        className
      )}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* Animated gradient border */}
      <div className={cn(
        'absolute inset-0 animate-gradient-xy opacity-75 group-hover:opacity-100 transition-opacity duration-300',
        gradientClasses[gradient],
        radiusClasses[borderRadius]
      )} />
      
      {/* Spotlight effect */}
      {spotlight && (
        <div className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300">
          <div className="absolute top-0 left-1/2 w-32 h-32 -translate-x-1/2 -translate-y-16 bg-white rounded-full blur-3xl animate-pulse" />
        </div>
      )}
      
      {/* Content */}
      <div className={cn(
        'relative glass p-6 h-full',
        radiusClasses[borderRadius]
      )}>
        {children}
      </div>
    </motion.div>
  );
}