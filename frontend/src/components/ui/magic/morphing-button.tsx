import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/utils/cn';

interface MorphingButtonProps {
  children: React.ReactNode;
  className?: string;
  hoverContent?: React.ReactNode;
  variant?: 'default' | 'outline' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const variants = {
  default: 'bg-blue-600 text-white border-transparent',
  outline: 'bg-transparent text-blue-600 border-blue-600',
  gradient: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white border-transparent'
};

const sizes = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg'
};

export default function MorphingButton({
  children,
  className,
  hoverContent,
  variant = 'default',
  size = 'md',
  onClick
}: MorphingButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.button
      className={cn(
        'relative border-2 rounded-xl font-semibold transition-all duration-300 overflow-hidden',
        variants[variant],
        sizes[size],
        className
      )}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <AnimatePresence mode="wait">
        {!isHovered ? (
          <motion.div
            key="normal"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.div>
        ) : (
          <motion.div
            key="hover"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {hoverContent || children}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Morphing background effect */}
      <motion.div
        className="absolute inset-0 bg-white/20"
        initial={{ scale: 0, borderRadius: '50%' }}
        animate={isHovered ? { 
          scale: 1.5, 
          borderRadius: '0%' 
        } : { 
          scale: 0, 
          borderRadius: '50%' 
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.button>
  );
}