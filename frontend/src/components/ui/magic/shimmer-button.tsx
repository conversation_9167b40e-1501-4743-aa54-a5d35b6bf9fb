import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface ShimmerButtonProps {
  children: React.ReactNode;
  className?: string;
  shimmerColor?: string;
  backgroundColor?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export default function ShimmerButton({
  children,
  className,
  shimmerColor = '#ffffff',
  backgroundColor = 'rgba(255, 255, 255, 0.1)',
  onClick,
  disabled = false
}: ShimmerButtonProps) {
  return (
    <motion.button
      className={cn(
        'relative overflow-hidden rounded-lg px-4 py-2 font-medium transition-all',
        'backdrop-blur-sm border border-white/20',
        'hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed',
        className
      )}
      style={{ backgroundColor }}
      onClick={onClick}
      disabled={disabled}
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
    >
      <span className="relative z-10">{children}</span>
      
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 -top-2 -bottom-2"
        animate={{
          x: ['-100%', '100%'],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        <div
          className="h-full w-24 skew-x-12 opacity-30"
          style={{
            background: `linear-gradient(90deg, transparent, ${shimmerColor}, transparent)`,
          }}
        />
      </motion.div>
    </motion.button>
  );
}