// Utility functions for UI components
import { ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Enhanced cn function with better TypeScript support
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Animation utilities
export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 }
};

export const slideInLeft = {
  initial: { opacity: 0, x: -50 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 50 }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 }
};

// Glass effect generator
export function getGlassEffect(
  opacity: number = 0.1,
  blur: string = '8px',
  borderOpacity: number = 0.2
) {
  return {
    background: `rgba(255, 255, 255, ${opacity})`,
    backdropFilter: `blur(${blur}) saturate(180%) brightness(110%)`,
    border: `1px solid rgba(255, 255, 255, ${borderOpacity})`,
  };
}

// Gradient generator
export function createGradient(
  direction: string = '135deg',
  colors: string[] = ['#667eea', '#764ba2']
) {
  return `linear-gradient(${direction}, ${colors.join(', ')})`;
}

// Random utilities for animations
export function randomBetween(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

export function randomFromArray<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Responsive breakpoint utilities
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

export function getResponsiveValue<T>(
  values: Partial<Record<keyof typeof breakpoints | 'base', T>>,
  currentBreakpoint: keyof typeof breakpoints | 'base'
): T | undefined {
  return values[currentBreakpoint] || values.base;
}

// Color utilities
export const colors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  services: {
    velian: '#8b5cf6',
    zeroentropy: '#06b6d4',
    hello_cv: '#10b981',
    yoink_ui: '#f59e0b',
    clueso: '#ef4444',
    permut: '#8b5cf6',
    intervo: '#06b6d4',
    pixelesq: '#ec4899',
  }
};

// Theme utilities
export function getServiceColor(serviceName: keyof typeof colors.services): string {
  return colors.services[serviceName] || colors.primary[500];
}

// Performance utilities
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}