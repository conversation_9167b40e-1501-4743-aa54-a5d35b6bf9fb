/**
 * AI Services Configuration
 * Phase 1: Service Registry & Configuration
 * 
 * Centralized configuration for the 8 AI services with exact specifications
 * from the master project prompt
 */

import type { AIService } from '@/types/dashboard';

/**
 * The 8 AI Services - Exact specifications from master project prompt
 * This configuration drives the entire dashboard display and functionality
 */
export const AI_SERVICES: readonly AIService[] = [
  // ==================== PRIMARY SERVICES (Large Cards) ====================
  {
    id: 'velian',
    name: '<PERSON><PERSON><PERSON>',
    color: '#8B5CF6', // Purple
    category: 'workflow-automation',
    description: 'Advanced AI analytics platform',
    longDescription: 'Workflow automation and n8n deployment with multi-model workflow planning and automated deployment',
    status: 'active',
    metrics: {
      requests: 15234,
      uptime: 99.9,
      responseTime: 45,
      errorRate: 0.1,
      lastUpdated: new Date(),
      dailyGrowth: 12.5,
      monthlyGrowth: 34.2
    },
    capabilities: [
      { name: 'n8n Workflow Generation', description: 'Automated workflow creation' },
      { name: 'Multi-model Planning', description: 'Sophisticated planning algorithms' },
      { name: 'Version Control', description: 'Workflow versioning system' },
      { name: 'Deployment Automation', description: 'One-click deployment' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/velian',
      websocketChannel: 'velian_updates',
      healthCheckEndpoint: '/api/v1/health/velian',
      documentationUrl: '/docs/services/velian',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 2, rows: 2 }, // Featured large card
      tablet: { cols: 2, rows: 2 },
      mobile: { cols: 1, rows: 1 },
      priority: 1
    }
  },

  // ==================== SECONDARY SERVICES (Medium Cards) ====================
  {
    id: 'zeroentropy',
    name: 'ZeroEntropy',
    color: '#06B6D4', // Cyan
    category: 'optimization',
    description: 'Quantum optimization engine',
    longDescription: 'Quantum-powered optimization engine for complex computational problems with advanced algorithms',
    status: 'active',
    metrics: {
      requests: 12847,
      uptime: 99.8,
      responseTime: 52,
      errorRate: 0.2,
      lastUpdated: new Date(),
      dailyGrowth: 8.3,
      monthlyGrowth: 28.7
    },
    capabilities: [
      { name: 'Quantum Algorithms', description: 'Advanced quantum optimization' },
      { name: 'Complex Problem Solving', description: 'Multi-dimensional optimization' },
      { name: 'Real-time Processing', description: 'Live optimization results' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/zeroentropy',
      websocketChannel: 'zeroentropy_updates',
      healthCheckEndpoint: '/api/v1/health/zeroentropy',
      documentationUrl: '/docs/services/zeroentropy',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 2, rows: 1 }, // Medium card
      tablet: { cols: 2, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 2
    }
  },

  {
    id: 'hello_cv',
    name: 'Hello.cv',
    color: '#10B981', // Emerald
    category: 'content-analysis',
    description: 'Resume parsing and analysis',
    longDescription: 'AI-powered resume parser and CV analysis with intelligent insights and recommendations',
    status: 'active',
    metrics: {
      requests: 8921,
      uptime: 99.5,
      responseTime: 38,
      errorRate: 0.5,
      lastUpdated: new Date(),
      dailyGrowth: 15.7,
      monthlyGrowth: 42.1
    },
    capabilities: [
      { name: 'Resume Parsing', description: 'Extract structured data from CVs' },
      { name: 'Skill Analysis', description: 'AI-powered skill assessment' },
      { name: 'Match Scoring', description: 'Job compatibility scoring' },
      { name: 'Recommendations', description: 'Career improvement suggestions' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/hello-cv',
      websocketChannel: 'hello_cv_updates',
      healthCheckEndpoint: '/api/v1/health/hello-cv',
      documentationUrl: '/docs/services/hello-cv',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 2, rows: 1 }, // Medium card
      tablet: { cols: 2, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 3
    }
  },

  {
    id: 'yoink_ui',
    name: 'YoinkUI',
    color: '#F97316', // Orange
    category: 'ui-generation',
    description: 'UI component generator',
    longDescription: 'AI-powered UI/UX design automation tools for rapid prototyping and component generation',
    status: 'active',
    metrics: {
      requests: 6743,
      uptime: 99.9,
      responseTime: 41,
      errorRate: 0.1,
      lastUpdated: new Date(),
      dailyGrowth: 18.2,
      monthlyGrowth: 51.3
    },
    capabilities: [
      { name: 'Component Generation', description: 'AI-generated UI components' },
      { name: 'Design System Integration', description: 'Consistent design tokens' },
      { name: 'Code Export', description: 'Multiple framework support' },
      { name: 'Style Optimization', description: 'Performance-optimized CSS' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/yoink-ui',
      websocketChannel: 'yoink_ui_updates',
      healthCheckEndpoint: '/api/v1/health/yoink-ui',
      documentationUrl: '/docs/services/yoink-ui',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 2, rows: 1 }, // Medium card
      tablet: { cols: 2, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 4
    }
  },

  // ==================== COMPACT SERVICES (Small Cards) ====================
  {
    id: 'clueso',
    name: 'Clueso',
    color: '#EF4444', // Red
    category: 'search-intelligence',
    description: 'Intelligent search',
    longDescription: 'Advanced search and discovery engine with AI-powered relevance and contextual understanding',
    status: 'active', // Note: 98.5% uptime but still considered active
    metrics: {
      requests: 4521,
      uptime: 98.5,
      responseTime: 67,
      errorRate: 1.5,
      lastUpdated: new Date(),
      dailyGrowth: 5.1,
      monthlyGrowth: 19.8
    },
    capabilities: [
      { name: 'Semantic Search', description: 'Context-aware search results' },
      { name: 'Auto-complete', description: 'Intelligent query suggestions' },
      { name: 'Faceted Search', description: 'Multi-dimensional filtering' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/clueso',
      websocketChannel: 'clueso_updates',
      healthCheckEndpoint: '/api/v1/health/clueso',
      documentationUrl: '/docs/services/clueso',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 1, rows: 1 }, // Compact card
      tablet: { cols: 1, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 5
    }
  },

  {
    id: 'permut',
    name: 'Permut',
    color: '#8B5CF6', // Purple (same as Velian for related services)
    category: 'computational',
    description: 'Permutation engine',
    longDescription: 'Advanced permutation and combination engine for complex mathematical computations',
    status: 'active',
    metrics: {
      requests: 3892,
      uptime: 99.6,
      responseTime: 31,
      errorRate: 0.4,
      lastUpdated: new Date(),
      dailyGrowth: 7.8,
      monthlyGrowth: 23.4
    },
    capabilities: [
      { name: 'Combinatorial Analysis', description: 'Complex mathematical computations' },
      { name: 'Optimization Algorithms', description: 'Efficient solution finding' },
      { name: 'Batch Processing', description: 'High-volume calculations' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/permut',
      websocketChannel: 'permut_updates',
      healthCheckEndpoint: '/api/v1/health/permut',
      documentationUrl: '/docs/services/permut',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 1, rows: 1 }, // Compact card
      tablet: { cols: 1, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 6
    }
  },

  {
    id: 'intervo',
    name: 'Intervo',
    color: '#14B8A6', // Teal
    category: 'voice-ai',
    description: 'Voice AI assistant',
    longDescription: 'Interactive voice AI assistant platform with natural language processing and real-time responses',
    status: 'active',
    metrics: {
      requests: 5678,
      uptime: 99.8,
      responseTime: 125, // Higher latency for voice processing
      errorRate: 0.2,
      lastUpdated: new Date(),
      dailyGrowth: 22.1,
      monthlyGrowth: 67.3
    },
    capabilities: [
      { name: 'Voice Recognition', description: 'Advanced speech-to-text' },
      { name: 'Natural Language Processing', description: 'Context understanding' },
      { name: 'Real-time Responses', description: 'Low-latency voice generation' },
      { name: 'Multi-language Support', description: '15+ languages supported' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/intervo',
      websocketChannel: 'intervo_updates',
      healthCheckEndpoint: '/api/v1/health/intervo',
      documentationUrl: '/docs/services/intervo',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 1, rows: 1 }, // Compact card
      tablet: { cols: 1, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 7
    }
  },

  {
    id: 'pixelesq',
    name: 'Pixelesq',
    color: '#EC4899', // Pink
    category: 'image-processing',
    description: 'Image processing',
    longDescription: 'Advanced image processing and generation platform with AI-powered enhancement and creation tools',
    status: 'active',
    metrics: {
      requests: 7234,
      uptime: 99.7,
      responseTime: 89, // Higher latency for image processing
      errorRate: 0.3,
      lastUpdated: new Date(),
      dailyGrowth: 13.9,
      monthlyGrowth: 38.6
    },
    capabilities: [
      { name: 'Image Enhancement', description: 'AI-powered image optimization' },
      { name: 'Style Transfer', description: 'Artistic style application' },
      { name: 'Object Detection', description: 'Computer vision analysis' },
      { name: 'Format Conversion', description: 'Multi-format support' }
    ],
    integration: {
      apiEndpoint: '/api/v1/services/pixelesq',
      websocketChannel: 'pixelesq_updates',
      healthCheckEndpoint: '/api/v1/health/pixelesq',
      documentationUrl: '/docs/services/pixelesq',
      requiresAuth: true
    },
    gridPosition: {
      desktop: { cols: 1, rows: 1 }, // Compact card
      tablet: { cols: 1, rows: 1 },
      mobile: { cols: 1, rows: 1 },
      priority: 8
    }
  }
] as const;

/**
 * Service lookup by ID for quick access
 */
export const SERVICE_BY_ID = AI_SERVICES.reduce((acc, service) => {
  acc[service.id] = service;
  return acc;
}, {} as Record<string, AIService>);

/**
 * Services grouped by category for organized display
 */
export const SERVICES_BY_CATEGORY = AI_SERVICES.reduce((acc, service) => {
  if (!acc[service.category]) {
    acc[service.category] = [];
  }
  acc[service.category].push(service);
  return acc;
}, {} as Record<string, AIService[]>);

/**
 * Color palette extracted from services for consistent theming
 */
export const SERVICE_COLORS = AI_SERVICES.reduce((acc, service) => {
  acc[service.id] = service.color;
  return acc;
}, {} as Record<string, string>);

/**
 * Service status counts for dashboard overview
 */
export const getServiceStatusCounts = () => {
  return AI_SERVICES.reduce((acc, service) => {
    acc[service.status] = (acc[service.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
};

/**
 * Total metrics across all services
 */
export const getTotalMetrics = () => {
  return AI_SERVICES.reduce((acc, service) => {
    acc.totalRequests += service.metrics.requests;
    acc.averageUptime += service.metrics.uptime;
    acc.averageResponseTime += service.metrics.responseTime;
    acc.totalErrorRate += service.metrics.errorRate;
    return acc;
  }, {
    totalRequests: 0,
    averageUptime: 0,
    averageResponseTime: 0,
    totalErrorRate: 0
  });
};

/**
 * Configuration constants for dashboard behavior
 */
export const DASHBOARD_CONFIG = {
  // Real-time update intervals
  METRICS_UPDATE_INTERVAL: 30000, // 30 seconds
  HEALTH_CHECK_INTERVAL: 60000,   // 60 seconds
  
  // Animation settings
  ANIMATION_DURATION: 0.6,
  STAGGER_DELAY: 0.1,
  COUNTER_ANIMATION_DURATION: 2000,
  
  // Layout settings
  GRID_GAP: '1.5rem',
  CARD_MIN_HEIGHT: '200px',
  
  // Performance settings
  MAX_RETRIES: 3,
  DEBOUNCE_DELAY: 300,
  
  // Colors and theming
  AURORA_COLORS: ['#8B5CF6', '#06B6D4', '#EC4899'], // Purple, Cyan, Pink
  GLASS_OPACITY: 0.05,
  BACKDROP_BLUR: '20px'
} as const;

export default AI_SERVICES;