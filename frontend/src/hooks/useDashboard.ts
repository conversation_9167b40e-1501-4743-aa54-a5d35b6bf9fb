/**
 * Dashboard Hook - Data Flow Architecture  
 * Phase 1: Authentication Integration & State Management
 * 
 * Centralized hook for dashboard state management, real-time updates,
 * and authentication integration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '@/stores/authStore';
import type { 
  AIService, 
  DashboardState, 
  DashboardError,
  ServiceHealthResponse,
  MetricsUpdate,
  WebSocketMessage
} from '@/types/dashboard';
import { AI_SERVICES, DASHBOARD_CONFIG } from '@/config/services';

/**
 * Main dashboard hook - handles all dashboard state and operations
 */
export const useDashboard = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const wsRef = useRef<WebSocket | null>(null);
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const healthCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // ==================== STATE MANAGEMENT ====================

  const [dashboardState, setDashboardState] = useState<DashboardState>({
    services: AI_SERVICES.map(service => ({ ...service })),
    isLoading: true,
    error: null,
    lastRefresh: new Date(),
    realTimeUpdates: false,
    selectedService: null
  });

  // ==================== AUTHENTICATION CHECK ====================

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, navigate]);

  // ==================== REAL-TIME WEBSOCKET CONNECTION ====================

  const connectWebSocket = useCallback(() => {
    if (!isAuthenticated || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      // Connect to backend WebSocket for real-time updates
      wsRef.current = new WebSocket(`ws://localhost:3000/ws/dashboard`);
      
      wsRef.current.onopen = () => {
        console.log('🔌 Dashboard WebSocket connected');
        setDashboardState(prev => ({ 
          ...prev, 
          realTimeUpdates: true,
          error: null 
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('🔌 Dashboard WebSocket disconnected');
        setDashboardState(prev => ({ 
          ...prev, 
          realTimeUpdates: false 
        }));
        
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isAuthenticated) {
            connectWebSocket();
          }
        }, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setDashboardState(prev => ({
          ...prev,
          error: {
            code: 'WEBSOCKET_ERROR',
            message: 'Real-time connection failed',
            timestamp: new Date()
          }
        }));
      };
    } catch (error) {
      console.error('❌ Failed to connect WebSocket:', error);
    }
  }, [isAuthenticated]);

  // ==================== WEBSOCKET MESSAGE HANDLER ====================

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'metrics_update':
        handleMetricsUpdate(message.payload as MetricsUpdate);
        break;
        
      case 'service_status_update':
        handleServiceStatusUpdate(message.serviceId!, message.payload.status);
        break;
        
      case 'error_notification':
        handleErrorNotification(message.payload);
        break;
        
      case 'maintenance_alert':
        handleMaintenanceAlert(message.serviceId!, message.payload);
        break;
        
      default:
        console.log('🔔 Unknown WebSocket message type:', message.type);
    }
  }, []);

  // ==================== METRICS UPDATE HANDLER ====================

  const handleMetricsUpdate = useCallback((update: MetricsUpdate) => {
    setDashboardState(prev => ({
      ...prev,
      services: prev.services.map(service => 
        service.id === update.serviceId
          ? {
              ...service,
              metrics: {
                ...service.metrics,
                ...update.metrics,
                lastUpdated: update.timestamp
              }
            }
          : service
      ),
      lastRefresh: new Date()
    }));
  }, []);

  // ==================== SERVICE STATUS UPDATE HANDLER ====================

  const handleServiceStatusUpdate = useCallback((serviceId: string, status: string) => {
    setDashboardState(prev => ({
      ...prev,
      services: prev.services.map(service => 
        service.id === serviceId
          ? { ...service, status: status as any }
          : service
      )
    }));
  }, []);

  // ==================== ERROR NOTIFICATION HANDLER ====================

  const handleErrorNotification = useCallback((payload: any) => {
    setDashboardState(prev => ({
      ...prev,
      error: {
        code: payload.code || 'UNKNOWN_ERROR',
        message: payload.message || 'An error occurred',
        details: payload.details,
        timestamp: new Date()
      }
    }));
  }, []);

  // ==================== MAINTENANCE ALERT HANDLER ====================

  const handleMaintenanceAlert = useCallback((serviceId: string, payload: any) => {
    console.log(`🔧 Maintenance alert for ${serviceId}:`, payload);
    // Could trigger toast notifications or UI updates here
  }, []);

  // ==================== DATA FETCHING ====================

  const fetchServiceHealth = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const response = await fetch('/api/v1/services/health', {
        headers: {
          'Authorization': `Bearer ${user?.token}`, // Assuming token in user object
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const healthData: ServiceHealthResponse[] = await response.json();
      
      setDashboardState(prev => ({
        ...prev,
        services: prev.services.map(service => {
          const healthInfo = healthData.find(h => h.serviceId === service.id);
          return healthInfo ? {
            ...service,
            status: healthInfo.status,
            metrics: {
              ...service.metrics,
              ...healthInfo.metrics
            }
          } : service;
        }),
        isLoading: false,
        lastRefresh: new Date()
      }));
    } catch (error) {
      console.error('❌ Failed to fetch service health:', error);
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        error: {
          code: 'HEALTH_CHECK_FAILED',
          message: 'Failed to fetch service health data',
          timestamp: new Date()
        }
      }));
    }
  }, [isAuthenticated, user]);

  // ==================== PERIODIC UPDATES ====================

  const startPeriodicUpdates = useCallback(() => {
    // Health check interval
    if (healthCheckIntervalRef.current) {
      clearInterval(healthCheckIntervalRef.current);
    }
    healthCheckIntervalRef.current = setInterval(
      fetchServiceHealth,
      DASHBOARD_CONFIG.HEALTH_CHECK_INTERVAL
    );

    // Metrics update interval (if no WebSocket)
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
      metricsIntervalRef.current = setInterval(
        fetchServiceHealth,
        DASHBOARD_CONFIG.METRICS_UPDATE_INTERVAL
      );
    }
  }, [fetchServiceHealth]);

  const stopPeriodicUpdates = useCallback(() => {
    if (healthCheckIntervalRef.current) {
      clearInterval(healthCheckIntervalRef.current);
    }
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
    }
  }, []);

  // ==================== LIFECYCLE MANAGEMENT ====================

  useEffect(() => {
    if (!isAuthenticated) return;

    // Initial data fetch
    fetchServiceHealth();
    
    // Start periodic updates
    startPeriodicUpdates();
    
    // Connect WebSocket for real-time updates
    connectWebSocket();

    // Cleanup on unmount
    return () => {
      stopPeriodicUpdates();
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [isAuthenticated, fetchServiceHealth, startPeriodicUpdates, stopPeriodicUpdates, connectWebSocket]);

  // ==================== MANUAL REFRESH ====================

  const refreshDashboard = useCallback(async () => {
    setDashboardState(prev => ({ ...prev, isLoading: true, error: null }));
    await fetchServiceHealth();
  }, [fetchServiceHealth]);

  // ==================== SERVICE SELECTION ====================

  const selectService = useCallback((serviceId: string | null) => {
    setDashboardState(prev => ({
      ...prev,
      selectedService: serviceId
    }));
  }, []);

  // ==================== ERROR DISMISSAL ====================

  const dismissError = useCallback(() => {
    setDashboardState(prev => ({
      ...prev,
      error: null
    }));
  }, []);

  // ==================== COMPUTED VALUES ====================

  const activeServices = dashboardState.services.filter(s => s.status === 'active');
  const totalRequests = dashboardState.services.reduce((sum, s) => sum + s.metrics.requests, 0);
  const averageUptime = dashboardState.services.reduce((sum, s) => sum + s.metrics.uptime, 0) / dashboardState.services.length;

  // ==================== RETURN HOOK INTERFACE ====================

  return {
    // State
    services: dashboardState.services,
    isLoading: dashboardState.isLoading,
    error: dashboardState.error,
    lastRefresh: dashboardState.lastRefresh,
    realTimeUpdates: dashboardState.realTimeUpdates,
    selectedService: dashboardState.selectedService,
    
    // Actions
    refreshDashboard,
    selectService,
    dismissError,
    
    // Computed values
    activeServices,
    totalRequests,
    averageUptime,
    totalServices: dashboardState.services.length,
    
    // User data
    user,
    isAuthenticated
  };
};

/**
 * Hook for individual service operations
 */
export const useService = (serviceId: string) => {
  const { services, selectService } = useDashboard();
  
  const service = services.find(s => s.id === serviceId);
  
  const navigateToService = useCallback(() => {
    if (service) {
      selectService(serviceId);
      // Could navigate to service detail page
      // navigate(`/services/${serviceId}`);
    }
  }, [service, serviceId, selectService]);

  return {
    service,
    navigateToService,
    isLoading: !service,
    status: service?.status,
    metrics: service?.metrics
  };
};