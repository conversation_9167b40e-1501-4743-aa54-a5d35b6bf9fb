import React from 'react';
import useAuthStore from '@/stores/authStore';
import { useNavigate } from 'react-router-dom';

console.log('Modern Dashboard component loaded');

function Dashboard() {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  
  console.log('Dashboard rendering, user:', user);
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      {/* Test div to verify Tailwind works */}
      <div className="bg-blue-500 text-white p-4 m-4 rounded">
        If this shows blue background, Tailwind works
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold mb-8">
          Welcome back, {user?.first_name || 'User'}!
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-semibold mb-4">AI Services</h2>
            <p className="text-gray-300">Manage your AI integrations</p>
            <button 
              onClick={() => navigate('/services')}
              className="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors"
            >
              View Services
            </button>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-semibold mb-4">Workflows</h2>
            <p className="text-gray-300">Create and manage workflows</p>
            <button 
              onClick={() => navigate('/workflows')}
              className="mt-4 bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors"
            >
              Create Workflow
            </button>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-semibold mb-4">API Keys</h2>
            <p className="text-gray-300">Manage authentication keys</p>
            <button 
              onClick={() => navigate('/api-keys')}
              className="mt-4 bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded transition-colors"
            >
              Manage Keys
            </button>
          </div>
        </div>
        
        <div className="mt-8 bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
          <h2 className="text-2xl font-semibold mb-4">Quick Stats</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">8</div>
              <div className="text-sm text-gray-400">Services</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400">12</div>
              <div className="text-sm text-gray-400">Active Workflows</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">156</div>
              <div className="text-sm text-gray-400">API Calls Today</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">99.9%</div>
              <div className="text-sm text-gray-400">Uptime</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;