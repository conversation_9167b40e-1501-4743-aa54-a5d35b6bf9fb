import React, { useState, useEffect, useRef } from 'react';
import useAuthStore from '@/stores/authStore';
import { useNavigate } from 'react-router-dom';

interface Service {
  name: string;
  color: string;
  requests: number;
  uptime: number;
  description: string;
}

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  // CORRECT DATA VALUES FROM YOUR SPECS
  const [services] = useState<Service[]>([
    { name: '<PERSON><PERSON><PERSON>', color: '#8B5CF6', requests: 15234, uptime: 99.9, description: 'Advanced AI analytics platform' },
    { name: 'ZeroEntropy', color: '#06B6D4', requests: 12847, uptime: 99.8, description: 'Quantum optimization engine' },
    { name: 'Hello.cv', color: '#10B981', requests: 8921, uptime: 99.5, description: 'Resume parsing and analysis' },    { name: '<PERSON>ink<PERSON>', color: '#F97316', requests: 6743, uptime: 99.9, description: 'UI component generator' },
    { name: '<PERSON><PERSON><PERSON>', color: '#EF4444', requests: 4521, uptime: 98.5, description: 'Intelligent search' },
    { name: 'Permut', color: '#8B5CF6', requests: 3892, uptime: 99.6, description: 'Permutation engine' },
    { name: 'Intervo', color: '#14B8A6', requests: 5678, uptime: 99.8, description: 'Voice AI assistant' },
    { name: 'Pixelesq', color: '#EC4899', requests: 7234, uptime: 99.7, description: 'Image processing' }
  ]);

  const [animatedRequests, setAnimatedRequests] = useState<number[]>(services.map(() => 0));
  const animationRef = useRef<number[]>(services.map(() => 0));

  useEffect(() => {
    const animate = () => {
      animationRef.current = animationRef.current.map((current, index) => {
        const target = services[index].requests;
        const step = (target - current) * 0.1;
        return current + step;
      });
      
      setAnimatedRequests([...animationRef.current]);
      
      if (animationRef.current.some((current, index) => Math.abs(current - services[index].requests) > 1)) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }, [services]);
  const getGridClass = (index: number) => {
    switch (index) {
      case 0: return 'col-span-2 row-span-2'; // Velian - hero card
      case 1: case 2: case 3: return 'col-span-2 row-span-1'; // Wide cards
      default: return 'col-span-1 row-span-1'; // Small cards
    }
  };

  const CircularProgress: React.FC<{ percentage: number; color: string }> = ({ percentage, color }) => {
    const radius = 20;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <svg className="w-12 h-12 transform -rotate-90">
        <circle
          cx="24"
          cy="24"
          r={radius}
          stroke="rgba(255,255,255,0.1)"
          strokeWidth="4"
          fill="none"
        />
        <circle
          cx="24"
          cy="24"
          r={radius}
          stroke={color}
          strokeWidth="4"
          fill="none"          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-1000 ease-out"
        />
      </svg>
    );
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-[#0F0F23] to-[#1A1A2E]">
      {/* Aurora Effect */}
      <div className="absolute inset-0">
        <div className="absolute top-[-20%] left-[-10%] w-96 h-96 bg-purple-500/30 rounded-full blur-[40px] animate-pulse" />
        <div className="absolute top-[40%] right-[-10%] w-96 h-96 bg-blue-500/30 rounded-full blur-[40px] animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-[-20%] left-[30%] w-96 h-96 bg-pink-500/30 rounded-full blur-[40px] animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-8">
        <h1 className="text-5xl font-bold mb-2 bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
          Welcome back, {user?.name || 'User'}!
        </h1>
        <p className="text-gray-400 mb-8">Your AI services are running smoothly</p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 max-w-7xl">
          {services.map((service, index) => (
            <div              key={service.name}
              className={`
                ${getGridClass(index)} 
                relative
                bg-white/5
                backdrop-blur-xl
                rounded-2xl 
                border 
                border-white/10
                hover:border-white/20
                p-6 
                cursor-pointer 
                transition-all 
                duration-300 
                hover:scale-[1.02] 
                hover:shadow-[0_0_30px_rgba(139,92,246,0.3)]
                group
              `}
              style={{
                animationDelay: `${index * 0.2}s`,
                animation: 'float 6s ease-in-out infinite',
                '--hover-color': service.color
              } as React.CSSProperties}
            >
              <div className="h-full flex flex-col justify-between">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-white">{service.name}</h3>
                    <div 
                      className="w-3 h-3 rounded-full animate-pulse"
                      style={{ backgroundColor: service.color }}                    />
                  </div>
                  <p className="text-gray-400 text-sm mb-4">{service.description}</p>
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-gray-400 text-xs uppercase tracking-wider">Requests</p>
                    <p className="text-2xl font-bold text-white">
                      {Math.floor(animatedRequests[index]).toLocaleString()}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs uppercase tracking-wider">Uptime</p>
                      <p className="text-lg font-semibold text-white">{service.uptime}%</p>
                    </div>
                    <CircularProgress percentage={service.uptime} color={service.color} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add inline styles for animations */}
      <style>{`        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        
        .group:hover {
          box-shadow: 0 0 30px var(--hover-color);
        }
      `}</style>
    </div>
  );
};

export default Dashboard;