import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const services = [
    {
      id: 'velian',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Advanced AI analytics platform',
      color: '#8B5CF6',
      requests: 15234,
      uptime: '99.9%',
      gridClass: 'col-span-1 md:col-span-2 lg:col-span-4 lg:row-span-2'
    },
    {
      id: 'zeroentropy',
      name: 'ZeroEntropy',
      description: 'Quantum optimization engine',
      color: '#06B6D4',
      requests: 12847,
      uptime: '99.8%',
      gridClass: 'col-span-1 md:col-span-1 lg:col-span-2'
    }];    {
      id: 'hellocv',
      name: 'Hello.cv',
      description: 'Resume parsing and analysis',
      color: '#10B981',
      requests: 8921,
      uptime: '99.5%',
      gridClass: 'col-span-1 md:col-span-1 lg:col-span-2'
    },
    {
      id: 'yoinkui',
      name: '<PERSON><PERSON><PERSON>',
      description: 'UI component generator',
      color: '#F97316',
      requests: 6743,
      uptime: '99.9%',
      gridClass: 'col-span-1 md:col-span-2 lg:col-span-2'
    },
    {
      id: 'clueso',
      name: 'Clueso',
      description: 'Intelligent search',
      color: '#EF4444',
      requests: 4521,
      uptime: '98.5%',
      gridClass: 'col-span-1 md:col-span-1 lg:col-span-1'
    },
    {
      id: 'permut',
      name: 'Permut',
      description: 'Permutation engine'