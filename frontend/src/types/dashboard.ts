/**
 * Dashboard Architecture - TypeScript Interfaces
 * Phase 1: UI Design System Architecture
 * 
 * Architectural foundation for enterprise AI Services Dashboard
 */

// ==================== CORE SERVICE INTERFACES ====================

/**
 * Core AI Service interface based on the 8 integrated services
 * Maps to backend service registry and real-time metrics
 */
export interface AIService {
  readonly id: string;
  readonly name: string;
  readonly color: string;
  readonly category: ServiceCategory;
  readonly description: string;
  readonly longDescription?: string;
  readonly status: ServiceStatus;
  readonly metrics: ServiceMetrics;
  readonly capabilities: ServiceCapability[];
  readonly integration: ServiceIntegration;
  readonly gridPosition: GridPosition;
}

/**
 * Service categories for logical grouping
 */
export type ServiceCategory = 
  | 'workflow-automation'
  | 'optimization'  
  | 'content-analysis'
  | 'ui-generation'
  | 'search-intelligence'
  | 'computational'
  | 'voice-ai'
  | 'image-processing';

/**
 * Real-time service status from backend health checks
 */
export type ServiceStatus = 
  | 'active'
  | 'maintenance' 
  | 'error'
  | 'offline';

/**
 * Service metrics from backend analytics
 */
export interface ServiceMetrics {
  readonly requests: number;
  readonly uptime: number;
  readonly responseTime: number;
  readonly errorRate: number;
  readonly lastUpdated: Date;
  readonly dailyGrowth?: number;
  readonly monthlyGrowth?: number;
}

/**
 * Service capabilities for feature showcasing
 */
export interface ServiceCapability {
  readonly name: string;
  readonly description: string;
  readonly isNew?: boolean;
}

/**
 * Backend integration configuration
 */
export interface ServiceIntegration {
  readonly apiEndpoint: string;
  readonly websocketChannel?: string;
  readonly healthCheckEndpoint: string;
  readonly documentationUrl: string;
  readonly requiresAuth: boolean;
}

/**
 * Bento grid positioning for responsive layout
 */
export interface GridPosition {
  readonly desktop: GridSize;
  readonly tablet: GridSize;
  readonly mobile: GridSize;
  readonly priority: number; // 1 = highest priority (largest card)
}

export interface GridSize {
  readonly cols: number;
  readonly rows: number;
}

// ==================== AUTHENTICATION INTERFACES ====================

/**
 * User authentication state from auth store
 */
export interface AuthenticatedUser {
  readonly id: string;
  readonly email: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly role: UserRole;
  readonly subscription: UserSubscription;
  readonly preferences: UserPreferences;
}

export type UserRole = 'admin' | 'user' | 'enterprise';

export interface UserSubscription {
  readonly tier: 'free' | 'pro' | 'enterprise';
  readonly status: 'active' | 'canceled' | 'past_due';
  readonly expiresAt: Date;
  readonly features: string[];
}

export interface UserPreferences {
  readonly theme: 'light' | 'dark' | 'system';
  readonly animations: boolean;
  readonly notifications: boolean;
  readonly defaultView: 'grid' | 'list';
}

// ==================== DASHBOARD STATE INTERFACES ====================

/**
 * Main dashboard state management
 */
export interface DashboardState {
  readonly services: AIService[];
  readonly isLoading: boolean;
  readonly error: DashboardError | null;
  readonly lastRefresh: Date;
  readonly realTimeUpdates: boolean;
  readonly selectedService: string | null;
}

/**
 * Error handling for dashboard operations
 */
export interface DashboardError {
  readonly code: string;
  readonly message: string;
  readonly details?: Record<string, any>;
  readonly timestamp: Date;
}

// ==================== REAL-TIME UPDATE INTERFACES ====================

/**
 * WebSocket message types for real-time updates
 */
export interface WebSocketMessage {
  readonly type: MessageType;
  readonly serviceId?: string;
  readonly payload: Record<string, any>;
  readonly timestamp: Date;
}

export type MessageType = 
  | 'service_status_update'
  | 'metrics_update'
  | 'error_notification'
  | 'maintenance_alert';

/**
 * Real-time metrics update payload
 */
export interface MetricsUpdate {
  readonly serviceId: string;
  readonly metrics: Partial<ServiceMetrics>;
  readonly timestamp: Date;
}

// ==================== COMPONENT PROP INTERFACES ====================

/**
 * Service card component props
 */
export interface ServiceCardProps {
  readonly service: AIService;
  readonly isLoading?: boolean;
  readonly onClick?: (service: AIService) => void;
  readonly className?: string;
  readonly animationDelay?: number;
}

/**
 * Metrics display component props
 */
export interface MetricsDisplayProps {
  readonly metrics: ServiceMetrics;
  readonly compact?: boolean;
  readonly showTrends?: boolean;
  readonly animationDuration?: number;
}

/**
 * Dashboard header component props
 */
export interface DashboardHeaderProps {
  readonly user: AuthenticatedUser;
  readonly totalServices: number;
  readonly onRefresh?: () => void;
  readonly onSettingsClick?: () => void;
}

// ==================== ANIMATION INTERFACES ====================

/**
 * Animation configuration for Framer Motion
 */
export interface AnimationConfig {
  readonly duration: number;
  readonly delay?: number;
  readonly ease: string;
  readonly repeat?: number;
}

/**
 * Card animation variants
 */
export interface CardAnimationVariants {
  readonly initial: Record<string, any>;
  readonly animate: Record<string, any>;
  readonly hover: Record<string, any>;
  readonly tap?: Record<string, any>;
}

// ==================== RESPONSIVE LAYOUT INTERFACES ====================

/**
 * Breakpoint configuration
 */
export interface Breakpoints {
  readonly mobile: number;
  readonly tablet: number;
  readonly desktop: number;
  readonly wide: number;
}

/**
 * Layout configuration for different screen sizes
 */
export interface LayoutConfig {
  readonly grid: {
    readonly mobile: { cols: number; gap: string };
    readonly tablet: { cols: number; gap: string };
    readonly desktop: { cols: number; gap: string };
  };
  readonly spacing: {
    readonly mobile: string;
    readonly tablet: string; 
    readonly desktop: string;
  };
}

// ==================== UTILITY TYPES ====================

/**
 * Color palette for services and UI elements
 */
export interface ColorPalette {
  readonly primary: string;
  readonly secondary: string;
  readonly accent: string;
  readonly background: string;
  readonly surface: string;
  readonly text: string;
  readonly services: Record<string, string>;
}

/**
 * Dashboard configuration constants
 */
export interface DashboardConfig {
  readonly refreshInterval: number;
  readonly animationDuration: number;
  readonly maxRetries: number;
  readonly debounceDelay: number;
  readonly colors: ColorPalette;
  readonly layout: LayoutConfig;
  readonly breakpoints: Breakpoints;
}

// ==================== API RESPONSE INTERFACES ====================

/**
 * Backend API response structure
 */
export interface ApiResponse<T> {
  readonly success: boolean;
  readonly data: T;
  readonly error?: {
    readonly code: string;
    readonly message: string;
  };
  readonly timestamp: string;
}

/**
 * Service health check response
 */
export interface ServiceHealthResponse {
  readonly serviceId: string;
  readonly status: ServiceStatus;
  readonly metrics: ServiceMetrics;
  readonly lastChecked: string;
}

// ==================== EXPORT ALL TYPES ====================

export type {
  // Core types
  ServiceCategory,
  ServiceStatus,
  MessageType,
  UserRole,
  
  // Main interfaces  
  AIService,
  AuthenticatedUser,
  DashboardState,
  DashboardError,
  WebSocketMessage,
  
  // Component props
  ServiceCardProps,
  MetricsDisplayProps,
  DashboardHeaderProps,
  
  // Configuration
  DashboardConfig,
  AnimationConfig,
  
  // API responses
  ApiResponse,
  ServiceHealthResponse
};