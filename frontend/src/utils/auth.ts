import type { User } from '@shared/types';

const AUTH_STORAGE_KEY = 'auth_user';
const TOKEN_STORAGE_KEY = 'auth_token';

export interface AuthData {
  user: User;
  token: string;
}

export const authUtils = {
  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    const user = localStorage.getItem(AUTH_STORAGE_KEY);
    const token = localStorage.getItem(TOKEN_STORAGE_KEY);
    return !!(user && token);
  },

  /**
   * Save authentication data to localStorage
   */
  setAuth(user: User, token: string): void {
    localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(user));
    localStorage.setItem(TOKEN_STORAGE_KEY, token);
  },

  /**
   * Clear authentication data from localStorage
   */
  clearAuth(): void {
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  },

  /**
   * Get current authenticated user
   */
  getUser(): User | null {
    try {
      const userStr = localStorage.getItem(AUTH_STORAGE_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing user from localStorage:', error);
      return null;
    }
  },

  /**
   * Get current authentication token
   */
  getToken(): string | null {
    return localStorage.getItem(TOKEN_STORAGE_KEY);
  },

  /**
   * Get complete auth data
   */
  getAuthData(): AuthData | null {
    const user = this.getUser();
    const token = this.getToken();
    return user && token ? { user, token } : null;
  }
};

export default authUtils;