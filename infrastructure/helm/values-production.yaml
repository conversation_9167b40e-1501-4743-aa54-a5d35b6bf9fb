# Production Environment Values for Helm Charts

global:
  environment: production
  namespace: ss-platform-production
  imageTag: latest
  registry: ghcr.io
  
# Application Configuration
app:
  name: ss-platform
  replicas: 5  # Higher replica count for production
  
  # Resource limits for production
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "2000m"
  
  # Environment variables
  env:
    NODE_ENV: production
    LOG_LEVEL: info
    RATE_LIMIT_WINDOW: "900000"  # 15 minutes
    RATE_LIMIT_MAX: "2000"  # Higher limits for production

# Database Configuration
database:
  host: ss-platform-prod-db.cluster-xyz.us-east-1.rds.amazonaws.com
  port: 5432
  name: ss_platform_production
  ssl: true
  poolMin: 5
  poolMax: 25
  
  # Production-specific database settings
  readReplica:
    enabled: true
    host: ss-platform-prod-db-ro.cluster-xyz.us-east-1.rds.amazonaws.com

# Redis Configuration  
redis:
  host: ss-platform-prod-cache.xyz.cache.amazonaws.com
  port: 6379
  ssl: true
  
  # Redis Cluster for production
  cluster:
    enabled: true
    nodes:
      - ss-platform-prod-cache-001.xyz.cache.amazonaws.com
      - ss-platform-prod-cache-002.xyz.cache.amazonaws.com
      - ss-platform-prod-cache-003.xyz.cache.amazonaws.com
    
# Service Configuration
service:
  type: ClusterIP
  ports:
    backend: 3000
    frontend: 3001
  
  # Production service annotations
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http

# Ingress Configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "500"
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options DENY;
      add_header X-Content-Type-Options nosniff;
      add_header X-XSS-Protection "1; mode=block";
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  hosts:
    - host: api.aiservices.com
      paths:
        - path: /api
          pathType: Prefix
          service: backend
        - path: /
          pathType: Prefix  
          service: frontend
  tls:
    - secretName: production-tls-secret
      hosts:
        - api.aiservices.com

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 15s  # More frequent monitoring in production
    path: /metrics
  
  prometheusRule:
    enabled: true
    rules:
      # Critical alerts for production
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected in production
          description: Error rate is {{ $value }} errors per second
          
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High response time in production
          description: 95th percentile response time is {{ $value }} seconds
          
      - alert: DatabaseConnectionFailure
        expr: up{job="database"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Database connection failure
          description: Cannot connect to production database
          
      - alert: MemoryUsageHigh
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: High memory usage
          description: Memory usage is above 85%
          
      - alert: CPUUsageHigh
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: High CPU usage
          description: CPU usage is above 80%

# Autoscaling
autoscaling:
  enabled: true
  minReplicas: 5
  maxReplicas: 20  # Higher scaling limits for production
  targetCPUUtilizationPercentage: 60  # More conservative CPU target
  targetMemoryUtilizationPercentage: 70
  
  # Advanced autoscaling metrics
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

# Security
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true
    
  # Production security policies
  podSecurityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 2000
    
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL
    readOnlyRootFilesystem: true
  
  # RBAC
  rbac:
    create: true
    
# Storage
persistence:
  enabled: true
  storageClass: gp3  # Faster storage for production
  size: 100Gi  # Larger storage
  
  # Backup storage
  backup:
    storageClass: standard
    size: 500Gi

# Backup Configuration
backup:
  enabled: true
  schedule: "0 1 * * *"  # Daily at 1 AM
  retention: 30  # Keep 30 daily backups
  
  # Additional backup schedules
  weekly:
    enabled: true
    schedule: "0 1 * * 0"  # Weekly on Sunday
    retention: 12  # Keep 12 weekly backups
    
  monthly:
    enabled: true
    schedule: "0 1 1 * *"  # Monthly on 1st
    retention: 12  # Keep 12 monthly backups

# Disaster Recovery
disasterRecovery:
  enabled: true
  
  # Cross-region replication
  replication:
    enabled: true
    targetRegion: us-west-2
    
  # Point-in-time recovery
  pointInTimeRecovery:
    enabled: true
    retentionPeriod: 7  # 7 days

# Performance Optimization
performance:
  # CDN Configuration
  cdn:
    enabled: true
    provider: cloudfront
    
  # Caching
  caching:
    enabled: true
    redis:
      ttl: 3600  # 1 hour default TTL
    
  # Connection pooling
  connectionPooling:
    enabled: true
    maxConnections: 100

# Compliance and Auditing
compliance:
  # Audit logging
  auditLogs:
    enabled: true
    retention: 90  # 90 days
    
  # Data encryption
  encryption:
    enabled: true
    atRest: true
    inTransit: true
    
  # GDPR compliance
  gdpr:
    enabled: true
    dataRetentionDays: 365