# Staging Environment Values for Helm Charts

global:
  environment: staging
  namespace: ss-platform-staging
  imageTag: latest
  registry: ghcr.io
  
# Application Configuration
app:
  name: ss-platform
  replicas: 2
  
  # Resource limits for staging
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "512Mi"
      cpu: "500m"
  
  # Environment variables
  env:
    NODE_ENV: staging
    LOG_LEVEL: debug
    RATE_LIMIT_WINDOW: "900000"  # 15 minutes
    RATE_LIMIT_MAX: "1000"

# Database Configuration
database:
  host: ss-platform-staging-db.cluster-xyz.us-east-1.rds.amazonaws.com
  port: 5432
  name: ss_platform_staging
  ssl: true
  poolMin: 2
  poolMax: 10

# Redis Configuration  
redis:
  host: ss-platform-staging-cache.xyz.cache.amazonaws.com
  port: 6379
  ssl: true
  
# Service Configuration
service:
  type: ClusterIP
  ports:
    backend: 3000
    frontend: 3001

# Ingress Configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-staging
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
  hosts:
    - host: staging-api.aiservices.com
      paths:
        - path: /api
          pathType: Prefix
          service: backend
        - path: /
          pathType: Prefix  
          service: frontend
  tls:
    - secretName: staging-tls-secret
      hosts:
        - staging-api.aiservices.com

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics
  
  prometheusRule:
    enabled: true
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High error rate detected in staging
          
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: High response time in staging

# Autoscaling
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 8
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Security
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true
  
  # RBAC
  rbac:
    create: true
    
# Storage
persistence:
  enabled: true
  storageClass: gp2
  size: 20Gi

# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: 7  # Keep 7 daily backups