#!/bin/bash

# <PERSON><PERSON><PERSON> to run Crush with proper authentication
echo "🚀 Starting Crush with Claude Max authentication..."

# Check if session token is set
if [ -z "$ANTHROPIC_SESSION_TOKEN" ]; then
    echo "⚠️  ANTHROPIC_SESSION_TOKEN not set."
    echo ""
    echo "Please set your session token first:"
    echo "export ANTHROPIC_SESSION_TOKEN=\"your_session_token_here\""
    echo ""
    echo "Or add it to ~/.zprofile for permanent setup:"
    echo "echo 'export ANTHROPIC_SESSION_TOKEN=\"your_token\"' >> ~/.zprofile"
    echo ""
    read -p "Do you want to set it now? (y/n): " set_now
    
    if [[ $set_now == "y" || $set_now == "Y" ]]; then
        echo ""
        read -s -p "Paste your session token: " token
        export ANTHROPIC_SESSION_TOKEN="$token"
        echo ""
        echo "✅ Session token set for this session!"
    else
        echo "Please set the token and run this script again."
        exit 1
    fi
fi

# Navigate to project directory
cd /Users/<USER>/development/ss_site

# Run Crush
echo "Starting Crush..."
~/go/bin/crush
