#!/bin/bash

echo "🔧 Setting up Crush authentication for Claude Max account..."
echo ""
echo "Please follow these steps to get your session token:"
echo ""
echo "1. Open https://claude.ai in your browser"
echo "2. Make sure you're logged in to your Claude Max account"
echo "3. Press F12 to open Developer Tools"
echo "4. Go to Application tab → Cookies → https://claude.ai"
echo "5. Find the 'sessionKey' cookie and copy its value"
echo ""
echo "Once you have the session token, we'll set it up for Crush."
echo ""

read -p "Do you have your session token ready? (y/n): " ready

if [[ $ready == "y" || $ready == "Y" ]]; then
    echo ""
    read -s -p "Paste your session token here: " session_token
    echo ""
    
    # Add to .zprofile for permanent setup
    echo "" >> ~/.zprofile
    echo "# Claude session token for Crush" >> ~/.zprofile
    echo "export ANTHROPIC_SESSION_TOKEN=\"$session_token\"" >> ~/.zprofile
    
    # Also add to current session
    export ANTHROPIC_SESSION_TOKEN="$session_token"
    
    echo "✅ Session token has been set up!"
    echo "✅ Added to ~/.zprofile for future sessions"
    echo ""
    echo "Now let's test Crush with your Claude Max account:"
    echo ""
    
    # Test crush
    echo "Starting Crush..."
    ~/go/bin/crush
    
else
    echo ""
    echo "No problem! Here's what you need to do:"
    echo ""
    echo "1. Go to https://claude.ai and log in"
    echo "2. Open Developer Tools (F12)"
    echo "3. Application tab → Cookies → https://claude.ai"
    echo "4. Copy the 'sessionKey' value"
    echo "5. Run this script again"
    echo ""
    echo "Alternative: You can also set it manually:"
    echo "export ANTHROPIC_SESSION_TOKEN=\"your_session_token_here\""
fi
