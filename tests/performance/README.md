# Performance Testing Suite

Comprehensive performance benchmarking and testing suite for the AI Services Platform at `/Users/<USER>/development/ss_site`.

## 🎯 Overview

This performance testing suite provides:

- **Baseline Performance Metrics** - Establish performance baselines for all API endpoints
- **Automated Performance Regression Testing** - Detect performance degradations
- **Real-time Performance Monitoring** - Continuous system health monitoring  
- **Load Testing Scenarios** - Test system behavior under various load conditions
- **Stress Testing** - Find breaking points and system limits
- **Chaos Engineering** - Test system resilience under failure conditions
- **Memory Leak Detection** - Monitor and detect memory issues
- **Performance Budgets** - Enforce performance SLAs
- **Comprehensive Reporting** - Visual dashboards and detailed analysis

## 📊 Test Results

### Current Baseline Metrics
*(Generated automatically and updated by CI/CD pipeline)*

| Endpoint | P95 Response Time | Throughput | Error Rate | Status |
|----------|-------------------|------------|------------|--------|
| `/health` | ~50ms | 1000+ req/sec | <0.1% | ✅ |
| `/api/v1/auth/login` | ~200ms | 100+ req/sec | <1% | ✅ |
| `/api/v1/services/execute` | ~1000ms | 50+ req/sec | <2% | ✅ |
| `/api/v1/workflows/execute` | ~3000ms | 10+ req/sec | <2% | ✅ |
| `/api/v1/analytics/overview` | ~500ms | 50+ req/sec | <0.5% | ✅ |

## 🚀 Quick Start

### Prerequisites

```bash
# Install dependencies
npm install

# Ensure test databases are setup
npm run db:migrate
npm run db:seed

# Start the backend server
npm run dev:backend
```

### Running Tests

```bash
# Run all performance tests
npm run test:performance

# Run specific test categories
npm run performance:baseline     # Baseline metrics collection
npm run test:stress             # Stress testing
npm run test:chaos              # Chaos engineering
npm run test:k6                 # K6 load testing
npm run test:load               # Artillery load testing

# Generate comprehensive report
npm run performance:report

# Start real-time monitoring
npm run performance:monitor
```

## 📁 Directory Structure

```
tests/performance/
├── config/
│   └── performance-config.ts     # Central configuration
├── metrics/
│   └── baseline-collector.ts     # Baseline metrics collection
├── scenarios/
│   ├── artillery-advanced.yml    # Artillery load test scenarios
│   └── k6-load-test.js          # K6 load test scenarios
├── scripts/
│   ├── baseline-metrics.ts       # Baseline collection script
│   ├── stress-test.ts            # Stress testing script
│   ├── chaos-test.ts             # Chaos engineering script
│   ├── performance-monitor.ts    # Real-time monitoring
│   ├── generate-report.ts        # Report generation
│   └── advanced-processors.js    # Artillery processors
├── data/
│   ├── test-users.csv            # Test user data
│   ├── test-services.csv         # Test service data
│   └── test-workflows.csv        # Test workflow data
├── reports/                      # Generated reports (gitignored)
├── *.test.ts                     # Jest performance tests
├── jest.config.js               # Jest configuration
├── jest.setup.js                # Jest setup utilities
└── README.md                    # This file
```

## 🔧 Configuration

### Performance Thresholds

Configure performance thresholds in `config/performance-config.ts`:

```typescript
export const PERFORMANCE_CONFIG = {
  // Default thresholds
  defaultThresholds: {
    p50: 100,    // 100ms median response time
    p95: 500,    // 500ms 95th percentile  
    p99: 1000,   // 1s 99th percentile
    maxErrorRate: 0.01, // 1% max error rate
    minThroughput: 100   // 100 req/sec minimum
  },

  // Endpoint-specific budgets
  endpointBudgets: {
    '/api/v1/health': { p95: 50, errorRate: 0 },
    '/api/v1/auth/login': { p95: 200, errorRate: 0.001 },
    '/api/v1/services/execute': { p95: 1000, errorRate: 0.01 },
    // ... more endpoints
  }
};
```

### Load Test Scenarios

Customize load patterns in the configuration:

```typescript
loadTests: {
  light: {
    name: 'Light Load Test',
    duration: 60,
    target: 10,
    connections: 10
  },
  heavy: {
    name: 'Heavy Load Test', 
    duration: 600,
    target: 1000,
    connections: 200
  }
}
```

## 📊 Test Categories

### 1. Baseline Performance Tests

Establish performance baselines for all API endpoints:

```bash
# Collect baseline metrics
npm run performance:baseline

# Files generated:
# - tests/performance/reports/baseline-{timestamp}.json
# - tests/performance/reports/baseline-{timestamp}.html
```

**What it tests:**
- Response time percentiles (P50, P95, P99)
- Throughput (requests per second)
- Error rates
- Memory usage patterns
- CPU utilization

### 2. Jest Performance Tests

Unit-style performance tests with assertions:

```bash
# Run Jest performance tests
npm run test:performance

# Individual test files:
# - api-performance.test.ts         # API endpoint performance
# - workflow-performance.test.ts    # Workflow-specific performance
```

**Features:**
- Custom Jest matchers for performance assertions
- Memory leak detection
- Concurrent request testing
- Response time validation

### 3. Load Testing (Artillery)

Realistic user journey load testing:

```bash
# Run Artillery load tests
npm run test:load

# Configuration:
# - tests/performance/scenarios/artillery-advanced.yml
# - tests/performance/scripts/advanced-processors.js
```

**Scenarios:**
- User registration and login flows
- Service execution patterns
- Batch processing workflows
- Analytics dashboard usage
- API key authentication

### 4. Load Testing (K6)

High-performance load testing with K6:

```bash
# Run K6 load tests  
npm run test:k6

# Configuration:
# - tests/performance/scenarios/k6-load-test.js
```

**Features:**
- Custom metrics collection
- Performance thresholds
- Multi-stage load patterns
- Real-time monitoring
- Detailed reporting

### 5. Stress Testing

Progressive load increases to find breaking points:

```bash
# Run stress tests
npm run test:stress
```

**Test Phases:**
1. Light Load (10 connections)
2. Moderate Load (25 connections)  
3. Heavy Load (50 connections)
4. Intense Load (100 connections)
5. Extreme Load (250 connections)
6. Breaking Point (500+ connections)

### 6. Chaos Engineering

Test system resilience under failure conditions:

```bash
# Run chaos tests
npm run test:chaos
```

**Chaos Scenarios:**
- Network latency injection
- Memory pressure testing
- CPU stress testing
- Service dependency failures
- Random process termination

### 7. Real-time Monitoring

Continuous performance monitoring:

```bash
# Start performance monitor
npm run performance:monitor
```

**Features:**
- Real-time metrics collection
- Automated alerting
- Performance trend analysis
- Memory usage tracking
- Live dashboard updates

## 📈 Reporting

### Comprehensive Reports

Generate detailed performance reports:

```bash
# Generate comprehensive report
npm run performance:report
```

**Report Formats:**
- **HTML Report**: Visual dashboard with charts
- **JSON Report**: Detailed raw data
- **CSV Summary**: Spreadsheet-compatible metrics
- **Executive Summary**: Text-based overview

### Report Contents

1. **Executive Summary**
   - Overall performance score
   - Key findings and insights
   - Critical issues identification
   - Top recommendations

2. **Performance Budget Analysis**
   - Endpoint compliance scores
   - Budget violations
   - Trend analysis

3. **Test Results Overview**
   - Baseline performance metrics
   - Stress test outcomes
   - Chaos engineering results
   - Load test summaries

4. **Comparative Analysis**
   - Before/after comparisons
   - Historical trend analysis
   - Industry benchmark comparison

5. **Recommendations**
   - Immediate actions required
   - Short-term improvements
   - Long-term optimization strategy
   - Monitoring setup guidance

## 🤖 CI/CD Integration

### GitHub Actions Workflow

Automated performance testing in CI/CD pipeline:

```yaml
# .github/workflows/performance-tests.yml
name: Performance Tests
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * *' # Nightly runs
```

**Workflow Jobs:**
1. **Baseline Performance** - Quick baseline metrics
2. **Jest Performance** - Unit-style performance tests  
3. **K6 Load Tests** - High-performance load testing
4. **Artillery Load Tests** - Scenario-based load testing
5. **Stress Tests** - Breaking point analysis
6. **Report Generation** - Comprehensive reporting
7. **Regression Check** - Performance regression detection

### Performance Gates

Automatic performance validation:
- **P95 Response Time** must be < budget
- **Error Rate** must be < 1%
- **Performance Score** must be > 60
- **Critical Issues** must be 0

## 📊 Monitoring & Alerting

### Real-time Metrics

The monitoring system tracks:

```typescript
interface SystemHealth {
  responseTime: { avg: number, p95: number, p99: number };
  throughput: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
}
```

### Alert Thresholds

Configurable alert thresholds:

```typescript
alertThresholds: {
  responseTime: 2000,    // 2 second P95
  errorRate: 0.05,       // 5% error rate
  memoryUsage: 85,       // 85% memory usage  
  cpuUsage: 90,          // 90% CPU usage
}
```

### External Integration

- **Slack Webhook**: Real-time alert notifications
- **Grafana Dashboard**: Visual monitoring
- **Prometheus Metrics**: Time-series data collection

## 🔍 Debugging Performance Issues

### Common Scenarios

**Slow Response Times:**
```bash
# Check baseline metrics
npm run performance:baseline

# Run detailed profiling
npm run profile:clinic
npm run profile:flamegraph
```

**High Error Rates:**
```bash
# Monitor in real-time
npm run performance:monitor

# Check specific endpoints
npm run test:performance -- --testNamePattern="API Performance"
```

**Memory Leaks:**
```bash
# Extended memory testing
NODE_OPTIONS="--expose-gc" npm run test:performance
```

### Performance Profiling

Built-in profiling tools:

```bash
# Clinic.js profiling
npm run profile:clinic

# 0x flame graphs  
npm run profile:flamegraph

# Custom memory profiling
npm run profile:memory
```

## 🎛️ Advanced Configuration

### Custom Scenarios

Create custom test scenarios:

```javascript
// tests/performance/scenarios/custom-scenario.js
export const customScenario = {
  stages: [
    { duration: '30s', target: 20 },
    { duration: '1m', target: 100 },
    { duration: '30s', target: 0 }
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.02']
  }
};
```

### Custom Metrics

Add custom metrics collection:

```typescript
// Extend BaselineCollector for custom metrics
export class CustomMetricsCollector extends BaselineCollector {
  async collectCustomMetric(endpoint: string): Promise<CustomMetric> {
    // Custom metric collection logic
  }
}
```

### Environment-Specific Configuration

```bash
# Different environments
npm run performance:baseline -- --env=staging
npm run performance:baseline -- --env=production

# Custom configuration
API_BASE_URL=https://api.staging.com npm run test:performance
```

## 📚 Best Practices

### 1. Test Design

- **Start Simple**: Begin with baseline tests
- **Realistic Scenarios**: Use actual user patterns
- **Incremental Load**: Gradually increase test intensity
- **Isolated Testing**: Test one component at a time
- **Consistent Environment**: Use identical test conditions

### 2. Performance Budgets

- **Set Realistic Thresholds**: Based on user expectations
- **Monitor Trends**: Track performance over time
- **Automate Enforcement**: Fail builds on violations
- **Regular Review**: Update budgets as system evolves

### 3. Monitoring Strategy

- **Continuous Monitoring**: Don't just test in CI
- **Key Metrics Focus**: Monitor what matters most
- **Alert Fatigue**: Avoid too many false positives
- **Actionable Alerts**: Include troubleshooting context

### 4. Performance Culture

- **Shift Left**: Test early in development
- **Developer Awareness**: Make performance visible
- **Regular Reviews**: Discuss performance in team meetings
- **Knowledge Sharing**: Document learnings and solutions

## 🛠️ Troubleshooting

### Common Issues

**Tests Timing Out:**
```bash
# Increase timeout
TEST_TIMEOUT=600000 npm run test:performance

# Check server health
curl -f http://localhost:3000/health
```

**High Memory Usage:**
```bash
# Run with garbage collection
NODE_OPTIONS="--max-old-space-size=4096" npm run test:performance
```

**Flaky Tests:**
```bash
# Run multiple iterations
npm run performance:baseline -- --iterations=5

# Add delays between requests
npm run test:performance -- --delay=1000
```

### Getting Help

1. **Check Logs**: Look at `tests/performance/reports/` for detailed logs
2. **Run in Debug Mode**: Use `DEBUG=performance:*` environment variable
3. **Isolate Issues**: Run individual test categories
4. **Review Configuration**: Verify `performance-config.ts` settings

## 📝 Contributing

### Adding New Tests

1. **Create Test File**: Add `*.test.ts` in `/tests/performance/`
2. **Follow Patterns**: Use existing test structure
3. **Add Configuration**: Update `performance-config.ts`
4. **Update CI**: Add to `.github/workflows/performance-tests.yml`

### Extending Framework

1. **Custom Collectors**: Extend `BaselineCollector`
2. **New Scenarios**: Add to `scenarios/` directory
3. **Report Formats**: Extend `generate-report.ts`
4. **Monitoring Features**: Enhance `performance-monitor.ts`

## 📊 Performance Metrics Glossary

- **P50/P95/P99**: 50th/95th/99th percentile response times
- **Throughput**: Requests per second the system can handle
- **Error Rate**: Percentage of failed requests
- **Latency**: Time from request to response
- **TTFB**: Time To First Byte
- **Apdex**: Application Performance Index score
- **RPS**: Requests Per Second
- **Concurrent Users**: Simultaneous active users
- **Think Time**: Delay between user actions
- **Ramp Up**: Gradual increase in load

---

## 🎯 Next Steps

After setting up the performance testing suite:

1. **Run Initial Baseline**: `npm run performance:baseline`
2. **Set Performance Budgets**: Update thresholds in config
3. **Enable CI Integration**: Merge GitHub Actions workflow
4. **Start Monitoring**: `npm run performance:monitor`
5. **Schedule Regular Tests**: Set up nightly performance regression testing
6. **Train Team**: Share performance testing knowledge
7. **Iterate and Improve**: Continuously refine tests based on findings

For questions or issues, check the [GitHub Issues](../../issues) or contact the platform team.

---

**Performance testing is a journey, not a destination. Continuous monitoring and improvement ensure optimal user experience.**