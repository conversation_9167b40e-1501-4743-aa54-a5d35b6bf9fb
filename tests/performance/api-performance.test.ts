/**
 * API Performance Tests
 * Comprehensive performance tests for all API endpoints
 */

import axios from 'axios';
import { PERFORMANCE_CONFIG } from './config/performance-config';

const API_BASE = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'PerfTest123!'
};

describe('API Performance Tests', () => {
  let authToken: string;
  let measurements: any[] = [];

  beforeAll(async () => {
    // Setup test user and auth token if needed
    try {
      const loginResponse = await axios.post(`${API_BASE}/api/v1/auth/login`, TEST_USER, {
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (loginResponse.status === 200 && loginResponse.data.token) {
        authToken = loginResponse.data.token;
      }
    } catch (error) {
      console.warn('Could not setup auth token for performance tests');
    }
  });

  afterAll(async () => {
    if (measurements.length > 0) {
      const summary = global.PerfUtils.generateSummary(measurements);
      console.log('\n📊 Overall API Performance Summary:');
      console.log(`Total Requests: ${summary.total_requests}`);
      console.log(`Success Rate: ${((summary.successful_requests / summary.total_requests) * 100).toFixed(2)}%`);
      console.log(`Avg Response Time: ${summary.avg_response_time.toFixed(2)}ms`);
      console.log(`P95 Response Time: ${summary.p95.toFixed(2)}ms`);
      console.log(`P99 Response Time: ${summary.p99.toFixed(2)}ms`);
      console.log(`Error Rate: ${(summary.error_rate * 100).toFixed(2)}%\n`);
    }
  });

  describe('Health Check Endpoint', () => {
    it('should respond within 50ms for health check', async () => {
      const iterations = 10;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const measurement = await global.PerfUtils.measureAsync(async () => {
          const response = await axios.get(`${API_BASE}/health`, {
            timeout: 5000
          });
          return response;
        }, `Health check ${i + 1}`);

        responseTimes.push(measurement.duration);
        measurements.push({
          endpoint: '/health',
          duration: measurement.duration,
          error: measurement.result.status !== 200
        });

        expect(measurement.result.status).toBe(200);
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/health']?.p95 || 50);
      }

      const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);
      expect(percentiles).toMeetPercentileBudget(95, 50);
      
      console.log(`✅ Health check P95: ${percentiles.p95.toFixed(2)}ms`);
    });

    it('should maintain sub-100ms response time under concurrent load', async () => {
      const concurrentRequests = 20;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          global.PerfUtils.measureAsync(async () => {
            return await axios.get(`${API_BASE}/health`, { timeout: 5000 });
          }, `Concurrent health check ${i + 1}`)
        );
      }

      const results = await Promise.all(promises);
      const responseTimes = results.map(r => r.duration);
      const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);

      results.forEach(result => {
        measurements.push({
          endpoint: '/health',
          duration: result.duration,
          error: result.result.status !== 200
        });
      });

      expect(percentiles.p95).toBeLessThan(100);
      expect(percentiles.p99).toBeLessThan(200);

      console.log(`✅ Concurrent health check P95: ${percentiles.p95.toFixed(2)}ms, P99: ${percentiles.p99.toFixed(2)}ms`);
    });
  });

  describe('Authentication Endpoints', () => {
    it('should handle login requests within performance budget', async () => {
      const iterations = 5;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const measurement = await global.PerfUtils.measureAsync(async () => {
          return await axios.post(`${API_BASE}/api/v1/auth/login`, TEST_USER, {
            timeout: 10000,
            validateStatus: () => true
          });
        }, `Login attempt ${i + 1}`);

        responseTimes.push(measurement.duration);
        measurements.push({
          endpoint: '/api/v1/auth/login',
          duration: measurement.duration,
          error: measurement.result.status !== 200 && measurement.result.status !== 401
        });

        // Accept both 200 (success) and 401 (user not found) as valid for perf testing
        expect([200, 401]).toContain(measurement.result.status);
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/auth/login']?.p95 || 200);
      }

      const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);
      expect(percentiles).toMeetPercentileBudget(95, 200);

      console.log(`✅ Login P95: ${percentiles.p95.toFixed(2)}ms`);
    });

    it('should handle registration requests efficiently', async () => {
      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.post(`${API_BASE}/api/v1/auth/register`, {
          email: `perf-test-${Date.now()}@example.com`,
          password: 'PerfTest123!',
          name: 'Performance Test User'
        }, {
          timeout: 10000,
          validateStatus: () => true
        });
      }, 'User registration');

      measurements.push({
        endpoint: '/api/v1/auth/register',
        duration: measurement.duration,
        error: measurement.result.status !== 201 && measurement.result.status !== 400
      });

      // Accept 201 (created) or 400 (user exists) for perf testing
      expect([201, 400]).toContain(measurement.result.status);
      expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/auth/register']?.p95 || 300);

      console.log(`✅ Registration: ${measurement.duration.toFixed(2)}ms`);
    });
  });

  describe('Service Execution Endpoints', () => {
    it('should execute single service within performance budget', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping service tests - no auth token');
        return;
      }

      const servicePayload = {
        service: 'velian',
        action: 'analyze',
        data: {
          input: 'Performance test data for service execution',
          options: { timeout: 30000 }
        }
      };

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.post(`${API_BASE}/api/v1/services/execute`, servicePayload, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          timeout: 60000,
          validateStatus: () => true
        });
      }, 'Service execution');

      measurements.push({
        endpoint: '/api/v1/services/execute',
        duration: measurement.duration,
        error: measurement.result.status !== 200
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/services/execute']?.p95 || 1000);
        expect(measurement.result.data).toHaveProperty('success');
        console.log(`✅ Service execution: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Service execution returned ${measurement.result.status}, duration: ${measurement.duration.toFixed(2)}ms`);
      }
    });

    it('should handle batch service execution efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping batch service test - no auth token');
        return;
      }

      const batchPayload = {
        requests: [
          {
            service: 'velian',
            action: 'analyze',
            data: { input: 'Batch request 1 for performance testing' }
          },
          {
            service: 'zero-entropy',
            action: 'compress',
            data: { input: 'Batch request 2 for performance testing' }
          }
        ]
      };

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.post(`${API_BASE}/api/v1/services/batch`, batchPayload, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          timeout: 120000,
          validateStatus: () => true
        });
      }, 'Batch service execution');

      measurements.push({
        endpoint: '/api/v1/services/batch',
        duration: measurement.duration,
        error: measurement.result.status !== 200
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/services/batch']?.p95 || 2000);
        expect(measurement.result.data).toHaveProperty('results');
        console.log(`✅ Batch service execution: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Batch service execution returned ${measurement.result.status}, duration: ${measurement.duration.toFixed(2)}ms`);
      }
    });
  });

  describe('Analytics Endpoints', () => {
    it('should provide analytics overview within performance budget', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping analytics test - no auth token');
        return;
      }

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.get(`${API_BASE}/api/v1/analytics/overview`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          params: { timeframe: '1h' },
          timeout: 30000,
          validateStatus: () => true
        });
      }, 'Analytics overview');

      measurements.push({
        endpoint: '/api/v1/analytics/overview',
        duration: measurement.duration,
        error: measurement.result.status !== 200
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/analytics/overview']?.p95 || 500);
        console.log(`✅ Analytics overview: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Analytics overview returned ${measurement.result.status}, duration: ${measurement.duration.toFixed(2)}ms`);
      }
    });
  });

  describe('API Stress Testing', () => {
    it('should handle burst of concurrent requests', async () => {
      const concurrentRequests = 10;
      const promises = [];

      console.log(`🔥 Testing ${concurrentRequests} concurrent health check requests...`);

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          global.PerfUtils.measureAsync(async () => {
            return await axios.get(`${API_BASE}/health`, { 
              timeout: 10000,
              validateStatus: () => true 
            });
          }, `Concurrent request ${i + 1}`)
        );
      }

      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').map(r => r.value);
      const failed = results.filter(r => r.status === 'rejected');

      successful.forEach(result => {
        measurements.push({
          endpoint: '/health',
          duration: result.duration,
          error: result.result.status !== 200
        });
      });

      const successRate = successful.length / concurrentRequests;
      const responseTimes = successful.map(r => r.duration);
      
      if (responseTimes.length > 0) {
        const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);
        
        expect(successRate).toBeGreaterThan(0.9); // 90% success rate minimum
        expect(percentiles.p95).toBeLessThan(1000); // P95 under 1 second for burst
        
        console.log(`✅ Burst test: ${successful.length}/${concurrentRequests} successful, P95: ${percentiles.p95.toFixed(2)}ms`);
      }

      if (failed.length > 0) {
        console.log(`⚠️ ${failed.length} requests failed during burst test`);
      }
    });

    it('should maintain performance under sustained load', async () => {
      const requestCount = 20;
      const delayBetweenRequests = 100; // 100ms between requests
      const responseTimes: number[] = [];
      let errors = 0;

      console.log(`⏳ Testing sustained load: ${requestCount} requests with ${delayBetweenRequests}ms intervals...`);

      for (let i = 0; i < requestCount; i++) {
        try {
          const measurement = await global.PerfUtils.measureAsync(async () => {
            return await axios.get(`${API_BASE}/health`, { timeout: 5000 });
          }, `Sustained request ${i + 1}`);

          responseTimes.push(measurement.duration);
          measurements.push({
            endpoint: '/health',
            duration: measurement.duration,
            error: measurement.result.status !== 200
          });

          if (measurement.result.status !== 200) {
            errors++;
          }

          // Small delay between requests
          if (i < requestCount - 1) {
            await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
          }

        } catch (error) {
          errors++;
          console.log(`❌ Request ${i + 1} failed:`, error.message);
        }
      }

      const errorRate = errors / requestCount;
      const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);

      expect(errorRate).toHaveErrorRateBelow(0.05); // Less than 5% error rate
      expect(percentiles.p95).toBeLessThan(200); // P95 under 200ms for sustained load

      console.log(`✅ Sustained load: ${(100 - (errorRate * 100)).toFixed(1)}% success rate, P95: ${percentiles.p95.toFixed(2)}ms`);
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should not cause memory leaks during repeated requests', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      const iterations = 50;

      console.log(`🧠 Testing memory usage over ${iterations} requests...`);

      for (let i = 0; i < iterations; i++) {
        try {
          await axios.get(`${API_BASE}/health`, { timeout: 5000 });
          
          // Force garbage collection every 10 requests if available
          if (i % 10 === 0 && global.gc) {
            global.gc();
          }
        } catch (error) {
          // Ignore individual request failures for memory test
        }
      }

      // Allow some time for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreaseMB = memoryIncrease / 1024 / 1024;

      console.log(`Memory usage - Initial: ${Math.round(initialMemory / 1024 / 1024)}MB, Final: ${Math.round(finalMemory / 1024 / 1024)}MB`);
      console.log(`Memory increase: ${memoryIncreaseMB.toFixed(2)}MB`);

      // Allow up to 10MB memory increase for 50 requests (should be much less)
      expect(memoryIncreaseMB).toBeLessThan(10);

      if (memoryIncreaseMB > 5) {
        console.warn(`⚠️ Memory increase (${memoryIncreaseMB.toFixed(2)}MB) may indicate a memory leak`);
      } else {
        console.log(`✅ Memory usage looks healthy (+${memoryIncreaseMB.toFixed(2)}MB)`);
      }
    });
  });
});