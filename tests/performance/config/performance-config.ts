/**
 * Performance Testing Configuration
 * Central configuration for all performance testing scenarios
 */

export interface PerformanceThresholds {
  p50: number;
  p95: number;
  p99: number;
  maxErrorRate: number;
  maxMemoryUsage: number;
  maxCPUUsage: number;
  minThroughput: number;
}

export interface LoadTestConfig {
  name: string;
  duration: number;
  rampUp: number;
  target: number;
  connections: number;
  thresholds: PerformanceThresholds;
}

export const PERFORMANCE_CONFIG = {
  // API Base Configuration
  apiBase: process.env.API_BASE || 'http://localhost:3000',
  apiVersion: 'v1',
  
  // Test User Credentials
  testUsers: {
    admin: {
      email: '<EMAIL>',
      password: 'SecureAdminPass123!'
    },
    user: {
      email: '<EMAIL>',
      password: 'SecureUserPass123!'
    }
  },

  // Default Performance Thresholds
  defaultThresholds: {
    p50: 100,    // 100ms median response time
    p95: 500,    // 500ms 95th percentile
    p99: 1000,   // 1s 99th percentile
    maxErrorRate: 0.01, // 1% max error rate
    maxMemoryUsage: 512, // 512MB max memory
    maxCPUUsage: 80,     // 80% max CPU
    minThroughput: 100   // 100 req/sec minimum
  } as PerformanceThresholds,

  // Load Test Scenarios
  loadTests: {
    // Light Load Testing
    light: {
      name: 'Light Load Test',
      duration: 60,
      rampUp: 10,
      target: 10,
      connections: 10,
      thresholds: {
        p50: 50,
        p95: 200,
        p99: 500,
        maxErrorRate: 0.005,
        maxMemoryUsage: 256,
        maxCPUUsage: 50,
        minThroughput: 50
      }
    } as LoadTestConfig,

    // Medium Load Testing
    medium: {
      name: 'Medium Load Test',
      duration: 300,
      rampUp: 60,
      target: 100,
      connections: 50,
      thresholds: {
        p50: 100,
        p95: 500,
        p99: 1000,
        maxErrorRate: 0.01,
        maxMemoryUsage: 512,
        maxCPUUsage: 70,
        minThroughput: 100
      }
    } as LoadTestConfig,

    // Heavy Load Testing
    heavy: {
      name: 'Heavy Load Test',
      duration: 600,
      rampUp: 120,
      target: 1000,
      connections: 200,
      thresholds: {
        p50: 200,
        p95: 1000,
        p99: 2000,
        maxErrorRate: 0.02,
        maxMemoryUsage: 1024,
        maxCPUUsage: 85,
        minThroughput: 500
      }
    } as LoadTestConfig,

    // Spike Testing
    spike: {
      name: 'Spike Test',
      duration: 120,
      rampUp: 5,
      target: 5000,
      connections: 1000,
      thresholds: {
        p50: 500,
        p95: 2000,
        p99: 5000,
        maxErrorRate: 0.05,
        maxMemoryUsage: 2048,
        maxCPUUsage: 95,
        minThroughput: 1000
      }
    } as LoadTestConfig,

    // Soak Testing (24-hour)
    soak: {
      name: 'Soak Test',
      duration: 86400, // 24 hours
      rampUp: 300,
      target: 50,
      connections: 25,
      thresholds: {
        p50: 100,
        p95: 500,
        p99: 1000,
        maxErrorRate: 0.01,
        maxMemoryUsage: 512,
        maxCPUUsage: 60,
        minThroughput: 50
      }
    } as LoadTestConfig
  },

  // Endpoint Performance Budgets
  endpointBudgets: {
    '/api/v1/health': { p95: 50, errorRate: 0 },
    '/api/v1/auth/login': { p95: 200, errorRate: 0.001 },
    '/api/v1/auth/register': { p95: 300, errorRate: 0.001 },
    '/api/v1/services/execute': { p95: 1000, errorRate: 0.01 },
    '/api/v1/services/batch': { p95: 2000, errorRate: 0.02 },
    '/api/v1/workflows/execute': { p95: 3000, errorRate: 0.02 },
    '/api/v1/analytics/overview': { p95: 500, errorRate: 0.005 },
    '/api/v1/api-keys': { p95: 200, errorRate: 0.005 },
    '/api/v1/subscriptions': { p95: 300, errorRate: 0.005 },
    '/api/v1/billing': { p95: 400, errorRate: 0.005 }
  },

  // Monitoring Configuration
  monitoring: {
    metricsInterval: 5000, // 5 seconds
    alertThresholds: {
      responseTime: 2000,
      errorRate: 0.05,
      memoryUsage: 85,
      cpuUsage: 90,
      diskUsage: 85
    },
    grafanaUrl: process.env.GRAFANA_URL || 'http://localhost:3001',
    prometheusUrl: process.env.PROMETHEUS_URL || 'http://localhost:9090'
  },

  // Database Performance
  database: {
    connectionPoolSize: 20,
    queryTimeout: 30000,
    slowQueryThreshold: 1000,
    connectionTimeout: 5000
  },

  // Cache Performance
  cache: {
    redis: {
      maxMemory: '1gb',
      evictionPolicy: 'allkeys-lru',
      maxConnections: 50
    },
    hitRateThreshold: 0.8 // 80% cache hit rate minimum
  },

  // File Upload Limits
  fileUpload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    timeoutMs: 30000,
    concurrentUploads: 5
  },

  // WebSocket Performance
  websocket: {
    maxConnections: 1000,
    heartbeatInterval: 30000,
    messageTimeout: 5000
  },

  // Chaos Engineering
  chaos: {
    enabled: process.env.NODE_ENV !== 'production',
    scenarios: {
      networkLatency: { enabled: true, latencyMs: 100 },
      memoryPressure: { enabled: true, pressureMB: 100 },
      cpuStress: { enabled: true, durationSec: 30 },
      diskFull: { enabled: false }, // Dangerous - disabled by default
      serviceDown: { enabled: true, durationSec: 60 }
    }
  },

  // Report Configuration
  reporting: {
    outputDir: './tests/performance/reports',
    formats: ['json', 'html', 'csv'],
    includeCharts: true,
    compareBaseline: true,
    alertSlackWebhook: process.env.SLACK_WEBHOOK_URL
  }
};

export default PERFORMANCE_CONFIG;