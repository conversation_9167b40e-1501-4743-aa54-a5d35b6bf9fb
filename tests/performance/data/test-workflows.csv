workflowId,inputData,expectedOutput
workflow-001,Document processing pipeline test data,Processed document with extracted metadata
workflow-002,Multi-service orchestration test with API calls,Orchestrated response from multiple services
workflow-003,Data transformation workflow with validation,Validated and transformed data output
workflow-004,Image processing pipeline with filters,Processed images with applied filters
workflow-005,Content analysis workflow with ML models,Analysis results with confidence scores
workflow-006,Resume screening workflow with matching,Ranked candidates with match scores
workflow-007,Code analysis workflow with quality metrics,Code quality report with recommendations
workflow-008,Financial data processing with compliance,Compliant financial report with audit trail
workflow-009,Social media content workflow with moderation,Moderated content with approval status
workflow-010,E-commerce product workflow with recommendations,Product catalog with personalized recommendations
workflow-011,Customer service workflow with sentiment analysis,Customer interaction summary with sentiment
workflow-012,Marketing campaign workflow with analytics,Campaign performance metrics and insights
workflow-013,Inventory management workflow with forecasting,Inventory optimization recommendations
workflow-014,Quality assurance workflow with automated testing,Test results with coverage and performance metrics
workflow-015,Data backup workflow with verification,Backup verification report with integrity checks