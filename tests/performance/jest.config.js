/**
 * Jest Configuration for Performance Tests
 * Specialized configuration for performance testing suite
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Root directory for tests
  rootDir: '../../',
  
  // Test file patterns
  testMatch: [
    '<rootDir>/tests/performance/**/*.test.ts',
    '<rootDir>/tests/performance/**/*.test.js'
  ],
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Transform files
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
    '^.+\\.jsx?$': 'babel-jest'
  },
  
  // Module name mapping (path aliases)
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/backend/src/$1',
    '^@shared/(.*)$': '<rootDir>/shared/src/$1',
    '^@performance/(.*)$': '<rootDir>/tests/performance/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/performance/jest.setup.js'
  ],
  
  // Coverage configuration
  collectCoverage: false, // Performance tests don't need coverage
  
  // Test timeout (extended for performance tests)
  testTimeout: 300000, // 5 minutes for long-running tests
  
  // Reporters
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: '<rootDir>/tests/performance/reports',
      outputName: 'performance-test-results.xml',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
      ancestorSeparator: ' › ',
      usePathForSuiteName: true
    }]
  ],
  
  // Global variables
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json'
    },
    API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3000',
    TEST_TIMEOUT: 300000,
    PERFORMANCE_MODE: true
  },
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/backend/node_modules/',
    '<rootDir>/frontend/node_modules/',
    '<rootDir>/tests/performance/reports/',
    '<rootDir>/tests/performance/data/'
  ],
  
  // Module path ignore patterns
  modulePathIgnorePatterns: [
    '<rootDir>/tests/performance/reports/',
    '<rootDir>/tests/performance/data/'
  ],
  
  // Verbose output
  verbose: true,
  
  // Silent console output during tests
  silent: false,
  
  // Force exit after tests complete
  forceExit: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Detect open handles (helpful for debugging)
  detectOpenHandles: true,
  
  // Maximum worker processes
  maxWorkers: 1, // Run performance tests sequentially
  
  // Cache directory
  cacheDirectory: '<rootDir>/tests/performance/.jest-cache',
  
  // Custom matcher configuration
  testRunner: 'jest-jasmine2'
};