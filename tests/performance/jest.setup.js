/**
 * Jest Setup for Performance Tests
 * Global setup and utilities for performance testing
 */

const { performance } = require('perf_hooks');

// Global test configuration
global.PERFORMANCE_CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3000',
  TEST_TIMEOUT: 300000, // 5 minutes
  DEFAULT_THRESHOLDS: {
    response_time_p95: 1000,
    response_time_p99: 2000,
    error_rate_max: 0.01,
    throughput_min: 10
  }
};

// Performance measurement utilities
global.PerfUtils = {
  /**
   * Measure async function execution time
   */
  async measureAsync(fn, label = 'operation') {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    const duration = end - start;
    
    console.log(`⏱️  ${label}: ${duration.toFixed(2)}ms`);
    
    return {
      result,
      duration,
      start,
      end
    };
  },

  /**
   * Measure sync function execution time
   */
  measure(fn, label = 'operation') {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;
    
    console.log(`⏱️  ${label}: ${duration.toFixed(2)}ms`);
    
    return {
      result,
      duration,
      start,
      end
    };
  },

  /**
   * Calculate percentiles from array of numbers
   */
  calculatePercentiles(values, percentiles = [50, 95, 99]) {
    const sorted = [...values].sort((a, b) => a - b);
    const results = {};
    
    percentiles.forEach(p => {
      const index = Math.ceil((p / 100) * sorted.length) - 1;
      results[`p${p}`] = sorted[Math.max(0, Math.min(index, sorted.length - 1))];
    });
    
    return results;
  },

  /**
   * Generate performance summary
   */
  generateSummary(measurements) {
    const responseTimes = measurements.map(m => m.duration);
    const errors = measurements.filter(m => m.error).length;
    
    const percentiles = this.calculatePercentiles(responseTimes);
    const avgResponseTime = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const maxResponseTime = Math.max(...responseTimes);
    const minResponseTime = Math.min(...responseTimes);
    const errorRate = errors / measurements.length;
    
    return {
      total_requests: measurements.length,
      successful_requests: measurements.length - errors,
      error_count: errors,
      error_rate: errorRate,
      avg_response_time: avgResponseTime,
      min_response_time: minResponseTime,
      max_response_time: maxResponseTime,
      ...percentiles
    };
  }
};

// Custom Jest matchers for performance assertions
expect.extend({
  /**
   * Assert response time is within threshold
   */
  toBeWithinResponseTime(received, threshold) {
    const pass = received <= threshold;
    
    if (pass) {
      return {
        message: () => `Expected response time ${received}ms not to be within ${threshold}ms`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected response time ${received}ms to be within ${threshold}ms`,
        pass: false
      };
    }
  },

  /**
   * Assert error rate is below threshold
   */
  toHaveErrorRateBelow(received, threshold) {
    const pass = received < threshold;
    
    if (pass) {
      return {
        message: () => `Expected error rate ${received} not to be below ${threshold}`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected error rate ${received} to be below ${threshold}`,
        pass: false
      };
    }
  },

  /**
   * Assert throughput meets minimum requirement
   */
  toMeetThroughputRequirement(received, minimum) {
    const pass = received >= minimum;
    
    if (pass) {
      return {
        message: () => `Expected throughput ${received} not to meet minimum ${minimum}`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected throughput ${received} to meet minimum ${minimum}`,
        pass: false
      };
    }
  },

  /**
   * Assert percentile is within budget
   */
  toMeetPercentileBudget(received, percentile, budget) {
    const value = received[`p${percentile}`];
    const pass = value <= budget;
    
    if (pass) {
      return {
        message: () => `Expected P${percentile} ${value}ms not to meet budget ${budget}ms`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected P${percentile} ${value}ms to meet budget ${budget}ms`,
        pass: false
      };
    }
  }
});

// Setup test environment
beforeAll(async () => {
  console.log('🔧 Setting up performance test environment...');
  console.log(`Target API: ${global.PERFORMANCE_CONFIG.API_BASE_URL}`);
  console.log(`Test timeout: ${global.PERFORMANCE_CONFIG.TEST_TIMEOUT}ms`);
  
  // Optionally wait for API to be ready
  if (process.env.WAIT_FOR_API === 'true') {
    console.log('⏳ Waiting for API to be ready...');
    await waitForAPI();
  }
  
  console.log('✅ Performance test environment ready\n');
});

afterAll(async () => {
  console.log('\n🧹 Cleaning up performance test environment...');
  
  // Cleanup logic here if needed
  
  console.log('✅ Performance test cleanup complete');
});

// Utility function to wait for API availability
async function waitForAPI(maxWaitTime = 60000) {
  const axios = require('axios');
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      await axios.get(`${global.PERFORMANCE_CONFIG.API_BASE_URL}/health`, {
        timeout: 5000
      });
      console.log('✅ API is ready');
      return;
    } catch (error) {
      console.log('⏳ API not ready, waiting...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  throw new Error('API failed to become ready within timeout period');
}

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Memory usage tracking
const initialMemory = process.memoryUsage();
console.log(`Initial memory usage: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);

// Track memory usage periodically during tests
const memoryInterval = setInterval(() => {
  const currentMemory = process.memoryUsage();
  const heapUsedMB = Math.round(currentMemory.heapUsed / 1024 / 1024);
  
  if (heapUsedMB > 100) { // Warn if memory usage is high
    console.warn(`⚠️  High memory usage: ${heapUsedMB}MB`);
  }
}, 30000); // Check every 30 seconds

// Cleanup interval on exit
process.on('exit', () => {
  clearInterval(memoryInterval);
});