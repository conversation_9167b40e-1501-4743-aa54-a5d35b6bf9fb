/**
 * Baseline Performance Metrics Collector
 * Collects and stores baseline performance metrics for comparison
 */

import { performance } from 'perf_hooks';
import * as fs from 'fs/promises';
import * as path from 'path';
import axios from 'axios';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

export interface BaselineMetric {
  endpoint: string;
  method: string;
  timestamp: number;
  responseTime: number;
  statusCode: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  throughput: number;
  errorRate: number;
}

export interface BaselineReport {
  timestamp: number;
  version: string;
  environment: string;
  duration: number;
  totalRequests: number;
  metrics: BaselineMetric[];
  summary: {
    avgResponseTime: number;
    p50: number;
    p95: number;
    p99: number;
    maxResponseTime: number;
    minResponseTime: number;
    errorRate: number;
    throughput: number;
    avgMemoryUsage: number;
    avgCpuUsage: number;
  };
}

export class BaselineCollector {
  private metrics: BaselineMetric[] = [];
  private startTime: number = 0;
  private totalRequests: number = 0;

  constructor(
    private readonly baseUrl: string = PERFORMANCE_CONFIG.apiBase,
    private readonly outputDir: string = PERFORMANCE_CONFIG.reporting.outputDir
  ) {}

  /**
   * Start collecting baseline metrics
   */
  async startCollection(): Promise<void> {
    this.startTime = performance.now();
    this.metrics = [];
    this.totalRequests = 0;

    console.log('🎯 Starting baseline metrics collection...');
    
    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });
  }

  /**
   * Collect metrics for a single API call
   */
  async collectMetric(endpoint: string, method: string = 'GET', data?: any): Promise<BaselineMetric> {
    const startTime = performance.now();
    const startCpu = process.cpuUsage();
    
    let statusCode = 0;
    let error = false;

    try {
      const response = await axios({
        method: method.toLowerCase() as any,
        url: `${this.baseUrl}${endpoint}`,
        data,
        timeout: 10000,
        validateStatus: (status) => status < 500 // Don't throw for 4xx errors
      });
      
      statusCode = response.status;
    } catch (err: any) {
      statusCode = err.response?.status || 500;
      error = true;
    }

    const endTime = performance.now();
    const endCpu = process.cpuUsage(startCpu);
    
    const metric: BaselineMetric = {
      endpoint,
      method,
      timestamp: Date.now(),
      responseTime: endTime - startTime,
      statusCode,
      memoryUsage: process.memoryUsage(),
      cpuUsage: endCpu,
      throughput: 1000 / (endTime - startTime), // requests per second
      errorRate: error ? 1 : 0
    };

    this.metrics.push(metric);
    this.totalRequests++;

    return metric;
  }

  /**
   * Run comprehensive baseline test suite
   */
  async runBaselineSuite(): Promise<BaselineReport> {
    console.log('🚀 Running comprehensive baseline test suite...');

    const endpoints = [
      { endpoint: '/health', method: 'GET' },
      { endpoint: '/api/v1/', method: 'GET' },
      { endpoint: '/api/v1/auth/login', method: 'POST', data: PERFORMANCE_CONFIG.testUsers.user },
      { endpoint: '/api/v1/services/execute', method: 'POST', data: { service: 'velian', action: 'analyze', data: { input: 'baseline test' } } },
      { endpoint: '/api/v1/analytics/overview', method: 'GET' },
      { endpoint: '/api/v1/api-keys', method: 'GET' }
    ];

    // Run each endpoint multiple times to get statistical significance
    const iterations = 10;
    
    for (let i = 0; i < iterations; i++) {
      console.log(`📊 Running iteration ${i + 1}/${iterations}...`);
      
      for (const { endpoint, method, data } of endpoints) {
        try {
          await this.collectMetric(endpoint, method, data);
          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.warn(`⚠️ Failed to collect metric for ${method} ${endpoint}:`, error);
        }
      }
    }

    return this.generateReport();
  }

  /**
   * Generate baseline report from collected metrics
   */
  private generateReport(): BaselineReport {
    const duration = performance.now() - this.startTime;
    const responseTimes = this.metrics.map(m => m.responseTime).sort((a, b) => a - b);
    const errors = this.metrics.filter(m => m.errorRate > 0);

    const report: BaselineReport = {
      timestamp: Date.now(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'test',
      duration,
      totalRequests: this.totalRequests,
      metrics: this.metrics,
      summary: {
        avgResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        p50: this.percentile(responseTimes, 0.5),
        p95: this.percentile(responseTimes, 0.95),
        p99: this.percentile(responseTimes, 0.99),
        maxResponseTime: Math.max(...responseTimes),
        minResponseTime: Math.min(...responseTimes),
        errorRate: errors.length / this.totalRequests,
        throughput: (this.totalRequests * 1000) / duration,
        avgMemoryUsage: this.metrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0) / this.metrics.length,
        avgCpuUsage: this.metrics.reduce((sum, m) => sum + (m.cpuUsage.user + m.cpuUsage.system), 0) / this.metrics.length
      }
    };

    return report;
  }

  /**
   * Save baseline report to file
   */
  async saveReport(report: BaselineReport): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `baseline-${timestamp}.json`;
    const filepath = path.join(this.outputDir, filename);

    await fs.writeFile(filepath, JSON.stringify(report, null, 2));
    
    console.log(`📄 Baseline report saved: ${filepath}`);
    return filepath;
  }

  /**
   * Compare current metrics with baseline
   */
  async compareWithBaseline(baselineFile: string): Promise<any> {
    try {
      const baselineData = JSON.parse(await fs.readFile(baselineFile, 'utf-8'));
      const currentReport = this.generateReport();

      const comparison = {
        baseline: baselineData.summary,
        current: currentReport.summary,
        changes: {
          responseTime: {
            p50: ((currentReport.summary.p50 - baselineData.summary.p50) / baselineData.summary.p50) * 100,
            p95: ((currentReport.summary.p95 - baselineData.summary.p95) / baselineData.summary.p95) * 100,
            p99: ((currentReport.summary.p99 - baselineData.summary.p99) / baselineData.summary.p99) * 100
          },
          throughput: ((currentReport.summary.throughput - baselineData.summary.throughput) / baselineData.summary.throughput) * 100,
          errorRate: currentReport.summary.errorRate - baselineData.summary.errorRate,
          memoryUsage: ((currentReport.summary.avgMemoryUsage - baselineData.summary.avgMemoryUsage) / baselineData.summary.avgMemoryUsage) * 100
        }
      };

      return comparison;
    } catch (error) {
      console.warn('⚠️ Could not load baseline for comparison:', error);
      return null;
    }
  }

  /**
   * Calculate percentile from sorted array
   */
  private percentile(sortedArray: number[], percentile: number): number {
    const index = Math.ceil(sortedArray.length * percentile) - 1;
    return sortedArray[index] || 0;
  }

  /**
   * Stop collection and cleanup
   */
  async stopCollection(): Promise<void> {
    console.log('🛑 Stopping baseline metrics collection...');
    // Cleanup if needed
  }
}

export default BaselineCollector;