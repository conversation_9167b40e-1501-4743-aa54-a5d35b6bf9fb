# Advanced Artillery Load Testing Configuration
# Comprehensive load testing scenarios with detailed metrics collection

config:
  target: "{{ $environment.API_BASE || 'http://localhost:3000' }}"
  
  # Multi-phase load testing
  phases:
    # Warm-up phase
    - name: "Warm-up Phase"
      duration: 30
      arrivalRate: 1
      rampTo: 5
      
    # Baseline load
    - name: "Baseline Load"
      duration: 60
      arrivalRate: 10
      
    # Gradual ramp-up
    - name: "Gradual Ramp-up"
      duration: 120
      arrivalRate: 10
      rampTo: 50
      
    # Sustained load
    - name: "Sustained Load"
      duration: 300
      arrivalRate: 50
      
    # Peak load testing  
    - name: "Peak Load"
      duration: 120
      arrivalRate: 50
      rampTo: 200
      
    # Spike testing
    - name: "Spike Test"
      duration: 60
      arrivalRate: 200
      rampTo: 1000
      
    # Recovery testing
    - name: "Recovery Phase"
      duration: 120
      arrivalRate: 1000
      rampTo: 10
      
  # Test data payload
  payload:
    - path: "../data/test-users.csv"
      fields:
        - "email"
        - "password"
        - "name"
        - "role"
    - path: "../data/test-services.csv"
      fields:
        - "service"
        - "action"
        - "testData"
    - path: "../data/test-workflows.csv"
      fields:
        - "workflowId"
        - "inputData"
        - "expectedOutput"
        
  # Environment variables
  variables:
    baseUrl: "{{ $environment.API_BASE }}"
    adminToken: "{{ $environment.ADMIN_TOKEN }}"
    testApiKey: "{{ $environment.TEST_API_KEY }}"
    
  # Load test data processor
  processor: "../scripts/advanced-processors.js"
  
  # Enhanced plugins for detailed metrics
  plugins:
    # Metrics collection by endpoint
    metrics-by-endpoint:
      useOnlyRequestNames: true
      
    # Expectations and assertions
    expect:
      outputFormat: json
      
    # Memory and CPU monitoring
    cloudwatch:
      region: "{{ $environment.AWS_REGION }}"
      
    # Custom metrics publisher
    publish-metrics:
      type: "prometheus"
      url: "{{ $environment.PROMETHEUS_URL }}"
      
  # Performance thresholds and SLAs
  ensure:
    # Response time thresholds
    p50: 100    # 50th percentile < 100ms
    p95: 500    # 95th percentile < 500ms
    p99: 1000   # 99th percentile < 1s
    p99.9: 2000 # 99.9th percentile < 2s
    
    # Error rate thresholds
    maxErrorRate: 1        # Max 1% error rate
    max4xxErrorRate: 2     # Max 2% 4xx error rate
    max5xxErrorRate: 0.5   # Max 0.5% 5xx error rate
    
    # Throughput requirements
    minThroughput: 100     # Minimum 100 req/sec
    
  # HTTP configuration
  http:
    timeout: 30
    pool: 50
    maxSockets: 100
    
  # TLS configuration  
  tls:
    rejectUnauthorized: false
    
  # WebSocket configuration
  ws:
    subprotocols: ['v1.api']

# Test scenarios with realistic user journeys
scenarios:
  # Complete user onboarding flow
  - name: "User Onboarding Journey"
    weight: 15
    flow:
      # User registration
      - post:
          name: "User Registration"
          url: "/api/v1/auth/register"
          json:
            email: "{{ $randomString() }}@loadtest.com"
            password: "LoadTest123!"
            name: "Load Test User {{ $randomNumber() }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"  
              as: "userId"
          expect:
            - statusCode: 201
            - hasProperty: token
            - hasProperty: user
            
      - think: 2
      
      # Profile setup
      - put:
          name: "Profile Setup"
          url: "/api/v1/users/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            preferences: 
              theme: "dark"
              notifications: true
              language: "en"
          expect:
            - statusCode: 200
            
      # API key creation
      - post:
          name: "Create API Key"
          url: "/api/v1/api-keys"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            name: "Load Test API Key"
            permissions: ["services.execute", "workflows.read"]
            expiresIn: "30d"
          capture:
            - json: "$.key"
              as: "apiKey"
          expect:
            - statusCode: 201

  # High-frequency service execution
  - name: "Service Execution Intensive"
    weight: 40
    flow:
      # Quick authentication
      - post:
          name: "Quick Login"  
          url: "/api/v1/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 200
            
      # Burst of service calls
      - loop:
          count: 5
          over:
            # Single service execution
            - post:
                name: "Service Execution"
                url: "/api/v1/services/execute"
                headers:
                  Authorization: "Bearer {{ authToken }}"
                json:
                  service: "{{ service }}"
                  action: "{{ action }}"
                  data:
                    input: "{{ testData }}"
                    options:
                      timeout: 30000
                      retries: 2
                expect:
                  - statusCode: 200
                  - hasProperty: success
                  - hasProperty: result
                  
            - think: 0.5
      
      # Batch service execution
      - post:
          name: "Batch Service Execution"
          url: "/api/v1/services/batch"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            requests:
              - service: "velian"
                action: "analyze"  
                data: { input: "Batch request 1 {{ $randomString() }}" }
              - service: "zero-entropy"
                action: "compress"
                data: { input: "Batch request 2 {{ $randomString() }}" }
              - service: "clueso"
                action: "search"
                data: { query: "test query {{ $randomString() }}" }
          expect:
            - statusCode: 200
            - hasProperty: results

  # Complex workflow execution scenarios  
  - name: "Workflow Orchestration"
    weight: 25
    flow:
      - post:
          name: "Workflow Login"
          url: "/api/v1/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
            
      # Get available workflows
      - get:
          name: "List Workflows"
          url: "/api/v1/workflows"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$.workflows[0].id"
              as: "workflowId"
          expect:
            - statusCode: 200
            - hasProperty: workflows
            
      # Execute workflow with complex input
      - post:
          name: "Execute Complex Workflow"
          url: "/api/v1/workflows/{{ workflowId }}/execute"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            input:
              data: "{{ inputData }}"
              parameters:
                parallel: true
                timeout: 120000
                retries: 3
                priority: "high"
            context:
              userId: "{{ $randomNumber() }}"
              sessionId: "{{ $randomString() }}"
              metadata:
                source: "load-test"
                timestamp: "{{ $timestamp }}"
          expect:
            - statusCode: 200
            - hasProperty: executionId
            - hasProperty: status
            
      - think: 5
      
      # Check workflow status
      - get:
          name: "Check Workflow Status"
          url: "/api/v1/workflows/executions/{{ executionId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200

  # Analytics and reporting load
  - name: "Analytics Dashboard Load"
    weight: 15
    flow:
      - post:
          name: "Analytics Login"
          url: "/api/v1/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"  
          capture:
            - json: "$.token"
              as: "authToken"
            
      # Dashboard overview
      - get:
          name: "Dashboard Overview"
          url: "/api/v1/analytics/overview"
          headers:
            Authorization: "Bearer {{ authToken }}"
          qs:
            timeframe: "24h"
            metrics: "all"
          expect:
            - statusCode: 200
            
      # Service-specific analytics
      - get:
          name: "Service Analytics"
          url: "/api/v1/analytics/services/{{ service }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          qs:
            timeframe: "7d"
            groupBy: "hour"
          expect:
            - statusCode: 200
            
      # Usage statistics
      - get:
          name: "Usage Statistics"
          url: "/api/v1/analytics/usage"
          headers:
            Authorization: "Bearer {{ authToken }}"
          qs:
            timeframe: "30d"
            breakdown: "service,user"
          expect:
            - statusCode: 200
            
      # Real-time metrics
      - get:
          name: "Real-time Metrics"
          url: "/api/v1/analytics/realtime"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200

  # API key-based authentication load
  - name: "API Key Authentication"
    weight: 5
    flow:
      # Service execution with API key
      - post:
          name: "API Key Service Call"
          url: "/api/v1/services/execute"
          headers:
            X-API-Key: "{{ testApiKey }}"
            Content-Type: "application/json"
          json:
            service: "velian"
            action: "analyze"
            data:
              input: "API key authenticated request"
              priority: "normal"
          expect:
            - statusCode: 200
            
      # Batch execution with API key  
      - post:
          name: "API Key Batch Execution"
          url: "/api/v1/services/batch"
          headers:
            X-API-Key: "{{ testApiKey }}"
          json:
            requests:
              - service: "velian"
                action: "analyze"
                data: { input: "API batch 1" }
              - service: "zero-entropy" 
                action: "compress"
                data: { input: "API batch 2" }
          expect:
            - statusCode: 200