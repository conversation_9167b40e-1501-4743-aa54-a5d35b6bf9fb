/**
 * K6 Load Testing Script
 * Comprehensive performance testing with K6
 */

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Counter, Rate, Trend, Gauge } from 'k6/metrics';
import { randomString, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const serviceExecutionTime = new Trend('service_execution_time');
const workflowExecutionTime = new Trend('workflow_execution_time');
const authenticationTime = new Trend('authentication_time');
const errorRate = new Rate('error_rate');
const successRate = new Rate('success_rate');
const apiKeyUsage = new Counter('api_key_usage');
const memoryUsage = new Gauge('memory_usage_mb');
const cpuUsage = new Gauge('cpu_usage_percent');

// Test configuration
export const options = {
  // Execution stages for realistic load patterns
  stages: [
    { duration: '30s', target: 10 },   // Warm-up
    { duration: '1m', target: 50 },    // Ramp-up
    { duration: '3m', target: 100 },   // Normal load
    { duration: '2m', target: 300 },   // High load
    { duration: '1m', target: 500 },   // Peak load
    { duration: '30s', target: 100 },  // Scale down
    { duration: '30s', target: 0 },    // Cool down
  ],
  
  // Performance thresholds
  thresholds: {
    // HTTP request duration thresholds
    'http_req_duration': [
      'p(50)<100',    // 50% of requests under 100ms
      'p(95)<500',    // 95% of requests under 500ms
      'p(99)<1000',   // 99% of requests under 1s
      'p(99.9)<2000', // 99.9% of requests under 2s
    ],
    
    // HTTP request rate thresholds
    'http_req_rate': ['rate>10'],
    
    // Error rate thresholds
    'error_rate': ['rate<0.01'], // Less than 1% error rate
    'success_rate': ['rate>0.99'], // More than 99% success rate
    
    // Custom metrics thresholds
    'service_execution_time': ['p(95)<1000'],
    'workflow_execution_time': ['p(95)<3000'],
    'authentication_time': ['p(95)<200'],
    
    // HTTP status checks
    'http_req_failed': ['rate<0.02'], // Less than 2% failed requests
  },
  
  // Resource usage limits
  maxVUs: 1000,
  maxRedirects: 0,
  userAgent: 'K6-LoadTester/1.0',
  
  // Test scenarios with different user behaviors
  scenarios: {
    // Constant load testing
    constant_load: {
      executor: 'constant-vus',
      vus: 50,
      duration: '5m',
      gracefulStop: '30s',
    },
    
    // Ramping up load
    ramping_up: {
      executor: 'ramping-vus',
      stages: [
        { duration: '2m', target: 100 },
        { duration: '5m', target: 200 },
        { duration: '2m', target: 0 },
      ],
      gracefulRampDown: '30s',
      startTime: '5m',
    },
    
    // Spike testing
    spike_test: {
      executor: 'constant-arrival-rate',
      rate: 1000, // 1000 iterations per second
      timeUnit: '1s',
      duration: '1m',
      preAllocatedVUs: 100,
      maxVUs: 500,
      startTime: '12m',
    },
    
    // Stress testing with shared iterations
    stress_test: {
      executor: 'shared-iterations',
      vus: 200,
      iterations: 10000,
      maxDuration: '10m',
      startTime: '14m',
    },
  },
};

// Configuration
const API_BASE = __ENV.API_BASE || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'LoadTest123!'
};
const TEST_API_KEY = __ENV.TEST_API_KEY || 'test-api-key';

// Global variables
let authToken = null;

// Setup function - runs once per VU
export function setup() {
  console.log('🚀 Starting K6 load test setup...');
  
  // Create test user if needed
  const registerResponse = http.post(`${API_BASE}/api/v1/auth/register`, JSON.stringify({
    email: `loadtest_${randomString(8)}@example.com`,
    password: TEST_USER.password,
    name: `Load Test User ${randomString(5)}`
  }), {
    headers: { 'Content-Type': 'application/json' }
  });
  
  return {
    testUser: TEST_USER,
    apiBase: API_BASE,
    setupComplete: true
  };
}

// Main test function
export default function(data) {
  group('Authentication Flow', () => {
    // User login
    group('User Login', () => {
      const startTime = Date.now();
      
      const loginResponse = http.post(`${data.apiBase}/api/v1/auth/login`, JSON.stringify({
        email: data.testUser.email,
        password: data.testUser.password
      }), {
        headers: { 'Content-Type': 'application/json' },
        timeout: '30s',
      });
      
      const authTime = Date.now() - startTime;
      authenticationTime.add(authTime);
      
      const loginSuccess = check(loginResponse, {
        'login status is 200': (r) => r.status === 200,
        'login response has token': (r) => r.json('token') !== undefined,
        'login response time < 500ms': (r) => r.timings.duration < 500,
      });
      
      if (loginSuccess && loginResponse.json('token')) {
        authToken = loginResponse.json('token');
        successRate.add(1);
      } else {
        errorRate.add(1);
        console.warn('Login failed:', loginResponse.status, loginResponse.body);
      }
    });
  });

  group('Service Execution Tests', () => {
    if (!authToken) {
      console.warn('Skipping service tests - no auth token');
      return;
    }
    
    // Single service execution
    group('Single Service Execution', () => {
      const services = ['velian', 'zero-entropy', 'clueso', 'hello-cv'];
      const service = services[randomIntBetween(0, services.length - 1)];
      const actions = ['analyze', 'process', 'search', 'transform'];
      const action = actions[randomIntBetween(0, actions.length - 1)];
      
      const startTime = Date.now();
      
      const serviceResponse = http.post(`${data.apiBase}/api/v1/services/execute`, JSON.stringify({
        service: service,
        action: action,
        data: {
          input: `K6 load test data ${randomString(20)}`,
          options: {
            timeout: 30000,
            priority: 'normal'
          }
        }
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        timeout: '60s',
      });
      
      const serviceTime = Date.now() - startTime;
      serviceExecutionTime.add(serviceTime);
      
      const serviceSuccess = check(serviceResponse, {
        'service execution status is 200': (r) => r.status === 200,
        'service response has success field': (r) => r.json('success') !== undefined,
        'service response time < 2s': (r) => r.timings.duration < 2000,
        'service response has result': (r) => r.json('result') !== undefined,
      });
      
      serviceSuccess ? successRate.add(1) : errorRate.add(1);
    });
    
    // Batch service execution
    group('Batch Service Execution', () => {
      const batchResponse = http.post(`${data.apiBase}/api/v1/services/batch`, JSON.stringify({
        requests: [
          {
            service: 'velian',
            action: 'analyze',
            data: { input: `Batch request 1 ${randomString(10)}` }
          },
          {
            service: 'zero-entropy',
            action: 'compress',
            data: { input: `Batch request 2 ${randomString(10)}` }
          },
          {
            service: 'clueso',
            action: 'search',
            data: { query: `search query ${randomString(5)}` }
          }
        ]
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        timeout: '120s',
      });
      
      const batchSuccess = check(batchResponse, {
        'batch execution status is 200': (r) => r.status === 200,
        'batch response has results': (r) => r.json('results') !== undefined,
        'batch response time < 5s': (r) => r.timings.duration < 5000,
        'batch has multiple results': (r) => r.json('results') && r.json('results').length > 0,
      });
      
      batchSuccess ? successRate.add(1) : errorRate.add(1);
    });
  });

  group('Workflow Execution Tests', () => {
    if (!authToken) return;
    
    // Get available workflows
    const workflowListResponse = http.get(`${data.apiBase}/api/v1/workflows`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      timeout: '30s',
    });
    
    if (workflowListResponse.status === 200) {
      const workflows = workflowListResponse.json('workflows');
      
      if (workflows && workflows.length > 0) {
        const workflowId = workflows[0].id;
        
        // Execute workflow
        const startTime = Date.now();
        
        const workflowResponse = http.post(`${data.apiBase}/api/v1/workflows/${workflowId}/execute`, JSON.stringify({
          input: {
            data: `Workflow test data ${randomString(15)}`,
            parameters: {
              parallel: true,
              timeout: 60000,
              priority: 'high'
            }
          },
          context: {
            userId: randomString(8),
            sessionId: randomString(12),
            metadata: {
              source: 'k6-load-test',
              timestamp: new Date().toISOString()
            }
          }
        }), {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          },
          timeout: '180s',
        });
        
        const workflowTime = Date.now() - startTime;
        workflowExecutionTime.add(workflowTime);
        
        const workflowSuccess = check(workflowResponse, {
          'workflow execution status is 200': (r) => r.status === 200,
          'workflow response has executionId': (r) => r.json('executionId') !== undefined,
          'workflow response time < 10s': (r) => r.timings.duration < 10000,
        });
        
        workflowSuccess ? successRate.add(1) : errorRate.add(1);
      }
    }
  });

  group('Analytics and Monitoring Tests', () => {
    if (!authToken) return;
    
    // Dashboard overview
    const overviewResponse = http.get(`${data.apiBase}/api/v1/analytics/overview?timeframe=1h&metrics=all`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      timeout: '30s',
    });
    
    check(overviewResponse, {
      'analytics overview status is 200': (r) => r.status === 200,
      'analytics response time < 1s': (r) => r.timings.duration < 1000,
    });
    
    // Real-time metrics
    const realtimeResponse = http.get(`${data.apiBase}/api/v1/analytics/realtime`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      timeout: '15s',
    });
    
    check(realtimeResponse, {
      'realtime metrics status is 200': (r) => r.status === 200,
      'realtime response time < 500ms': (r) => r.timings.duration < 500,
    });
  });

  group('API Key Authentication Tests', () => {
    // Service execution with API key
    apiKeyUsage.add(1);
    
    const apiKeyResponse = http.post(`${data.apiBase}/api/v1/services/execute`, JSON.stringify({
      service: 'velian',
      action: 'analyze',
      data: {
        input: `API key test ${randomString(10)}`,
        priority: 'normal'
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': TEST_API_KEY
      },
      timeout: '60s',
    });
    
    const apiKeySuccess = check(apiKeyResponse, {
      'API key auth status is 200 or 401': (r) => [200, 401].includes(r.status),
      'API key response time < 2s': (r) => r.timings.duration < 2000,
    });
    
    apiKeySuccess ? successRate.add(1) : errorRate.add(1);
  });

  group('Health Check Tests', () => {
    const healthResponse = http.get(`${data.apiBase}/health`, {
      timeout: '10s',
    });
    
    check(healthResponse, {
      'health check status is 200': (r) => r.status === 200,
      'health check response time < 100ms': (r) => r.timings.duration < 100,
      'health status is healthy': (r) => r.json('status') === 'healthy',
    });
  });

  // Simulate user think time
  sleep(randomIntBetween(1, 3));
}

// Teardown function - runs once at the end
export function teardown(data) {
  console.log('🏁 K6 load test teardown completed');
  
  // Generate summary report
  console.log('📊 Test Summary:');
  console.log(`- API Base: ${data.apiBase}`);
  console.log(`- Setup Complete: ${data.setupComplete}`);
  console.log('- Custom metrics collected for detailed analysis');
}

// Handle data for debugging
export function handleSummary(data) {
  return {
    'tests/performance/reports/k6-summary.json': JSON.stringify(data, null, 2),
    'tests/performance/reports/k6-summary.html': htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true })
  };
}

// Simple HTML report generator
function htmlReport(data) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>K6 Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
        h1, h2 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>K6 Load Test Report</h1>
    <h2>Test Summary</h2>
    <p>Generated: ${new Date().toISOString()}</p>
    
    <h2>Metrics</h2>
    <div class="metrics">
        ${Object.entries(data.metrics).map(([name, metric]) => `
            <div class="metric">
                <strong>${name}</strong>: ${JSON.stringify(metric.values, null, 2)}
            </div>
        `).join('')}
    </div>
    
    <h2>Thresholds</h2>
    <div class="thresholds">
        ${Object.entries(data.thresholds || {}).map(([name, threshold]) => `
            <div class="metric ${threshold.ok ? 'pass' : 'fail'}">
                <strong>${name}</strong>: ${threshold.ok ? 'PASSED' : 'FAILED'}
            </div>
        `).join('')}
    </div>
</body>
</html>
  `;
}

// Simple text summary
function textSummary(data, options = {}) {
  const indent = options.indent || '';
  const colors = options.enableColors ? true : false;
  
  let summary = `${indent}K6 Load Test Results\n`;
  summary += `${indent}=====================\n`;
  
  // Add basic metrics
  for (const [name, metric] of Object.entries(data.metrics)) {
    summary += `${indent}${name}: ${JSON.stringify(metric.values)}\n`;
  }
  
  return summary;
}