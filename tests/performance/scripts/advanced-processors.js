/**
 * Advanced Artillery Processors
 * Custom request processors for realistic load testing scenarios
 */

const crypto = require('crypto');

module.exports = {
  // Generate random string for unique data
  generateRandomString,
  
  // Generate random number within range
  generateRandomNumber,
  
  // Generate timestamp
  generateTimestamp,
  
  // Generate UUID
  generateUUID,
  
  // Process authentication response
  processAuthResponse,
  
  // Generate realistic test data
  generateTestData,
  
  // Process service response
  processServiceResponse,
  
  // Generate batch request
  generateBatchRequest,
  
  // Process error response
  processErrorResponse,
  
  // Custom think time calculator
  calculateThinkTime,
  
  // Rate limiting handler
  handleRateLimit,
  
  // Memory usage tracker
  trackMemoryUsage,
  
  // Performance metrics collector
  collectMetrics
};

/**
 * Generate random string of specified length
 */
function generateRandomString(requestParams, context, ee, next) {
  const length = requestParams.length || 10;
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  context.vars.randomString = result;
  return next();
}

/**
 * Generate random number within range
 */
function generateRandomNumber(requestParams, context, ee, next) {
  const min = requestParams.min || 1;
  const max = requestParams.max || 1000;
  const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
  
  context.vars.randomNumber = randomNum;
  return next();
}

/**
 * Generate timestamp
 */
function generateTimestamp(requestParams, context, ee, next) {
  context.vars.timestamp = new Date().toISOString();
  return next();
}

/**
 * Generate UUID v4
 */
function generateUUID(requestParams, context, ee, next) {
  context.vars.uuid = crypto.randomUUID();
  return next();
}

/**
 * Process authentication response and extract token
 */
function processAuthResponse(requestParams, response, context, ee, next) {
  if (response.statusCode === 200 && response.body) {
    try {
      const body = JSON.parse(response.body);
      if (body.token) {
        context.vars.authToken = body.token;
        console.log('Auth token captured successfully');
      }
      if (body.user) {
        context.vars.userId = body.user.id;
        context.vars.userEmail = body.user.email;
      }
    } catch (error) {
      console.error('Failed to parse auth response:', error);
    }
  }
  return next();
}

/**
 * Generate realistic test data based on service type
 */
function generateTestData(requestParams, context, ee, next) {
  const serviceType = requestParams.service || 'generic';
  let testData = {};
  
  switch (serviceType) {
    case 'velian':
      testData = {
        input: generateTextAnalysisData(),
        options: {
          analysisType: 'comprehensive',
          includeMetrics: true,
          format: 'json'
        }
      };
      break;
      
    case 'zero-entropy':
      testData = {
        input: generateCompressionData(),
        algorithm: 'optimal',
        level: Math.floor(Math.random() * 9) + 1
      };
      break;
      
    case 'clueso':
      testData = {
        query: generateSearchQuery(),
        filters: generateSearchFilters(),
        maxResults: Math.floor(Math.random() * 100) + 10
      };
      break;
      
    case 'hello-cv':
      testData = {
        resume: generateResumeData(),
        jobDescription: generateJobDescription(),
        analysisLevel: 'detailed'
      };
      break;
      
    default:
      testData = {
        input: `Generic test data ${crypto.randomUUID()}`,
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID()
      };
  }
  
  context.vars.testData = testData;
  return next();
}

/**
 * Process service response and validate
 */
function processServiceResponse(requestParams, response, context, ee, next) {
  const startTime = context.vars._startTime || Date.now();
  const responseTime = Date.now() - startTime;
  
  // Track response time
  context.vars.responseTime = responseTime;
  
  // Validate response structure
  if (response.statusCode === 200) {
    try {
      const body = JSON.parse(response.body);
      context.vars.responseValid = !!(body.success && body.result);
      context.vars.resultSize = JSON.stringify(body).length;
      
      // Extract specific metrics if available
      if (body.metrics) {
        context.vars.serviceMetrics = body.metrics;
      }
      
    } catch (error) {
      context.vars.responseValid = false;
      console.error('Invalid JSON response:', error);
    }
  } else {
    context.vars.responseValid = false;
    console.warn(`Service responded with status: ${response.statusCode}`);
  }
  
  return next();
}

/**
 * Generate batch request with multiple services
 */
function generateBatchRequest(requestParams, context, ee, next) {
  const batchSize = requestParams.batchSize || 3;
  const services = ['velian', 'zero-entropy', 'clueso', 'hello-cv', 'permut'];
  const batchRequests = [];
  
  for (let i = 0; i < batchSize; i++) {
    const service = services[Math.floor(Math.random() * services.length)];
    const actions = getServiceActions(service);
    const action = actions[Math.floor(Math.random() * actions.length)];
    
    batchRequests.push({
      service,
      action,
      data: {
        input: `Batch request ${i + 1} for ${service}`,
        batchId: crypto.randomUUID(),
        priority: Math.random() > 0.7 ? 'high' : 'normal'
      }
    });
  }
  
  context.vars.batchRequests = batchRequests;
  return next();
}

/**
 * Process error response and categorize
 */
function processErrorResponse(requestParams, response, context, ee, next) {
  if (response.statusCode >= 400) {
    let errorCategory = 'unknown';
    
    if (response.statusCode >= 400 && response.statusCode < 500) {
      errorCategory = 'client_error';
    } else if (response.statusCode >= 500) {
      errorCategory = 'server_error';
    }
    
    context.vars.errorCategory = errorCategory;
    context.vars.errorCode = response.statusCode;
    
    // Try to parse error message
    try {
      const body = JSON.parse(response.body);
      context.vars.errorMessage = body.error?.message || body.message || 'Unknown error';
    } catch (error) {
      context.vars.errorMessage = `HTTP ${response.statusCode} - ${response.statusMessage}`;
    }
    
    console.warn(`${errorCategory}: ${context.vars.errorMessage}`);
  }
  
  return next();
}

/**
 * Calculate dynamic think time based on user behavior
 */
function calculateThinkTime(requestParams, context, ee, next) {
  const baseThinkTime = requestParams.baseTime || 2000; // 2 seconds base
  const variation = requestParams.variation || 0.5; // 50% variation
  
  // Simulate realistic user behavior patterns
  const userType = context.vars.userType || 'normal';
  let multiplier = 1;
  
  switch (userType) {
    case 'power_user':
      multiplier = 0.5; // Power users are faster
      break;
    case 'casual_user':
      multiplier = 2; // Casual users take more time
      break;
    case 'mobile_user':
      multiplier = 1.5; // Mobile users slightly slower
      break;
    default:
      multiplier = 1;
  }
  
  const randomVariation = 1 + (Math.random() - 0.5) * variation;
  const thinkTime = Math.round(baseThinkTime * multiplier * randomVariation);
  
  context.vars.thinkTime = Math.max(100, thinkTime); // Minimum 100ms
  return next();
}

/**
 * Handle rate limiting with exponential backoff
 */
function handleRateLimit(requestParams, response, context, ee, next) {
  if (response.statusCode === 429) {
    const retryAfter = response.headers['retry-after'] || 1;
    const backoffTime = Math.min(30000, 1000 * Math.pow(2, context.vars.retryCount || 0));
    
    context.vars.retryCount = (context.vars.retryCount || 0) + 1;
    context.vars.backoffTime = Math.max(retryAfter * 1000, backoffTime);
    
    console.log(`Rate limited. Backing off for ${context.vars.backoffTime}ms`);
    
    setTimeout(() => {
      return next();
    }, context.vars.backoffTime);
  } else {
    context.vars.retryCount = 0; // Reset on success
    return next();
  }
}

/**
 * Track memory usage for the test process
 */
function trackMemoryUsage(requestParams, context, ee, next) {
  const memUsage = process.memoryUsage();
  
  context.vars.memoryUsage = {
    rss: Math.round(memUsage.rss / 1024 / 1024), // MB
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
    external: Math.round(memUsage.external / 1024 / 1024) // MB
  };
  
  // Warn if memory usage is high
  if (context.vars.memoryUsage.heapUsed > 100) {
    console.warn(`High memory usage: ${context.vars.memoryUsage.heapUsed}MB`);
  }
  
  return next();
}

/**
 * Collect custom performance metrics
 */
function collectMetrics(requestParams, response, context, ee, next) {
  const metrics = {
    timestamp: Date.now(),
    responseTime: context.vars.responseTime || 0,
    statusCode: response.statusCode,
    bodySize: response.body ? response.body.length : 0,
    memoryUsage: context.vars.memoryUsage || {}
  };
  
  // Emit custom metrics to Artillery
  ee.emit('counter', 'custom.requests', 1);
  ee.emit('histogram', 'custom.response_time', metrics.responseTime);
  ee.emit('histogram', 'custom.body_size', metrics.bodySize);
  
  if (response.statusCode >= 400) {
    ee.emit('counter', `custom.errors.${response.statusCode}`, 1);
  }
  
  return next();
}

// Helper functions for generating realistic test data

function generateTextAnalysisData() {
  const texts = [
    'The quick brown fox jumps over the lazy dog. This is a sample text for analysis.',
    'Performance testing is crucial for ensuring application reliability under load.',
    'Artificial intelligence and machine learning are transforming modern software development.',
    'Cloud computing platforms provide scalable infrastructure for modern applications.',
    'Microservices architecture enables better scalability and maintainability.'
  ];
  
  return texts[Math.floor(Math.random() * texts.length)];
}

function generateCompressionData() {
  const data = [];
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ';
  
  // Generate text with some repetition for better compression
  const patterns = ['test data ', 'compression sample ', 'performance load '];
  const pattern = patterns[Math.floor(Math.random() * patterns.length)];
  
  for (let i = 0; i < 100; i++) {
    data.push(pattern.repeat(Math.floor(Math.random() * 5) + 1));
  }
  
  return data.join('\n');
}

function generateSearchQuery() {
  const queries = [
    'performance optimization',
    'load testing best practices',
    'API response time',
    'database query optimization',
    'caching strategies',
    'microservices architecture',
    'cloud deployment patterns',
    'monitoring and alerting'
  ];
  
  return queries[Math.floor(Math.random() * queries.length)];
}

function generateSearchFilters() {
  return {
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    },
    category: ['technical', 'documentation', 'tutorial'][Math.floor(Math.random() * 3)],
    relevance: Math.random() > 0.5 ? 'high' : 'medium'
  };
}

function generateResumeData() {
  return {
    name: `Test Candidate ${Math.floor(Math.random() * 1000)}`,
    skills: ['JavaScript', 'Node.js', 'React', 'Python', 'Docker'],
    experience: Math.floor(Math.random() * 15) + 1,
    education: 'Bachelor of Computer Science'
  };
}

function generateJobDescription() {
  return {
    title: 'Software Engineer',
    requirements: ['JavaScript', 'Node.js', 'API development', 'Testing'],
    experienceLevel: ['junior', 'mid', 'senior'][Math.floor(Math.random() * 3)]
  };
}

function getServiceActions(service) {
  const actions = {
    'velian': ['analyze', 'process', 'transform'],
    'zero-entropy': ['compress', 'decompress', 'optimize'],
    'clueso': ['search', 'index', 'analyze'],
    'hello-cv': ['resume', 'skills', 'match'],
    'permut': ['generate', 'optimize', 'calculate'],
    'intervo': ['schedule', 'analyze', 'feedback'],
    'pixelesq': ['resize', 'filter', 'optimize'],
    'yoink-ui': ['generate', 'theme', 'responsive']
  };
  
  return actions[service] || ['process', 'analyze', 'execute'];
}