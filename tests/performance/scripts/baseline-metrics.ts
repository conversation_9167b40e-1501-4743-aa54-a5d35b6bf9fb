#!/usr/bin/env tsx

/**
 * Baseline Performance Metrics Collection Script
 * Collects baseline performance metrics for all API endpoints
 */

import { BaselineCollector } from '../metrics/baseline-collector';
import { PERFORMANCE_CONFIG } from '../config/performance-config';
import * as fs from 'fs/promises';
import * as path from 'path';

async function main() {
  console.log('🎯 Starting Baseline Performance Metrics Collection');
  console.log('==================================================');

  const collector = new BaselineCollector();
  
  try {
    // Start collection
    await collector.startCollection();
    
    // Run comprehensive baseline test suite
    const report = await collector.runBaselineSuite();
    
    // Save the report
    const reportPath = await collector.saveReport(report);
    
    // Display summary
    console.log('\n📈 Baseline Performance Summary');
    console.log('===============================');
    console.log(`📊 Total Requests: ${report.totalRequests}`);
    console.log(`⏱️  Test Duration: ${Math.round(report.duration / 1000)}s`);
    console.log(`📍 Response Times:`);
    console.log(`   • Median (p50): ${Math.round(report.summary.p50)}ms`);
    console.log(`   • 95th percentile: ${Math.round(report.summary.p95)}ms`);
    console.log(`   • 99th percentile: ${Math.round(report.summary.p99)}ms`);
    console.log(`   • Average: ${Math.round(report.summary.avgResponseTime)}ms`);
    console.log(`🔄 Throughput: ${Math.round(report.summary.throughput)} req/sec`);
    console.log(`❌ Error Rate: ${(report.summary.errorRate * 100).toFixed(2)}%`);
    console.log(`💾 Average Memory: ${Math.round(report.summary.avgMemoryUsage / 1024 / 1024)}MB`);
    console.log(`🔥 Average CPU: ${Math.round(report.summary.avgCpuUsage / 1000)}ms`);
    
    // Check against thresholds
    console.log('\n🎯 Threshold Analysis');
    console.log('====================');
    
    const thresholds = PERFORMANCE_CONFIG.defaultThresholds;
    const violations: string[] = [];
    
    if (report.summary.p50 > thresholds.p50) {
      violations.push(`❌ P50 (${Math.round(report.summary.p50)}ms) exceeds threshold (${thresholds.p50}ms)`);
    } else {
      console.log(`✅ P50 (${Math.round(report.summary.p50)}ms) within threshold (${thresholds.p50}ms)`);
    }
    
    if (report.summary.p95 > thresholds.p95) {
      violations.push(`❌ P95 (${Math.round(report.summary.p95)}ms) exceeds threshold (${thresholds.p95}ms)`);
    } else {
      console.log(`✅ P95 (${Math.round(report.summary.p95)}ms) within threshold (${thresholds.p95}ms)`);
    }
    
    if (report.summary.p99 > thresholds.p99) {
      violations.push(`❌ P99 (${Math.round(report.summary.p99)}ms) exceeds threshold (${thresholds.p99}ms)`);
    } else {
      console.log(`✅ P99 (${Math.round(report.summary.p99)}ms) within threshold (${thresholds.p99}ms)`);
    }
    
    if (report.summary.errorRate > thresholds.maxErrorRate) {
      violations.push(`❌ Error rate (${(report.summary.errorRate * 100).toFixed(2)}%) exceeds threshold (${(thresholds.maxErrorRate * 100).toFixed(2)}%)`);
    } else {
      console.log(`✅ Error rate (${(report.summary.errorRate * 100).toFixed(2)}%) within threshold (${(thresholds.maxErrorRate * 100).toFixed(2)}%)`);
    }
    
    if (report.summary.throughput < thresholds.minThroughput) {
      violations.push(`❌ Throughput (${Math.round(report.summary.throughput)} req/sec) below minimum (${thresholds.minThroughput} req/sec)`);
    } else {
      console.log(`✅ Throughput (${Math.round(report.summary.throughput)} req/sec) above minimum (${thresholds.minThroughput} req/sec)`);
    }
    
    // Endpoint-specific analysis
    console.log('\n🔍 Endpoint Performance Analysis');
    console.log('=================================');
    
    const endpointMetrics = new Map<string, number[]>();
    
    for (const metric of report.metrics) {
      if (!endpointMetrics.has(metric.endpoint)) {
        endpointMetrics.set(metric.endpoint, []);
      }
      endpointMetrics.get(metric.endpoint)!.push(metric.responseTime);
    }
    
    for (const [endpoint, responseTimes] of endpointMetrics) {
      const sorted = responseTimes.sort((a, b) => a - b);
      const p95 = sorted[Math.ceil(sorted.length * 0.95) - 1] || 0;
      const avg = sorted.reduce((a, b) => a + b, 0) / sorted.length;
      
      const budget = PERFORMANCE_CONFIG.endpointBudgets[endpoint];
      const status = budget && p95 <= budget.p95 ? '✅' : '⚠️';
      
      console.log(`${status} ${endpoint}:`);
      console.log(`   • Average: ${Math.round(avg)}ms`);
      console.log(`   • P95: ${Math.round(p95)}ms ${budget ? `(budget: ${budget.p95}ms)` : ''}`);
      console.log(`   • Requests: ${responseTimes.length}`);
    }
    
    // Create baseline file for future comparisons
    const baselineDir = path.join(PERFORMANCE_CONFIG.reporting.outputDir, 'baselines');
    await fs.mkdir(baselineDir, { recursive: true });
    
    const baselineFile = path.join(baselineDir, 'current-baseline.json');
    await fs.writeFile(baselineFile, JSON.stringify(report, null, 2));
    console.log(`\n📋 Current baseline saved: ${baselineFile}`);
    
    // Generate HTML report
    await generateHTMLReport(report, reportPath);
    
    // Final summary
    console.log('\n🏁 Baseline Collection Complete');
    console.log('===============================');
    console.log(`📄 Report: ${reportPath}`);
    console.log(`📊 Metrics: ${report.metrics.length} data points collected`);
    
    if (violations.length > 0) {
      console.log('\n⚠️  PERFORMANCE VIOLATIONS DETECTED:');
      violations.forEach(violation => console.log(`   ${violation}`));
      process.exit(1);
    } else {
      console.log('\n✅ All performance thresholds met!');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('💥 Error during baseline collection:', error);
    process.exit(1);
  } finally {
    await collector.stopCollection();
  }
}

async function generateHTMLReport(report: any, reportPath: string): Promise<void> {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Baseline Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2, h3 { color: #333; margin-top: 0; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; font-size: 0.9em; }
        .violation { border-left-color: #dc3545; }
        .violation .metric-value { color: #dc3545; }
        .success { border-left-color: #28a745; }
        .success .metric-value { color: #28a745; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .chart { width: 100%; height: 300px; margin: 20px 0; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>🎯 Performance Baseline Report</h1>
        <p class="timestamp">Generated: ${new Date(report.timestamp).toLocaleString()}</p>
        <p>Environment: <strong>${report.environment}</strong> | Version: <strong>${report.version}</strong></p>
        
        <h2>📊 Performance Summary</h2>
        <div class="summary-grid">
            <div class="metric-card ${report.summary.p50 <= PERFORMANCE_CONFIG.defaultThresholds.p50 ? 'success' : 'violation'}">
                <div class="metric-value">${Math.round(report.summary.p50)}ms</div>
                <div class="metric-label">Median Response Time (P50)</div>
            </div>
            <div class="metric-card ${report.summary.p95 <= PERFORMANCE_CONFIG.defaultThresholds.p95 ? 'success' : 'violation'}">
                <div class="metric-value">${Math.round(report.summary.p95)}ms</div>
                <div class="metric-label">95th Percentile (P95)</div>
            </div>
            <div class="metric-card ${report.summary.p99 <= PERFORMANCE_CONFIG.defaultThresholds.p99 ? 'success' : 'violation'}">
                <div class="metric-value">${Math.round(report.summary.p99)}ms</div>
                <div class="metric-label">99th Percentile (P99)</div>
            </div>
            <div class="metric-card ${report.summary.throughput >= PERFORMANCE_CONFIG.defaultThresholds.minThroughput ? 'success' : 'violation'}">
                <div class="metric-value">${Math.round(report.summary.throughput)}</div>
                <div class="metric-label">Throughput (req/sec)</div>
            </div>
            <div class="metric-card ${report.summary.errorRate <= PERFORMANCE_CONFIG.defaultThresholds.maxErrorRate ? 'success' : 'violation'}">
                <div class="metric-value">${(report.summary.errorRate * 100).toFixed(2)}%</div>
                <div class="metric-label">Error Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${Math.round(report.summary.avgMemoryUsage / 1024 / 1024)}</div>
                <div class="metric-label">Average Memory (MB)</div>
            </div>
        </div>
        
        <h2>📈 Response Time Distribution</h2>
        <canvas id="responseTimeChart" class="chart"></canvas>
        
        <h2>🔍 Endpoint Performance</h2>
        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Requests</th>
                    <th>Avg Response (ms)</th>
                    <th>P95 (ms)</th>
                    <th>Error Rate</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                ${generateEndpointRows(report.metrics)}
            </tbody>
        </table>
        
        <h2>💾 Memory Usage Over Time</h2>
        <canvas id="memoryChart" class="chart"></canvas>
        
        <h2>📋 Raw Metrics</h2>
        <details>
            <summary>View detailed metrics data</summary>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto;">
${JSON.stringify(report, null, 2)}
            </pre>
        </details>
    </div>
    
    <script>
        // Response Time Distribution Chart
        const rtCtx = document.getElementById('responseTimeChart').getContext('2d');
        const responseTimes = ${JSON.stringify(report.metrics.map((m: any) => m.responseTime))};
        
        new Chart(rtCtx, {
            type: 'histogram',
            data: {
                datasets: [{
                    label: 'Response Time Distribution',
                    data: responseTimes,
                    backgroundColor: 'rgba(0, 123, 255, 0.5)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Response Time Distribution'
                    }
                }
            }
        });
        
        // Memory Usage Chart  
        const memCtx = document.getElementById('memoryChart').getContext('2d');
        const memoryData = ${JSON.stringify(report.metrics.map((m: any) => ({
          x: m.timestamp,
          y: Math.round(m.memoryUsage.heapUsed / 1024 / 1024)
        })))};
        
        new Chart(memCtx, {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Memory Usage (MB)',
                    data: memoryData,
                    borderColor: 'rgba(40, 167, 69, 1)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                second: 'HH:mm:ss'
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Memory Usage Over Time'
                    }
                }
            }
        });
    </script>
</body>
</html>
  `;
  
  const htmlPath = reportPath.replace('.json', '.html');
  await fs.writeFile(htmlPath, htmlContent);
  console.log(`📊 HTML report generated: ${htmlPath}`);
}

function generateEndpointRows(metrics: any[]): string {
  const endpointStats = new Map();
  
  metrics.forEach(metric => {
    const key = `${metric.method} ${metric.endpoint}`;
    if (!endpointStats.has(key)) {
      endpointStats.set(key, {
        endpoint: metric.endpoint,
        method: metric.method,
        responseTimes: [],
        errors: 0,
        total: 0
      });
    }
    
    const stats = endpointStats.get(key);
    stats.responseTimes.push(metric.responseTime);
    stats.errors += metric.errorRate;
    stats.total += 1;
  });
  
  return Array.from(endpointStats.values()).map(stats => {
    const avg = stats.responseTimes.reduce((a: number, b: number) => a + b, 0) / stats.responseTimes.length;
    const sorted = stats.responseTimes.sort((a: number, b: number) => a - b);
    const p95 = sorted[Math.ceil(sorted.length * 0.95) - 1] || 0;
    const errorRate = (stats.errors / stats.total) * 100;
    
    const budget = PERFORMANCE_CONFIG.endpointBudgets[stats.endpoint];
    const status = budget && p95 <= budget.p95 ? '✅' : '⚠️';
    
    return `
      <tr>
        <td>${stats.endpoint}</td>
        <td>${stats.method}</td>
        <td>${stats.total}</td>
        <td>${Math.round(avg)}</td>
        <td>${Math.round(p95)}</td>
        <td>${errorRate.toFixed(2)}%</td>
        <td>${status}</td>
      </tr>
    `;
  }).join('');
}

if (require.main === module) {
  main().catch(console.error);
}