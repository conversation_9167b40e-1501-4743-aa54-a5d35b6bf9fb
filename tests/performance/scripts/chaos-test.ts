#!/usr/bin/env tsx

/**
 * Chaos Engineering Test Script
 * Tests system resilience under various failure conditions
 */

import { spawn, ChildProcess } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import axios from 'axios';
import { performance } from 'perf_hooks';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

interface ChaosExperiment {
  name: string;
  description: string;
  type: 'network' | 'memory' | 'cpu' | 'disk' | 'service';
  enabled: boolean;
  duration: number; // seconds
  severity: 'low' | 'medium' | 'high';
  execute: () => Promise<void>;
  cleanup: () => Promise<void>;
}

interface ChaosResult {
  experiment: string;
  startTime: number;
  endTime: number;
  duration: number;
  baseline: any;
  duringChaos: any;
  recovery: any;
  impact: {
    responseTimeDegradation: number;
    throughputDegradation: number;
    errorRateIncrease: number;
    recoveryTime: number;
  };
  success: boolean;
  notes: string[];
}

class ChaosTestRunner {
  private results: ChaosResult[] = [];
  private baselineMetrics: any = null;
  private activeProcesses: ChildProcess[] = [];

  constructor(
    private readonly baseUrl: string = PERFORMANCE_CONFIG.apiBase,
    private readonly outputDir: string = PERFORMANCE_CONFIG.reporting.outputDir
  ) {}

  /**
   * Run comprehensive chaos engineering tests
   */
  async runChaosTests(): Promise<void> {
    console.log('🌪️  Starting Chaos Engineering Tests');
    console.log('=====================================');

    if (!PERFORMANCE_CONFIG.chaos.enabled) {
      console.log('⚠️  Chaos testing disabled - set CHAOS_ENABLED=true to enable');
      return;
    }

    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });

    // Collect baseline metrics
    console.log('📊 Collecting baseline metrics...');
    this.baselineMetrics = await this.collectMetrics();
    console.log(`✅ Baseline: ${Math.round(this.baselineMetrics.avgResponseTime)}ms avg, ${Math.round(this.baselineMetrics.throughput)} req/sec`);

    // Define chaos experiments
    const experiments = this.createChaosExperiments();

    // Run each enabled experiment
    for (const experiment of experiments) {
      if (!experiment.enabled) {
        console.log(`⏭️  Skipping disabled experiment: ${experiment.name}`);
        continue;
      }

      console.log(`\n🧪 Running Chaos Experiment: ${experiment.name}`);
      console.log(`   Description: ${experiment.description}`);
      console.log(`   Type: ${experiment.type}, Severity: ${experiment.severity}`);
      console.log(`   Duration: ${experiment.duration}s`);

      try {
        const result = await this.runSingleExperiment(experiment);
        this.results.push(result);

        // Display immediate results
        console.log('   Results:');
        console.log(`   • Response Time Impact: ${result.impact.responseTimeDegradation.toFixed(1)}%`);
        console.log(`   • Throughput Impact: ${result.impact.throughputDegradation.toFixed(1)}%`);
        console.log(`   • Error Rate Increase: ${result.impact.errorRateIncrease.toFixed(2)}%`);
        console.log(`   • Recovery Time: ${Math.round(result.impact.recoveryTime / 1000)}s`);
        console.log(`   • Status: ${result.success ? '✅ System Resilient' : '❌ System Impacted'}`);

        // Cooldown between experiments
        console.log('   ⏸️  Cooldown period (60s)...');
        await this.sleep(60000);

      } catch (error) {
        console.error(`❌ Experiment ${experiment.name} failed:`, error);
      }
    }

    // Generate comprehensive report
    await this.generateChaosReport();
  }

  /**
   * Create chaos experiments based on configuration
   */
  private createChaosExperiments(): ChaosExperiment[] {
    const experiments: ChaosExperiment[] = [];

    // Network Latency Experiment
    if (PERFORMANCE_CONFIG.chaos.scenarios.networkLatency.enabled) {
      experiments.push({
        name: 'Network Latency Injection',
        description: 'Inject network latency to simulate slow connections',
        type: 'network',
        enabled: true,
        duration: 120,
        severity: 'medium',
        execute: async () => {
          // Simulate network latency using tc (traffic control)
          const latencyMs = PERFORMANCE_CONFIG.chaos.scenarios.networkLatency.latencyMs;
          console.log(`   🌐 Injecting ${latencyMs}ms network latency...`);
          // Note: This would require sudo privileges in real implementation
          // For testing purposes, we'll simulate the effect
          await this.simulateNetworkLatency(latencyMs);
        },
        cleanup: async () => {
          console.log('   🧹 Removing network latency...');
          await this.removeNetworkLatency();
        }
      });
    }

    // Memory Pressure Experiment
    if (PERFORMANCE_CONFIG.chaos.scenarios.memoryPressure.enabled) {
      experiments.push({
        name: 'Memory Pressure',
        description: 'Create memory pressure to test memory management',
        type: 'memory',
        enabled: true,
        duration: 180,
        severity: 'high',
        execute: async () => {
          const pressureMB = PERFORMANCE_CONFIG.chaos.scenarios.memoryPressure.pressureMB;
          console.log(`   💾 Creating ${pressureMB}MB memory pressure...`);
          await this.createMemoryPressure(pressureMB);
        },
        cleanup: async () => {
          console.log('   🧹 Releasing memory pressure...');
          await this.releaseMemoryPressure();
        }
      });
    }

    // CPU Stress Experiment  
    if (PERFORMANCE_CONFIG.chaos.scenarios.cpuStress.enabled) {
      experiments.push({
        name: 'CPU Stress Test',
        description: 'Create CPU stress to test under high CPU load',
        type: 'cpu',
        enabled: true,
        duration: PERFORMANCE_CONFIG.chaos.scenarios.cpuStress.durationSec,
        severity: 'high',
        execute: async () => {
          console.log('   🔥 Starting CPU stress...');
          await this.createCPUStress();
        },
        cleanup: async () => {
          console.log('   🧹 Stopping CPU stress...');
          await this.stopCPUStress();
        }
      });
    }

    // Service Dependency Failure
    if (PERFORMANCE_CONFIG.chaos.scenarios.serviceDown.enabled) {
      experiments.push({
        name: 'Service Dependency Failure',
        description: 'Simulate external service failures',
        type: 'service',
        enabled: true,
        duration: PERFORMANCE_CONFIG.chaos.scenarios.serviceDown.durationSec,
        severity: 'medium',
        execute: async () => {
          console.log('   🚫 Simulating service dependencies failure...');
          await this.simulateServiceFailure();
        },
        cleanup: async () => {
          console.log('   🧹 Restoring service dependencies...');
          await this.restoreServices();
        }
      });
    }

    // Random Process Killer
    experiments.push({
      name: 'Random Process Termination',
      description: 'Randomly terminate worker processes to test recovery',
      type: 'service',
      enabled: true,
      duration: 90,
      severity: 'high',
      execute: async () => {
        console.log('   💀 Randomly terminating processes...');
        await this.randomProcessKiller();
      },
      cleanup: async () => {
        console.log('   🧹 Process termination complete...');
        // No specific cleanup needed - processes should auto-restart
      }
    });

    return experiments;
  }

  /**
   * Run a single chaos experiment
   */
  private async runSingleExperiment(experiment: ChaosExperiment): Promise<ChaosResult> {
    const startTime = performance.now();
    
    // Phase 1: Pre-chaos baseline
    const preMetrics = await this.collectMetrics();
    
    // Phase 2: Execute chaos
    await experiment.execute();
    await this.sleep(2000); // Allow chaos to propagate
    
    // Phase 3: Measure during chaos
    const chaosMetrics = await this.collectMetrics();
    
    // Phase 4: Wait for chaos duration
    await this.sleep((experiment.duration - 2) * 1000);
    
    // Phase 5: Cleanup chaos
    await experiment.cleanup();
    
    // Phase 6: Measure recovery
    const recoveryStartTime = performance.now();
    let recoveredMetrics;
    let recoveryTime = 0;
    
    // Wait for system to recover (max 5 minutes)
    for (let i = 0; i < 60; i++) {
      await this.sleep(5000);
      recoveredMetrics = await this.collectMetrics();
      
      // Check if system has recovered to acceptable levels
      if (this.hasRecovered(preMetrics, recoveredMetrics)) {
        recoveryTime = performance.now() - recoveryStartTime;
        break;
      }
    }
    
    const endTime = performance.now();
    
    // Calculate impact
    const impact = {
      responseTimeDegradation: ((chaosMetrics.avgResponseTime - preMetrics.avgResponseTime) / preMetrics.avgResponseTime) * 100,
      throughputDegradation: ((preMetrics.throughput - chaosMetrics.throughput) / preMetrics.throughput) * 100,
      errorRateIncrease: (chaosMetrics.errorRate - preMetrics.errorRate) * 100,
      recoveryTime
    };

    // Determine success criteria
    const success = this.evaluateExperimentSuccess(experiment, impact, chaosMetrics);
    
    return {
      experiment: experiment.name,
      startTime,
      endTime,
      duration: endTime - startTime,
      baseline: preMetrics,
      duringChaos: chaosMetrics,
      recovery: recoveredMetrics,
      impact,
      success,
      notes: this.generateExperimentNotes(experiment, impact, chaosMetrics)
    };
  }

  /**
   * Collect system metrics
   */
  private async collectMetrics(): Promise<any> {
    const samples = [];
    const sampleCount = 5;
    
    for (let i = 0; i < sampleCount; i++) {
      try {
        const start = performance.now();
        const response = await axios.get(`${this.baseUrl}/health`, { timeout: 10000 });
        const responseTime = performance.now() - start;
        
        samples.push({
          responseTime,
          statusCode: response.status,
          success: response.status >= 200 && response.status < 400
        });
        
        await this.sleep(200); // Small delay between samples
      } catch (error) {
        samples.push({
          responseTime: 10000, // Timeout
          statusCode: 0,
          success: false
        });
      }
    }
    
    const successfulSamples = samples.filter(s => s.success);
    const avgResponseTime = successfulSamples.length > 0 
      ? successfulSamples.reduce((sum, s) => sum + s.responseTime, 0) / successfulSamples.length
      : 10000;
    
    return {
      avgResponseTime,
      throughput: successfulSamples.length > 0 ? (1000 / avgResponseTime) * sampleCount : 0,
      errorRate: (samples.length - successfulSamples.length) / samples.length,
      successfulRequests: successfulSamples.length,
      totalRequests: samples.length,
      timestamp: Date.now()
    };
  }

  /**
   * Check if system has recovered from chaos
   */
  private hasRecovered(baseline: any, current: any): boolean {
    const responseTimeOk = current.avgResponseTime <= baseline.avgResponseTime * 1.2; // 20% tolerance
    const throughputOk = current.throughput >= baseline.throughput * 0.8; // 20% tolerance  
    const errorRateOk = current.errorRate <= baseline.errorRate + 0.05; // 5% tolerance
    
    return responseTimeOk && throughputOk && errorRateOk;
  }

  /**
   * Evaluate experiment success
   */
  private evaluateExperimentSuccess(experiment: ChaosExperiment, impact: any, chaosMetrics: any): boolean {
    // Success criteria based on severity
    switch (experiment.severity) {
      case 'low':
        return impact.responseTimeDegradation < 50 && impact.errorRateIncrease < 5;
      case 'medium':
        return impact.responseTimeDegradation < 200 && impact.errorRateIncrease < 15;
      case 'high':
        return impact.responseTimeDegradation < 500 && impact.errorRateIncrease < 30;
      default:
        return true;
    }
  }

  /**
   * Generate experiment notes
   */
  private generateExperimentNotes(experiment: ChaosExperiment, impact: any, metrics: any): string[] {
    const notes = [];
    
    if (impact.responseTimeDegradation > 100) {
      notes.push('High response time degradation detected');
    }
    
    if (impact.throughputDegradation > 50) {
      notes.push('Significant throughput loss observed');
    }
    
    if (impact.errorRateIncrease > 10) {
      notes.push('Error rate increased substantially');
    }
    
    if (impact.recoveryTime > 60000) {
      notes.push('Slow recovery time - consider auto-healing mechanisms');
    }
    
    if (metrics.errorRate > 0.2) {
      notes.push('Critical error rate reached during chaos');
    }
    
    if (notes.length === 0) {
      notes.push('System showed good resilience under chaos conditions');
    }
    
    return notes;
  }

  // Chaos injection methods (simplified implementations)
  
  private async simulateNetworkLatency(latencyMs: number): Promise<void> {
    // In a real implementation, this would use tc (traffic control) or similar
    // For testing, we'll simulate by adding delays to requests
    console.log(`   Simulating ${latencyMs}ms network latency...`);
  }

  private async removeNetworkLatency(): Promise<void> {
    console.log('   Removing network latency simulation...');
  }

  private async createMemoryPressure(pressureMB: number): Promise<void> {
    // Create memory pressure by allocating memory
    const process = spawn('node', ['-e', `
      const chunks = [];
      const chunkSize = 1024 * 1024; // 1MB chunks
      const targetMB = ${pressureMB};
      
      console.log('Creating memory pressure...');
      for (let i = 0; i < targetMB; i++) {
        chunks.push(Buffer.alloc(chunkSize, 'a'));
        if (i % 100 === 0) process.stdout.write('.');
      }
      
      console.log('\\nMemory pressure created, holding for duration...');
      setTimeout(() => {
        console.log('Memory pressure test complete');
        process.exit(0);
      }, 180000); // Hold for 3 minutes
    `]);
    
    this.activeProcesses.push(process);
  }

  private async releaseMemoryPressure(): Promise<void> {
    // Kill memory pressure processes
    this.activeProcesses.forEach(proc => {
      if (!proc.killed) {
        proc.kill('SIGTERM');
      }
    });
    this.activeProcesses = [];
  }

  private async createCPUStress(): Promise<void> {
    // Create CPU stress using multiple worker processes
    const cpuCount = require('os').cpus().length;
    
    for (let i = 0; i < cpuCount; i++) {
      const process = spawn('node', ['-e', `
        console.log('Starting CPU stress worker ${i}...');
        const startTime = Date.now();
        const duration = ${PERFORMANCE_CONFIG.chaos.scenarios.cpuStress.durationSec * 1000};
        
        while (Date.now() - startTime < duration) {
          Math.random() * Math.random();
        }
        
        console.log('CPU stress worker ${i} complete');
      `]);
      
      this.activeProcesses.push(process);
    }
  }

  private async stopCPUStress(): Promise<void> {
    this.activeProcesses.forEach(proc => {
      if (!proc.killed) {
        proc.kill('SIGTERM');
      }
    });
    this.activeProcesses = [];
  }

  private async simulateServiceFailure(): Promise<void> {
    // Simulate by blocking certain endpoints or services
    console.log('   Simulating external service failures...');
    // In real implementation, this could use network policies or service mesh
  }

  private async restoreServices(): Promise<void> {
    console.log('   Restoring service connectivity...');
  }

  private async randomProcessKiller(): Promise<void> {
    // In production, this would kill random worker processes
    // For testing, we'll just simulate the effect
    console.log('   Simulating random process termination...');
  }

  /**
   * Generate chaos engineering report
   */
  private async generateChaosReport(): Promise<void> {
    const timestamp = new Date().toISOString();
    const reportData = {
      timestamp,
      testType: 'chaos-engineering',
      environment: process.env.NODE_ENV || 'test',
      target: this.baseUrl,
      baseline: this.baselineMetrics,
      experiments: this.results,
      summary: this.generateChaosSummary(),
      recommendations: this.generateChaosRecommendations()
    };
    
    // Save JSON report
    const jsonPath = path.join(this.outputDir, `chaos-test-${timestamp.replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(reportData, null, 2));
    
    // Generate HTML report
    await this.generateChaosHTMLReport(reportData, jsonPath);
    
    console.log('\n🌪️  Chaos Engineering Test Complete');
    console.log('=====================================');
    console.log(`📄 Report: ${jsonPath}`);
    console.log(`🧪 Experiments Completed: ${this.results.length}`);
    
    const summary = this.generateChaosSummary();
    console.log(`✅ Successful: ${summary.successful}`);
    console.log(`❌ Failed: ${summary.failed}`);
    console.log(`📊 Average Impact: ${summary.averageImpact.toFixed(1)}%`);
    console.log(`⏱️  Average Recovery: ${Math.round(summary.averageRecovery / 1000)}s`);
    
    // Display key findings
    if (summary.criticalFindings.length > 0) {
      console.log('\n⚠️  Critical Findings:');
      summary.criticalFindings.forEach(finding => console.log(`   • ${finding}`));
    }
  }

  private generateChaosSummary() {
    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.length - successful;
    
    const averageImpact = this.results.reduce((sum, r) => 
      sum + Math.max(r.impact.responseTimeDegradation, r.impact.throughputDegradation), 0
    ) / this.results.length;
    
    const averageRecovery = this.results.reduce((sum, r) => sum + r.impact.recoveryTime, 0) / this.results.length;
    
    const criticalFindings = [];
    
    // Analyze for critical issues
    for (const result of this.results) {
      if (result.impact.responseTimeDegradation > 300) {
        criticalFindings.push(`${result.experiment}: Severe response time degradation (${result.impact.responseTimeDegradation.toFixed(1)}%)`);
      }
      if (result.impact.errorRateIncrease > 20) {
        criticalFindings.push(`${result.experiment}: High error rate increase (${result.impact.errorRateIncrease.toFixed(1)}%)`);
      }
      if (result.impact.recoveryTime > 120000) {
        criticalFindings.push(`${result.experiment}: Slow recovery (${Math.round(result.impact.recoveryTime / 1000)}s)`);
      }
    }
    
    return {
      successful,
      failed,
      averageImpact,
      averageRecovery,
      criticalFindings,
      totalExperiments: this.results.length
    };
  }

  private generateChaosRecommendations(): string[] {
    const recommendations = [];
    
    const highImpactExperiments = this.results.filter(r => 
      r.impact.responseTimeDegradation > 200 || r.impact.errorRateIncrease > 15
    );
    
    const slowRecoveryExperiments = this.results.filter(r => r.impact.recoveryTime > 60000);
    
    if (highImpactExperiments.length > 0) {
      recommendations.push('Implement circuit breakers for high-impact failure scenarios');
      recommendations.push('Add graceful degradation mechanisms for critical services');
    }
    
    if (slowRecoveryExperiments.length > 0) {
      recommendations.push('Implement auto-healing and faster recovery mechanisms');
      recommendations.push('Add health checks with automatic service restart');
    }
    
    recommendations.push('Monitor system metrics continuously in production');
    recommendations.push('Set up alerts for chaos engineering key indicators');
    recommendations.push('Implement bulkhead patterns to isolate failures');
    recommendations.push('Consider implementing retry mechanisms with exponential backoff');
    
    return recommendations;
  }

  private async generateChaosHTMLReport(reportData: any, jsonPath: string): Promise<void> {
    // Similar to stress test HTML report but focused on chaos experiments
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chaos Engineering Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .experiment-card { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .success { border-left: 4px solid #28a745; }
        .failure { border-left: 4px solid #dc3545; }
        .critical { background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; }
        .metric { display: flex; justify-content: space-between; margin: 8px 0; }
        .chart { width: 100%; height: 400px; margin: 20px 0; }
        h1, h2, h3 { color: #333; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>🌪️ Chaos Engineering Report</h1>
        <p><strong>Generated:</strong> ${new Date(reportData.timestamp).toLocaleString()}</p>
        <p><strong>Target:</strong> ${reportData.target}</p>
        
        <h2>📊 Summary</h2>
        <div class="metric">
            <span>Total Experiments:</span>
            <span>${reportData.summary.totalExperiments}</span>
        </div>
        <div class="metric">
            <span>Successful:</span>
            <span>${reportData.summary.successful} ✅</span>
        </div>
        <div class="metric">
            <span>Failed:</span>
            <span>${reportData.summary.failed} ❌</span>
        </div>
        <div class="metric">
            <span>Average Impact:</span>
            <span>${reportData.summary.averageImpact.toFixed(1)}%</span>
        </div>
        <div class="metric">
            <span>Average Recovery:</span>
            <span>${Math.round(reportData.summary.averageRecovery / 1000)}s</span>
        </div>
        
        ${reportData.summary.criticalFindings.length > 0 ? `
        <div class="critical" style="padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>⚠️ Critical Findings</h3>
            <ul>
                ${reportData.summary.criticalFindings.map((finding: string) => `<li>${finding}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        <h2>🧪 Experiment Results</h2>
        ${reportData.experiments.map((exp: any) => `
        <div class="experiment-card ${exp.success ? 'success' : 'failure'}">
            <h3>${exp.experiment}</h3>
            <div class="metric">
                <span>Duration:</span>
                <span>${Math.round(exp.duration / 1000)}s</span>
            </div>
            <div class="metric">
                <span>Response Time Impact:</span>
                <span>${exp.impact.responseTimeDegradation.toFixed(1)}%</span>
            </div>
            <div class="metric">
                <span>Throughput Impact:</span>
                <span>${exp.impact.throughputDegradation.toFixed(1)}%</span>
            </div>
            <div class="metric">
                <span>Error Rate Increase:</span>
                <span>${exp.impact.errorRateIncrease.toFixed(2)}%</span>
            </div>
            <div class="metric">
                <span>Recovery Time:</span>
                <span>${Math.round(exp.impact.recoveryTime / 1000)}s</span>
            </div>
            <div class="metric">
                <span>Status:</span>
                <span>${exp.success ? '✅ Resilient' : '❌ Impacted'}</span>
            </div>
            ${exp.notes.length > 0 ? `
            <h4>Notes:</h4>
            <ul>
                ${exp.notes.map((note: string) => `<li>${note}</li>`).join('')}
            </ul>
            ` : ''}
        </div>
        `).join('')}
        
        <h2>💡 Recommendations</h2>
        <ul>
            ${reportData.recommendations.map((rec: string) => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
</body>
</html>
    `;
    
    const htmlPath = jsonPath.replace('.json', '.html');
    await fs.writeFile(htmlPath, htmlContent);
    console.log(`📊 HTML chaos report: ${htmlPath}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  const runner = new ChaosTestRunner();
  await runner.runChaosTests();
}

if (require.main === module) {
  main().catch(console.error);
}