#!/usr/bin/env tsx

/**
 * Performance Report Generator
 * Generates comprehensive performance reports from test results
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

interface TestResult {
  type: string;
  timestamp: string;
  duration: number;
  summary: any;
  data: any;
}

interface ComprehensiveReport {
  metadata: {
    generated: string;
    environment: string;
    target: string;
    reportType: 'comprehensive-performance';
    version: string;
  };
  executive_summary: {
    overall_performance: string;
    key_findings: string[];
    critical_issues: string[];
    recommendations: string[];
    performance_score: number;
  };
  test_results: {
    baseline: any;
    stress_test: any;
    chaos_engineering: any;
    monitoring: any;
    load_tests: any[];
  };
  comparative_analysis: {
    before_after?: any;
    trend_analysis?: any;
    benchmark_comparison?: any;
  };
  performance_budget_analysis: {
    endpoint_budgets: any[];
    violations: any[];
    compliance_score: number;
  };
  recommendations: {
    immediate_actions: string[];
    short_term: string[];
    long_term: string[];
    monitoring_setup: string[];
  };
  appendices: {
    raw_data: any;
    methodology: string;
    tools_used: string[];
    test_environment: any;
  };
}

class PerformanceReportGenerator {
  constructor(
    private readonly outputDir: string = PERFORMANCE_CONFIG.reporting.outputDir
  ) {}

  /**
   * Generate comprehensive performance report
   */
  async generateComprehensiveReport(): Promise<void> {
    console.log('📊 Generating Comprehensive Performance Report');
    console.log('==============================================');

    try {
      // Collect all test results
      const testResults = await this.collectTestResults();
      
      if (Object.keys(testResults).length === 0) {
        console.log('⚠️  No test results found. Run some performance tests first.');
        return;
      }

      // Generate comprehensive report
      const report = await this.buildComprehensiveReport(testResults);
      
      // Save reports in multiple formats
      await this.saveReports(report);
      
      // Generate visualizations
      await this.generateVisualizations(testResults);
      
      console.log('\n✅ Comprehensive performance report generated successfully!');
      
    } catch (error) {
      console.error('❌ Failed to generate performance report:', error);
      throw error;
    }
  }

  /**
   * Collect all available test results
   */
  private async collectTestResults(): Promise<Record<string, TestResult[]>> {
    const results: Record<string, TestResult[]> = {
      baseline: [],
      stress: [],
      chaos: [],
      monitoring: [],
      load: []
    };

    try {
      const files = await fs.readdir(this.outputDir);
      
      for (const file of files) {
        if (!file.endsWith('.json')) continue;
        
        try {
          const filePath = path.join(this.outputDir, file);
          const content = JSON.parse(await fs.readFile(filePath, 'utf-8'));
          
          // Categorize based on test type
          if (file.includes('baseline')) {
            results.baseline.push({
              type: 'baseline',
              timestamp: content.timestamp || file,
              duration: content.duration || 0,
              summary: content.summary,
              data: content
            });
          } else if (file.includes('stress')) {
            results.stress.push({
              type: 'stress',
              timestamp: content.timestamp || file,
              duration: content.duration || 0,
              summary: content.summary,
              data: content
            });
          } else if (file.includes('chaos')) {
            results.chaos.push({
              type: 'chaos',
              timestamp: content.timestamp || file,
              duration: content.duration || 0,
              summary: content.summary,
              data: content
            });
          } else if (file.includes('monitoring')) {
            results.monitoring.push({
              type: 'monitoring',
              timestamp: content.timestamp || file,
              duration: content.duration || 0,
              summary: content.summary,
              data: content
            });
          } else if (file.includes('load') || file.includes('artillery') || file.includes('k6')) {
            results.load.push({
              type: 'load',
              timestamp: content.timestamp || file,
              duration: content.duration || 0,
              summary: content.summary,
              data: content
            });
          }
          
        } catch (error) {
          console.warn(`⚠️  Failed to parse ${file}:`, error);
        }
      }
      
    } catch (error) {
      console.warn('⚠️  Failed to read test results directory:', error);
    }

    return results;
  }

  /**
   * Build comprehensive performance report
   */
  private async buildComprehensiveReport(testResults: Record<string, TestResult[]>): Promise<ComprehensiveReport> {
    const metadata = {
      generated: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'test',
      target: PERFORMANCE_CONFIG.apiBase,
      reportType: 'comprehensive-performance' as const,
      version: process.env.npm_package_version || '1.0.0'
    };

    // Executive Summary
    const executiveSummary = this.generateExecutiveSummary(testResults);
    
    // Performance Budget Analysis
    const budgetAnalysis = this.analyzePerfomanceBudgets(testResults);
    
    // Comparative Analysis
    const comparativeAnalysis = this.generateComparativeAnalysis(testResults);
    
    // Recommendations
    const recommendations = this.generateRecommendations(testResults, budgetAnalysis);

    return {
      metadata,
      executive_summary: executiveSummary,
      test_results: {
        baseline: testResults.baseline[testResults.baseline.length - 1] || null,
        stress_test: testResults.stress[testResults.stress.length - 1] || null,
        chaos_engineering: testResults.chaos[testResults.chaos.length - 1] || null,
        monitoring: testResults.monitoring[testResults.monitoring.length - 1] || null,
        load_tests: testResults.load
      },
      comparative_analysis: comparativeAnalysis,
      performance_budget_analysis: budgetAnalysis,
      recommendations,
      appendices: {
        raw_data: testResults,
        methodology: this.getTestingMethodology(),
        tools_used: ['Artillery', 'K6', 'Autocannon', 'Custom Baseline Collector', 'Chaos Engineering Framework'],
        test_environment: {
          target: PERFORMANCE_CONFIG.apiBase,
          node_version: process.version,
          platform: process.platform,
          arch: process.arch,
          memory: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        }
      }
    };
  }

  /**
   * Generate executive summary
   */
  private generateExecutiveSummary(testResults: Record<string, TestResult[]>): any {
    const keyFindings = [];
    const criticalIssues = [];
    let performanceScore = 100;

    // Analyze baseline results
    const latestBaseline = testResults.baseline[testResults.baseline.length - 1];
    if (latestBaseline) {
      const baseline = latestBaseline.summary;
      if (baseline.p95 > PERFORMANCE_CONFIG.defaultThresholds.p95) {
        criticalIssues.push(`P95 response time (${Math.round(baseline.p95)}ms) exceeds threshold`);
        performanceScore -= 20;
      }
      if (baseline.errorRate > PERFORMANCE_CONFIG.defaultThresholds.maxErrorRate) {
        criticalIssues.push(`Error rate (${(baseline.errorRate * 100).toFixed(2)}%) too high`);
        performanceScore -= 30;
      }
      keyFindings.push(`Baseline P95 response time: ${Math.round(baseline.p95)}ms`);
      keyFindings.push(`Baseline throughput: ${Math.round(baseline.throughput)} req/sec`);
    }

    // Analyze stress test results
    const latestStress = testResults.stress[testResults.stress.length - 1];
    if (latestStress && latestStress.data.breakingPoint) {
      keyFindings.push(`Breaking point reached at ${latestStress.data.breakingPoint.connections} connections`);
      performanceScore -= 10;
    } else if (latestStress) {
      keyFindings.push('System handled all stress test phases successfully');
      performanceScore += 10;
    }

    // Analyze chaos engineering results
    const latestChaos = testResults.chaos[testResults.chaos.length - 1];
    if (latestChaos) {
      const chaosData = latestChaos.data;
      if (chaosData.summary && chaosData.summary.failed > chaosData.summary.successful) {
        criticalIssues.push(`${chaosData.summary.failed} chaos experiments failed`);
        performanceScore -= 25;
      } else if (chaosData.summary) {
        keyFindings.push(`${chaosData.summary.successful} chaos experiments passed`);
        performanceScore += 5;
      }
    }

    // Overall performance assessment
    let overallPerformance = 'Excellent';
    if (performanceScore < 60) overallPerformance = 'Poor';
    else if (performanceScore < 75) overallPerformance = 'Fair';
    else if (performanceScore < 90) overallPerformance = 'Good';

    return {
      overall_performance: overallPerformance,
      key_findings: keyFindings,
      critical_issues: criticalIssues,
      recommendations: this.getTopRecommendations(criticalIssues),
      performance_score: Math.max(0, Math.min(100, performanceScore))
    };
  }

  /**
   * Analyze performance budgets
   */
  private analyzePerfomanceBudgets(testResults: Record<string, TestResult[]>): any {
    const endpointBudgets = [];
    const violations = [];
    let compliantEndpoints = 0;
    let totalEndpoints = 0;

    const baseline = testResults.baseline[testResults.baseline.length - 1];
    if (baseline && baseline.data.metrics) {
      const endpointMetrics = new Map();
      
      // Group metrics by endpoint
      baseline.data.metrics.forEach((metric: any) => {
        if (!endpointMetrics.has(metric.endpoint)) {
          endpointMetrics.set(metric.endpoint, []);
        }
        endpointMetrics.get(metric.endpoint).push(metric.responseTime);
      });

      // Analyze each endpoint against budget
      for (const [endpoint, responseTimes] of endpointMetrics) {
        totalEndpoints++;
        const sorted = (responseTimes as number[]).sort((a, b) => a - b);
        const p95 = sorted[Math.ceil(sorted.length * 0.95) - 1] || 0;
        
        const budget = PERFORMANCE_CONFIG.endpointBudgets[endpoint];
        const isCompliant = budget ? p95 <= budget.p95 : true;
        
        if (isCompliant) compliantEndpoints++;
        
        const budgetInfo = {
          endpoint,
          actual_p95: Math.round(p95),
          budget_p95: budget?.p95 || null,
          compliant: isCompliant,
          variance: budget ? ((p95 - budget.p95) / budget.p95) * 100 : null
        };

        endpointBudgets.push(budgetInfo);

        if (!isCompliant && budget) {
          violations.push({
            endpoint,
            metric: 'P95 Response Time',
            actual: Math.round(p95),
            budget: budget.p95,
            severity: p95 > budget.p95 * 2 ? 'critical' : 'warning'
          });
        }
      }
    }

    return {
      endpoint_budgets: endpointBudgets,
      violations,
      compliance_score: totalEndpoints > 0 ? Math.round((compliantEndpoints / totalEndpoints) * 100) : 100
    };
  }

  /**
   * Generate comparative analysis
   */
  private generateComparativeAnalysis(testResults: Record<string, TestResult[]>): any {
    const analysis: any = {};

    // Trend analysis if multiple baseline results exist
    if (testResults.baseline.length > 1) {
      const oldest = testResults.baseline[0];
      const newest = testResults.baseline[testResults.baseline.length - 1];
      
      analysis.trend_analysis = {
        time_span: `${oldest.timestamp} to ${newest.timestamp}`,
        response_time_trend: newest.summary.p95 - oldest.summary.p95,
        throughput_trend: newest.summary.throughput - oldest.summary.throughput,
        error_rate_trend: newest.summary.errorRate - oldest.summary.errorRate
      };
    }

    // Benchmark comparison against industry standards
    const latestBaseline = testResults.baseline[testResults.baseline.length - 1];
    if (latestBaseline) {
      analysis.benchmark_comparison = {
        response_time: {
          actual: Math.round(latestBaseline.summary.p95),
          industry_standard: 200, // Industry standard for API response time
          status: latestBaseline.summary.p95 <= 200 ? 'meets_standard' : 'below_standard'
        },
        throughput: {
          actual: Math.round(latestBaseline.summary.throughput),
          target: PERFORMANCE_CONFIG.defaultThresholds.minThroughput,
          status: latestBaseline.summary.throughput >= PERFORMANCE_CONFIG.defaultThresholds.minThroughput ? 'meets_target' : 'below_target'
        },
        availability: {
          actual: Math.round((1 - latestBaseline.summary.errorRate) * 100),
          target: 99.9, // 99.9% uptime target
          status: latestBaseline.summary.errorRate <= 0.001 ? 'meets_sla' : 'below_sla'
        }
      };
    }

    return analysis;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(testResults: Record<string, TestResult[]>, budgetAnalysis: any): any {
    const immediate = [];
    const shortTerm = [];
    const longTerm = [];
    const monitoring = [];

    // Based on critical issues
    if (budgetAnalysis.violations.length > 0) {
      immediate.push('Address performance budget violations immediately');
      budgetAnalysis.violations.forEach((v: any) => {
        if (v.severity === 'critical') {
          immediate.push(`Critical: Optimize ${v.endpoint} (${v.actual}ms > ${v.budget}ms)`);
        }
      });
    }

    // Based on stress test results
    const stressTest = testResults.stress[testResults.stress.length - 1];
    if (stressTest && stressTest.data.breakingPoint) {
      shortTerm.push(`Scale horizontally before ${stressTest.data.breakingPoint.connections} connections`);
      shortTerm.push('Implement circuit breakers and rate limiting');
    }

    // Based on chaos engineering results
    const chaosTest = testResults.chaos[testResults.chaos.length - 1];
    if (chaosTest && chaosTest.data.summary && chaosTest.data.summary.failed > 0) {
      shortTerm.push('Improve system resilience based on chaos engineering failures');
      immediate.push('Implement better error handling and recovery mechanisms');
    }

    // General long-term recommendations
    longTerm.push('Implement comprehensive observability stack');
    longTerm.push('Set up automated performance regression testing');
    longTerm.push('Establish performance engineering culture');
    longTerm.push('Regular capacity planning and scaling exercises');

    // Monitoring recommendations
    monitoring.push('Set up real-time performance dashboards');
    monitoring.push('Configure alerting for SLA violations');
    monitoring.push('Implement distributed tracing');
    monitoring.push('Monitor business metrics alongside technical metrics');

    return {
      immediate_actions: immediate,
      short_term: shortTerm,
      long_term: longTerm,
      monitoring_setup: monitoring
    };
  }

  /**
   * Get top recommendations based on critical issues
   */
  private getTopRecommendations(criticalIssues: string[]): string[] {
    const recommendations = [];
    
    if (criticalIssues.some(issue => issue.includes('response time'))) {
      recommendations.push('Optimize slow API endpoints immediately');
    }
    
    if (criticalIssues.some(issue => issue.includes('error rate'))) {
      recommendations.push('Investigate and fix high error rates');
    }
    
    if (criticalIssues.some(issue => issue.includes('experiments failed'))) {
      recommendations.push('Improve system resilience and error handling');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Continue monitoring and maintain current performance levels');
    }

    return recommendations.slice(0, 3); // Top 3 recommendations
  }

  /**
   * Get testing methodology description
   */
  private getTestingMethodology(): string {
    return `
Performance testing was conducted using a comprehensive methodology including:

1. Baseline Performance Testing:
   - Measured baseline performance across all API endpoints
   - Collected statistical metrics (P50, P95, P99)
   - Established performance budgets

2. Stress Testing:
   - Progressive load increases to find breaking points
   - Multiple phases from light to extreme load
   - System recovery assessment

3. Chaos Engineering:
   - Network latency injection
   - Memory and CPU stress testing
   - Service dependency failures
   - Recovery time measurement

4. Continuous Monitoring:
   - Real-time metrics collection
   - Automated alerting
   - Trend analysis

All tests were executed against the same target environment with consistent configuration.
Statistical significance was ensured through multiple test iterations and proper sample sizes.
    `.trim();
  }

  /**
   * Save reports in multiple formats
   */
  private async saveReports(report: ComprehensiveReport): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save JSON report
    const jsonPath = path.join(this.outputDir, `comprehensive-report-${timestamp}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));
    console.log(`📄 JSON report saved: ${jsonPath}`);

    // Save HTML report
    const htmlPath = await this.generateHTMLReport(report, jsonPath);
    console.log(`🌐 HTML report saved: ${htmlPath}`);

    // Save CSV summary
    const csvPath = await this.generateCSVSummary(report, timestamp);
    console.log(`📊 CSV summary saved: ${csvPath}`);

    // Save executive summary (text)
    const txtPath = await this.generateTextSummary(report, timestamp);
    console.log(`📝 Text summary saved: ${txtPath}`);
  }

  /**
   * Generate HTML report
   */
  private async generateHTMLReport(report: ComprehensiveReport, jsonPath: string): Promise<string> {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Performance Report</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 0.5rem; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .executive-summary { background: white; border-radius: 10px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .score-circle { width: 120px; height: 120px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2em; font-weight: bold; margin: 0 auto 1rem; }
        .score-excellent { background: linear-gradient(135deg, #4CAF50, #45a049); color: white; }
        .score-good { background: linear-gradient(135deg, #2196F3, #1976D2); color: white; }
        .score-fair { background: linear-gradient(135deg, #FF9800, #F57C00); color: white; }
        .score-poor { background: linear-gradient(135deg, #f44336, #d32f2f); color: white; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0; }
        .card { background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .card h3 { color: #667eea; margin-bottom: 1rem; border-bottom: 2px solid #e0e6ff; padding-bottom: 0.5rem; }
        .metric { display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f0f0f0; }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: 600; }
        .status-excellent { color: #4CAF50; }
        .status-good { color: #2196F3; }
        .status-warning { color: #FF9800; }
        .status-critical { color: #f44336; }
        .recommendations { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 2rem; }
        .recommendations h2 { margin-bottom: 1rem; }
        .recommendations ul { list-style-type: none; }
        .recommendations li { margin: 0.5rem 0; padding-left: 1.5rem; position: relative; }
        .recommendations li:before { content: '→'; position: absolute; left: 0; color: #a8d8ff; }
        .table { width: 100%; border-collapse: collapse; margin: 1rem 0; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .alert { padding: 1rem; border-radius: 5px; margin: 1rem 0; }
        .alert-critical { background: #ffebee; border: 1px solid #ffcdd2; color: #c62828; }
        .alert-warning { background: #fff3e0; border: 1px solid #ffcc02; color: #ef6c00; }
        .alert-success { background: #e8f5e8; border: 1px solid #c8e6c9; color: #2e7d32; }
        .chart-placeholder { background: #f8f9fa; border: 2px dashed #dee2e6; padding: 2rem; text-align: center; color: #6c757d; border-radius: 5px; margin: 1rem 0; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 Performance Report</h1>
            <p>Comprehensive Analysis • Generated ${new Date(report.metadata.generated).toLocaleDateString()}</p>
            <p>Environment: ${report.metadata.environment} • Target: ${report.metadata.target}</p>
        </div>

        <!-- Executive Summary -->
        <div class="executive-summary">
            <h2>Executive Summary</h2>
            <div style="display: flex; align-items: center; margin: 2rem 0;">
                <div class="score-circle score-${report.executive_summary.overall_performance.toLowerCase()}">
                    ${report.executive_summary.performance_score}
                </div>
                <div style="margin-left: 2rem;">
                    <h3>Overall Performance: ${report.executive_summary.overall_performance}</h3>
                    <p>Performance Score: ${report.executive_summary.performance_score}/100</p>
                </div>
            </div>
            
            ${report.executive_summary.critical_issues.length > 0 ? `
            <div class="alert alert-critical">
                <h4>🚨 Critical Issues</h4>
                <ul>
                    ${report.executive_summary.critical_issues.map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
            ` : `
            <div class="alert alert-success">
                <h4>✅ No Critical Issues Found</h4>
                <p>System performance is within acceptable parameters.</p>
            </div>
            `}
        </div>

        <!-- Key Findings -->
        <div class="grid">
            <div class="card">
                <h3>🔍 Key Findings</h3>
                ${report.executive_summary.key_findings.length > 0 ? `
                <ul>
                    ${report.executive_summary.key_findings.map(finding => `<li>${finding}</li>`).join('')}
                </ul>
                ` : '<p>No specific findings to report.</p>'}
            </div>

            <div class="card">
                <h3>📈 Performance Budget Compliance</h3>
                <div class="metric">
                    <span>Compliance Score:</span>
                    <span class="metric-value ${report.performance_budget_analysis.compliance_score >= 90 ? 'status-excellent' : 
                                                 report.performance_budget_analysis.compliance_score >= 75 ? 'status-good' : 
                                                 report.performance_budget_analysis.compliance_score >= 60 ? 'status-warning' : 'status-critical'}">
                        ${report.performance_budget_analysis.compliance_score}%
                    </span>
                </div>
                <div class="metric">
                    <span>Budget Violations:</span>
                    <span class="metric-value ${report.performance_budget_analysis.violations.length === 0 ? 'status-excellent' : 'status-warning'}">
                        ${report.performance_budget_analysis.violations.length}
                    </span>
                </div>
            </div>
        </div>

        <!-- Test Results Overview -->
        <div class="card">
            <h3>🧪 Test Results Overview</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Test Type</th>
                        <th>Status</th>
                        <th>Key Metric</th>
                        <th>Result</th>
                        <th>Assessment</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Baseline Performance</td>
                        <td>${report.test_results.baseline ? '✅ Completed' : '❌ Not Run'}</td>
                        <td>P95 Response Time</td>
                        <td>${report.test_results.baseline ? `${Math.round(report.test_results.baseline.summary.p95)}ms` : 'N/A'}</td>
                        <td class="${report.test_results.baseline && report.test_results.baseline.summary.p95 <= PERFORMANCE_CONFIG.defaultThresholds.p95 ? 'status-excellent' : 'status-warning'}">
                            ${report.test_results.baseline && report.test_results.baseline.summary.p95 <= PERFORMANCE_CONFIG.defaultThresholds.p95 ? 'Within Budget' : 'Exceeds Budget'}
                        </td>
                    </tr>
                    <tr>
                        <td>Stress Testing</td>
                        <td>${report.test_results.stress_test ? '✅ Completed' : '❌ Not Run'}</td>
                        <td>Breaking Point</td>
                        <td>${report.test_results.stress_test && report.test_results.stress_test.data.breakingPoint ? 
                            `${report.test_results.stress_test.data.breakingPoint.connections} connections` : 
                            report.test_results.stress_test ? 'Not reached' : 'N/A'}</td>
                        <td class="${report.test_results.stress_test && !report.test_results.stress_test.data.breakingPoint ? 'status-excellent' : 'status-good'}">
                            ${report.test_results.stress_test && !report.test_results.stress_test.data.breakingPoint ? 'Excellent' : 'Good'}
                        </td>
                    </tr>
                    <tr>
                        <td>Chaos Engineering</td>
                        <td>${report.test_results.chaos_engineering ? '✅ Completed' : '❌ Not Run'}</td>
                        <td>Resilience Score</td>
                        <td>${report.test_results.chaos_engineering && report.test_results.chaos_engineering.data.summary ? 
                            `${report.test_results.chaos_engineering.data.summary.successful}/${report.test_results.chaos_engineering.data.summary.totalExperiments}` : 'N/A'}</td>
                        <td class="${report.test_results.chaos_engineering && report.test_results.chaos_engineering.data.summary && 
                                    report.test_results.chaos_engineering.data.summary.successful >= report.test_results.chaos_engineering.data.summary.failed ? 'status-excellent' : 'status-warning'}">
                            ${report.test_results.chaos_engineering && report.test_results.chaos_engineering.data.summary && 
                              report.test_results.chaos_engineering.data.summary.successful >= report.test_results.chaos_engineering.data.summary.failed ? 'Resilient' : 'Needs Work'}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Performance Budget Violations -->
        ${report.performance_budget_analysis.violations.length > 0 ? `
        <div class="card">
            <h3>⚠️ Performance Budget Violations</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Endpoint</th>
                        <th>Metric</th>
                        <th>Actual</th>
                        <th>Budget</th>
                        <th>Severity</th>
                    </tr>
                </thead>
                <tbody>
                    ${report.performance_budget_analysis.violations.map(v => `
                    <tr>
                        <td>${v.endpoint}</td>
                        <td>${v.metric}</td>
                        <td>${v.actual}</td>
                        <td>${v.budget}</td>
                        <td class="status-${v.severity === 'critical' ? 'critical' : 'warning'}">${v.severity}</td>
                    </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}

        <!-- Recommendations -->
        <div class="recommendations">
            <h2>💡 Recommendations</h2>
            
            ${report.recommendations.immediate_actions.length > 0 ? `
            <h3>🚨 Immediate Actions Required</h3>
            <ul>
                ${report.recommendations.immediate_actions.map(action => `<li>${action}</li>`).join('')}
            </ul>
            ` : ''}
            
            <h3>📋 Short-term Improvements</h3>
            <ul>
                ${report.recommendations.short_term.map(action => `<li>${action}</li>`).join('')}
            </ul>
            
            <h3>🎯 Long-term Strategy</h3>
            <ul>
                ${report.recommendations.long_term.map(action => `<li>${action}</li>`).join('')}
            </ul>
            
            <h3>📊 Monitoring Setup</h3>
            <ul>
                ${report.recommendations.monitoring_setup.map(action => `<li>${action}</li>`).join('')}
            </ul>
        </div>

        <!-- Methodology -->
        <div class="card">
            <h3>🔬 Testing Methodology</h3>
            <p style="white-space: pre-wrap;">${report.appendices.methodology}</p>
            
            <h4 style="margin-top: 1rem;">Tools Used:</h4>
            <ul>
                ${report.appendices.tools_used.map(tool => `<li>${tool}</li>`).join('')}
            </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding: 2rem; color: #666;">
            <p>Report generated on ${new Date(report.metadata.generated).toLocaleString()}</p>
            <p>For detailed raw data, see: ${path.basename(jsonPath)}</p>
        </div>
    </div>
</body>
</html>
    `;

    const htmlPath = jsonPath.replace('.json', '.html');
    await fs.writeFile(htmlPath, htmlContent);
    return htmlPath;
  }

  /**
   * Generate CSV summary
   */
  private async generateCSVSummary(report: ComprehensiveReport, timestamp: string): Promise<string> {
    const csvRows = [
      'Metric,Value,Status,Threshold',
      `Performance Score,${report.executive_summary.performance_score},${report.executive_summary.overall_performance},100`,
      `Budget Compliance,${report.performance_budget_analysis.compliance_score}%,${report.performance_budget_analysis.compliance_score >= 90 ? 'Good' : 'Warning'},90%`,
      `Budget Violations,${report.performance_budget_analysis.violations.length},${report.performance_budget_analysis.violations.length === 0 ? 'Good' : 'Warning'},0`
    ];

    if (report.test_results.baseline) {
      const baseline = report.test_results.baseline.summary;
      csvRows.push(`Baseline P95,${Math.round(baseline.p95)}ms,${baseline.p95 <= PERFORMANCE_CONFIG.defaultThresholds.p95 ? 'Good' : 'Warning'},${PERFORMANCE_CONFIG.defaultThresholds.p95}ms`);
      csvRows.push(`Baseline Throughput,${Math.round(baseline.throughput)},${baseline.throughput >= PERFORMANCE_CONFIG.defaultThresholds.minThroughput ? 'Good' : 'Warning'},${PERFORMANCE_CONFIG.defaultThresholds.minThroughput}`);
      csvRows.push(`Baseline Error Rate,${(baseline.errorRate * 100).toFixed(2)}%,${baseline.errorRate <= PERFORMANCE_CONFIG.defaultThresholds.maxErrorRate ? 'Good' : 'Warning'},${(PERFORMANCE_CONFIG.defaultThresholds.maxErrorRate * 100).toFixed(2)}%`);
    }

    const csvPath = path.join(this.outputDir, `performance-summary-${timestamp}.csv`);
    await fs.writeFile(csvPath, csvRows.join('\n'));
    return csvPath;
  }

  /**
   * Generate text summary
   */
  private async generateTextSummary(report: ComprehensiveReport, timestamp: string): Promise<string> {
    const summary = `
PERFORMANCE REPORT EXECUTIVE SUMMARY
Generated: ${new Date(report.metadata.generated).toLocaleString()}
Environment: ${report.metadata.environment}
Target: ${report.metadata.target}

OVERALL ASSESSMENT: ${report.executive_summary.overall_performance} (${report.executive_summary.performance_score}/100)

KEY FINDINGS:
${report.executive_summary.key_findings.map(finding => `• ${finding}`).join('\n')}

CRITICAL ISSUES:
${report.executive_summary.critical_issues.length > 0 ? 
  report.executive_summary.critical_issues.map(issue => `• ${issue}`).join('\n') :
  '• No critical issues found'}

PERFORMANCE BUDGET COMPLIANCE: ${report.performance_budget_analysis.compliance_score}%
• Budget violations: ${report.performance_budget_analysis.violations.length}

IMMEDIATE ACTIONS REQUIRED:
${report.recommendations.immediate_actions.length > 0 ?
  report.recommendations.immediate_actions.map(action => `• ${action}`).join('\n') :
  '• Continue monitoring current performance levels'}

SHORT-TERM IMPROVEMENTS:
${report.recommendations.short_term.map(action => `• ${action}`).join('\n')}

For detailed analysis and charts, see the HTML report.
    `.trim();

    const txtPath = path.join(this.outputDir, `executive-summary-${timestamp}.txt`);
    await fs.writeFile(txtPath, summary);
    return txtPath;
  }

  /**
   * Generate visualizations (placeholder for future chart generation)
   */
  private async generateVisualizations(testResults: Record<string, TestResult[]>): Promise<void> {
    console.log('📈 Visualization generation completed (charts embedded in HTML report)');
    // In a real implementation, this could generate separate chart files
    // using libraries like Chart.js, D3.js, or server-side chart generation
  }
}

// Main execution
async function main() {
  const generator = new PerformanceReportGenerator();
  await generator.generateComprehensiveReport();
}

if (require.main === module) {
  main().catch(console.error);
}