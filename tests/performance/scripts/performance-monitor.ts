#!/usr/bin/env tsx

/**
 * Real-time Performance Monitor
 * Continuous monitoring of system performance with alerting
 */

import axios from 'axios';
import * as fs from 'fs/promises';
import * as path from 'path';
import { performance } from 'perf_hooks';
import { EventEmitter } from 'events';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

interface PerformanceMetric {
  timestamp: number;
  endpoint: string;
  responseTime: number;
  statusCode: number;
  error: boolean;
  memory: NodeJS.MemoryUsage;
  cpu: {
    user: number;
    system: number;
  };
}

interface SystemHealth {
  timestamp: number;
  responseTime: {
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
  queueLength: number;
}

interface Alert {
  id: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'response_time' | 'error_rate' | 'throughput' | 'memory' | 'cpu' | 'availability';
  message: string;
  value: number;
  threshold: number;
  resolved: boolean;
}

class PerformanceMonitor extends EventEmitter {
  private metrics: PerformanceMetric[] = [];
  private healthHistory: SystemHealth[] = [];
  private activeAlerts: Map<string, Alert> = new Map();
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;
  private healthInterval?: NodeJS.Timeout;
  private alertInterval?: NodeJS.Timeout;

  constructor(
    private readonly baseUrl: string = PERFORMANCE_CONFIG.apiBase,
    private readonly outputDir: string = PERFORMANCE_CONFIG.reporting.outputDir,
    private readonly monitoringIntervalMs: number = PERFORMANCE_CONFIG.monitoring.metricsInterval
  ) {
    super();
    this.setupEventHandlers();
  }

  /**
   * Start real-time performance monitoring
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚠️ Monitoring already running');
      return;
    }

    console.log('🔍 Starting Real-time Performance Monitoring');
    console.log('===========================================');
    console.log(`Target: ${this.baseUrl}`);
    console.log(`Interval: ${this.monitoringIntervalMs}ms`);
    console.log(`Output: ${this.outputDir}\n`);

    this.isMonitoring = true;
    
    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });

    // Start monitoring intervals
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, this.monitoringIntervalMs);

    this.healthInterval = setInterval(() => {
      this.calculateSystemHealth();
    }, this.monitoringIntervalMs * 2); // Calculate health every 10s

    this.alertInterval = setInterval(() => {
      this.checkAlerts();
    }, this.monitoringIntervalMs * 6); // Check alerts every 30s

    // Initial metrics collection
    await this.collectMetrics();
    await this.calculateSystemHealth();

    console.log('✅ Performance monitoring started');
    console.log('📊 Collecting metrics...\n');

    // Display real-time updates
    this.startRealTimeDisplay();
  }

  /**
   * Stop performance monitoring
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      console.log('⚠️ Monitoring not running');
      return;
    }

    console.log('\n🛑 Stopping performance monitoring...');

    this.isMonitoring = false;

    // Clear intervals
    if (this.monitoringInterval) clearInterval(this.monitoringInterval);
    if (this.healthInterval) clearInterval(this.healthInterval);
    if (this.alertInterval) clearInterval(this.alertInterval);

    // Generate final report
    await this.generateMonitoringReport();

    console.log('✅ Monitoring stopped');
  }

  /**
   * Collect performance metrics
   */
  private async collectMetrics(): Promise<void> {
    const endpoints = [
      '/health',
      '/api/v1/',
      '/api/v1/analytics/overview',
      '/api/v1/services/execute' // This might fail without auth, but useful for monitoring
    ];

    for (const endpoint of endpoints) {
      try {
        const startTime = performance.now();
        const startCpu = process.cpuUsage();
        
        const response = await axios.get(`${this.baseUrl}${endpoint}`, {
          timeout: 10000,
          validateStatus: () => true // Don't throw on any status
        });
        
        const endTime = performance.now();
        const endCpu = process.cpuUsage(startCpu);
        
        const metric: PerformanceMetric = {
          timestamp: Date.now(),
          endpoint,
          responseTime: endTime - startTime,
          statusCode: response.status,
          error: response.status >= 400,
          memory: process.memoryUsage(),
          cpu: {
            user: endCpu.user,
            system: endCpu.system
          }
        };

        this.metrics.push(metric);
        this.emit('metric', metric);

        // Keep only last 1000 metrics to prevent memory issues
        if (this.metrics.length > 1000) {
          this.metrics = this.metrics.slice(-1000);
        }

      } catch (error: any) {
        const metric: PerformanceMetric = {
          timestamp: Date.now(),
          endpoint,
          responseTime: 10000, // Timeout
          statusCode: 0,
          error: true,
          memory: process.memoryUsage(),
          cpu: { user: 0, system: 0 }
        };

        this.metrics.push(metric);
        this.emit('error-metric', metric, error.message);
      }
    }
  }

  /**
   * Calculate system health metrics
   */
  private async calculateSystemHealth(): Promise<SystemHealth> {
    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp < 60000 // Last minute
    );

    if (recentMetrics.length === 0) {
      return {
        timestamp: Date.now(),
        responseTime: { avg: 0, p50: 0, p95: 0, p99: 0 },
        throughput: 0,
        errorRate: 1,
        memoryUsage: 0,
        cpuUsage: 0,
        activeConnections: 0,
        queueLength: 0
      };
    }

    // Calculate response time metrics
    const responseTimes = recentMetrics.map(m => m.responseTime).sort((a, b) => a - b);
    const avgResponseTime = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;

    const responseTimeMetrics = {
      avg: avgResponseTime,
      p50: this.percentile(responseTimes, 0.5),
      p95: this.percentile(responseTimes, 0.95),
      p99: this.percentile(responseTimes, 0.99)
    };

    // Calculate other metrics
    const errorCount = recentMetrics.filter(m => m.error).length;
    const errorRate = errorCount / recentMetrics.length;
    const throughput = recentMetrics.length / 60; // requests per second

    // Memory and CPU metrics
    const latestMemory = recentMetrics[recentMetrics.length - 1]?.memory.heapUsed || 0;
    const avgCpu = recentMetrics.reduce((sum, m) => sum + m.cpu.user + m.cpu.system, 0) / recentMetrics.length;

    const health: SystemHealth = {
      timestamp: Date.now(),
      responseTime: responseTimeMetrics,
      throughput,
      errorRate,
      memoryUsage: latestMemory / 1024 / 1024, // MB
      cpuUsage: avgCpu / 1000, // Convert to percentage approximation
      activeConnections: recentMetrics.length, // Approximation
      queueLength: 0 // Would need to query actual queue
    };

    this.healthHistory.push(health);
    this.emit('health-update', health);

    // Keep only last 100 health records
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }

    return health;
  }

  /**
   * Check for performance alerts
   */
  private async checkAlerts(): Promise<void> {
    const latestHealth = this.healthHistory[this.healthHistory.length - 1];
    if (!latestHealth) return;

    const thresholds = PERFORMANCE_CONFIG.monitoring.alertThresholds;

    // Response time alerts
    if (latestHealth.responseTime.p95 > thresholds.responseTime) {
      this.createAlert('response_time', 'high', 
        `P95 response time (${Math.round(latestHealth.responseTime.p95)}ms) exceeds threshold (${thresholds.responseTime}ms)`,
        latestHealth.responseTime.p95, thresholds.responseTime
      );
    } else {
      this.resolveAlert('response_time');
    }

    // Error rate alerts
    if (latestHealth.errorRate > thresholds.errorRate) {
      this.createAlert('error_rate', 'critical',
        `Error rate (${(latestHealth.errorRate * 100).toFixed(2)}%) exceeds threshold (${(thresholds.errorRate * 100).toFixed(2)}%)`,
        latestHealth.errorRate, thresholds.errorRate
      );
    } else {
      this.resolveAlert('error_rate');
    }

    // Memory alerts
    if (latestHealth.memoryUsage > (thresholds.memoryUsage / 100) * 512) { // Assuming 512MB base
      this.createAlert('memory', 'medium',
        `Memory usage (${Math.round(latestHealth.memoryUsage)}MB) is high`,
        latestHealth.memoryUsage, thresholds.memoryUsage
      );
    } else {
      this.resolveAlert('memory');
    }

    // Throughput alerts (low throughput)
    const expectedThroughput = 10; // Expected baseline requests per second
    if (latestHealth.throughput < expectedThroughput * 0.5) {
      this.createAlert('throughput', 'medium',
        `Throughput (${latestHealth.throughput.toFixed(2)} req/sec) is below expected level`,
        latestHealth.throughput, expectedThroughput
      );
    } else {
      this.resolveAlert('throughput');
    }
  }

  /**
   * Create or update an alert
   */
  private createAlert(type: Alert['type'], severity: Alert['severity'], message: string, value: number, threshold: number): void {
    const alertId = type;
    const existingAlert = this.activeAlerts.get(alertId);

    if (existingAlert && !existingAlert.resolved) {
      // Update existing alert
      existingAlert.timestamp = Date.now();
      existingAlert.value = value;
      return;
    }

    const alert: Alert = {
      id: alertId,
      timestamp: Date.now(),
      severity,
      type,
      message,
      value,
      threshold,
      resolved: false
    };

    this.activeAlerts.set(alertId, alert);
    this.emit('alert', alert);

    console.log(`🚨 ${severity.toUpperCase()} ALERT: ${message}`);

    // Send to external alert systems if configured
    this.sendExternalAlert(alert);
  }

  /**
   * Resolve an alert
   */
  private resolveAlert(type: Alert['type']): void {
    const alert = this.activeAlerts.get(type);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      console.log(`✅ RESOLVED: ${alert.message}`);
      this.emit('alert-resolved', alert);
    }
  }

  /**
   * Send alert to external systems
   */
  private async sendExternalAlert(alert: Alert): Promise<void> {
    // Send to Slack webhook if configured
    if (PERFORMANCE_CONFIG.reporting.alertSlackWebhook) {
      try {
        await axios.post(PERFORMANCE_CONFIG.reporting.alertSlackWebhook, {
          text: `🚨 Performance Alert: ${alert.message}`,
          attachments: [{
            color: alert.severity === 'critical' ? 'danger' : 'warning',
            fields: [
              { title: 'Type', value: alert.type, short: true },
              { title: 'Severity', value: alert.severity, short: true },
              { title: 'Value', value: alert.value.toFixed(2), short: true },
              { title: 'Threshold', value: alert.threshold.toFixed(2), short: true }
            ],
            timestamp: Math.floor(alert.timestamp / 1000)
          }]
        });
      } catch (error) {
        console.warn('Failed to send Slack alert:', error);
      }
    }
  }

  /**
   * Setup event handlers for monitoring feedback
   */
  private setupEventHandlers(): void {
    this.on('metric', (metric: PerformanceMetric) => {
      // Could implement custom metric processing here
    });

    this.on('health-update', (health: SystemHealth) => {
      // Could implement custom health processing here
    });

    this.on('alert', (alert: Alert) => {
      // Could implement additional alert processing here
    });
  }

  /**
   * Start real-time display
   */
  private startRealTimeDisplay(): void {
    // Display updates every 10 seconds
    setInterval(() => {
      if (!this.isMonitoring) return;

      const latestHealth = this.healthHistory[this.healthHistory.length - 1];
      if (!latestHealth) return;

      console.clear();
      console.log('🔍 Real-time Performance Monitor');
      console.log('=================================');
      console.log(`Time: ${new Date().toLocaleTimeString()}`);
      console.log(`Target: ${this.baseUrl}\n`);

      console.log('📊 Current Metrics:');
      console.log(`• Response Time P95: ${Math.round(latestHealth.responseTime.p95)}ms`);
      console.log(`• Response Time P99: ${Math.round(latestHealth.responseTime.p99)}ms`);
      console.log(`• Throughput: ${latestHealth.throughput.toFixed(2)} req/sec`);
      console.log(`• Error Rate: ${(latestHealth.errorRate * 100).toFixed(2)}%`);
      console.log(`• Memory Usage: ${Math.round(latestHealth.memoryUsage)}MB`);
      console.log(`• CPU Usage: ${latestHealth.cpuUsage.toFixed(1)}%\n`);

      // Active alerts
      const activeAlerts = Array.from(this.activeAlerts.values()).filter(a => !a.resolved);
      if (activeAlerts.length > 0) {
        console.log('🚨 Active Alerts:');
        activeAlerts.forEach(alert => {
          console.log(`• [${alert.severity.toUpperCase()}] ${alert.message}`);
        });
      } else {
        console.log('✅ No active alerts');
      }

      console.log('\n📈 Trending (last 5 minutes):');
      const recentHealth = this.healthHistory.slice(-5);
      if (recentHealth.length > 1) {
        const trend = this.calculateTrend(recentHealth);
        console.log(`• Response Time: ${trend.responseTime > 0 ? '📈' : '📉'} ${Math.abs(trend.responseTime).toFixed(1)}%`);
        console.log(`• Throughput: ${trend.throughput > 0 ? '📈' : '📉'} ${Math.abs(trend.throughput).toFixed(1)}%`);
        console.log(`• Error Rate: ${trend.errorRate > 0 ? '📈' : '📉'} ${Math.abs(trend.errorRate).toFixed(2)}%`);
      }

      console.log('\nPress Ctrl+C to stop monitoring...\n');
    }, 10000);
  }

  /**
   * Calculate performance trends
   */
  private calculateTrend(healthData: SystemHealth[]): any {
    if (healthData.length < 2) return { responseTime: 0, throughput: 0, errorRate: 0 };

    const first = healthData[0];
    const last = healthData[healthData.length - 1];

    return {
      responseTime: ((last.responseTime.p95 - first.responseTime.p95) / first.responseTime.p95) * 100,
      throughput: ((last.throughput - first.throughput) / first.throughput) * 100,
      errorRate: ((last.errorRate - first.errorRate) / (first.errorRate || 0.01)) * 100
    };
  }

  /**
   * Generate monitoring report
   */
  private async generateMonitoringReport(): Promise<void> {
    const timestamp = new Date().toISOString();
    const duration = this.healthHistory.length > 0 
      ? this.healthHistory[this.healthHistory.length - 1].timestamp - this.healthHistory[0].timestamp
      : 0;

    const reportData = {
      timestamp,
      testType: 'performance-monitoring',
      environment: process.env.NODE_ENV || 'production',
      target: this.baseUrl,
      duration: Math.round(duration / 1000), // seconds
      totalMetrics: this.metrics.length,
      healthRecords: this.healthHistory.length,
      alerts: Array.from(this.activeAlerts.values()),
      summary: this.generateMonitoringSummary(),
      healthHistory: this.healthHistory,
      recommendations: this.generateMonitoringRecommendations()
    };

    // Save JSON report
    const jsonPath = path.join(this.outputDir, `monitoring-${timestamp.replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(reportData, null, 2));

    console.log(`📄 Monitoring report saved: ${jsonPath}`);
  }

  private generateMonitoringSummary() {
    if (this.healthHistory.length === 0) return {};

    const avgResponseTime = this.healthHistory.reduce((sum, h) => sum + h.responseTime.avg, 0) / this.healthHistory.length;
    const avgThroughput = this.healthHistory.reduce((sum, h) => sum + h.throughput, 0) / this.healthHistory.length;
    const avgErrorRate = this.healthHistory.reduce((sum, h) => sum + h.errorRate, 0) / this.healthHistory.length;
    const maxMemory = Math.max(...this.healthHistory.map(h => h.memoryUsage));

    const totalAlerts = Array.from(this.activeAlerts.values()).length;
    const unresolvedAlerts = Array.from(this.activeAlerts.values()).filter(a => !a.resolved).length;

    return {
      avgResponseTime: Math.round(avgResponseTime),
      avgThroughput: avgThroughput.toFixed(2),
      avgErrorRate: (avgErrorRate * 100).toFixed(2),
      maxMemoryUsage: Math.round(maxMemory),
      totalAlerts,
      unresolvedAlerts,
      uptime: this.healthHistory.length * (this.monitoringIntervalMs / 1000), // seconds
    };
  }

  private generateMonitoringRecommendations(): string[] {
    const recommendations = [];
    const summary = this.generateMonitoringSummary();

    if (summary.avgResponseTime > 500) {
      recommendations.push('Average response time is high - investigate slow endpoints');
    }

    if (parseFloat(summary.avgErrorRate) > 5) {
      recommendations.push('Error rate is elevated - check application logs');
    }

    if (summary.unresolvedAlerts > 0) {
      recommendations.push('Address unresolved alerts to improve system stability');
    }

    if (summary.avgThroughput < 5) {
      recommendations.push('Low throughput detected - consider performance optimization');
    }

    recommendations.push('Continue monitoring key metrics in production');
    recommendations.push('Set up automated alerting for critical thresholds');
    recommendations.push('Regularly review and update performance baselines');

    return recommendations;
  }

  /**
   * Calculate percentile from sorted array
   */
  private percentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    const index = Math.ceil(sortedArray.length * percentile) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }
}

// Main execution with graceful shutdown
async function main() {
  const monitor = new PerformanceMonitor();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, stopping monitoring...');
    await monitor.stopMonitoring();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, stopping monitoring...');
    await monitor.stopMonitoring();
    process.exit(0);
  });

  // Start monitoring
  await monitor.startMonitoring();

  // Keep process alive
  process.stdin.resume();
}

if (require.main === module) {
  main().catch(console.error);
}