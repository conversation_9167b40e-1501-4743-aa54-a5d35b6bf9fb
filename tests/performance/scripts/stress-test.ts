#!/usr/bin/env tsx

/**
 * Stress Testing Script
 * Progressively increases load to find breaking points and resource limits
 */

import autocannon from 'autocannon';
import * as fs from 'fs/promises';
import * as path from 'path';
import { performance } from 'perf_hooks';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

interface StressTestResult {
  phase: string;
  connections: number;
  duration: number;
  requests: number;
  bytes: number;
  throughput: {
    average: number;
    mean: number;
    stddev: number;
    min: number;
    max: number;
  };
  latency: {
    average: number;
    mean: number;
    stddev: number;
    min: number;
    max: number;
    p0_001: number;
    p0_01: number;
    p0_1: number;
    p1: number;
    p2_5: number;
    p10: number;
    p25: number;
    p50: number;
    p75: number;
    p90: number;
    p97_5: number;
    p99: number;
    p99_9: number;
    p99_99: number;
    p99_999: number;
  };
  errors: number;
  timeouts: number;
  mismatches: number;
}

interface StressTestPhase {
  name: string;
  connections: number;
  duration: number;
  expectedThroughput: number;
  maxLatency: number;
  maxErrorRate: number;
}

class StressTestRunner {
  private results: StressTestResult[] = [];
  private breakingPoint: StressTestPhase | null = null;
  
  constructor(
    private readonly baseUrl: string = PERFORMANCE_CONFIG.apiBase,
    private readonly outputDir: string = PERFORMANCE_CONFIG.reporting.outputDir
  ) {}

  /**
   * Run comprehensive stress test with multiple phases
   */
  async runStressTest(): Promise<void> {
    console.log('🔥 Starting Stress Testing');
    console.log('=========================');
    console.log(`Target: ${this.baseUrl}`);
    console.log(`Output: ${this.outputDir}\n`);

    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });

    // Define stress test phases - progressively increasing load
    const phases: StressTestPhase[] = [
      { name: 'Light Load', connections: 10, duration: 30, expectedThroughput: 50, maxLatency: 200, maxErrorRate: 0.01 },
      { name: 'Moderate Load', connections: 25, duration: 60, expectedThroughput: 100, maxLatency: 500, maxErrorRate: 0.02 },
      { name: 'Heavy Load', connections: 50, duration: 90, expectedThroughput: 200, maxLatency: 1000, maxErrorRate: 0.05 },
      { name: 'Intense Load', connections: 100, duration: 120, expectedThroughput: 300, maxLatency: 2000, maxErrorRate: 0.1 },
      { name: 'Extreme Load', connections: 250, duration: 180, expectedThroughput: 400, maxLatency: 5000, maxErrorRate: 0.15 },
      { name: 'Breaking Point', connections: 500, duration: 240, expectedThroughput: 500, maxLatency: 10000, maxErrorRate: 0.2 },
      { name: 'Beyond Breaking', connections: 1000, duration: 300, expectedThroughput: 600, maxLatency: 20000, maxErrorRate: 0.3 },
    ];

    for (let i = 0; i < phases.length; i++) {
      const phase = phases[i];
      console.log(`🚀 Running Phase ${i + 1}/${phases.length}: ${phase.name}`);
      console.log(`   Connections: ${phase.connections}`);
      console.log(`   Duration: ${phase.duration}s`);
      console.log(`   Expected Throughput: ${phase.expectedThroughput} req/sec`);
      
      try {
        const result = await this.runSingleStressPhase(phase);
        
        // Analyze results
        const analysis = this.analyzePhaseResult(result, phase);
        console.log(analysis.summary);
        
        // Check if we've hit the breaking point
        if (analysis.failed && !this.breakingPoint) {
          this.breakingPoint = phase;
          console.log(`💥 BREAKING POINT DETECTED at ${phase.name}!`);
          console.log(`   Connections: ${phase.connections}`);
          console.log(`   Errors: ${analysis.errorRate.toFixed(2)}%`);
          console.log(`   P99 Latency: ${Math.round(result.latency.p99)}ms`);
        }
        
        // Add cooldown between phases
        if (i < phases.length - 1) {
          console.log(`⏸️  Cooldown period (30s)...\n`);
          await this.sleep(30000);
        }
        
      } catch (error) {
        console.error(`❌ Phase ${phase.name} failed:`, error);
        break;
      }
    }
    
    // Generate comprehensive report
    await this.generateStressTestReport();
  }

  /**
   * Run a single stress test phase
   */
  private async runSingleStressPhase(phase: StressTestPhase): Promise<StressTestResult> {
    const startTime = performance.now();
    
    const result = await autocannon({
      url: `${this.baseUrl}/health`,
      connections: phase.connections,
      duration: phase.duration,
      pipelining: 1,
      workers: Math.min(phase.connections, 10),
      
      // Request configuration
      method: 'GET',
      headers: {
        'User-Agent': 'StressTester/1.0',
        'Accept': 'application/json'
      },
      
      // Timeouts and limits
      timeout: 30000,
      connectionTimeout: 5000,
      reconnectRate: 100,
      
      // Advanced options
      overallRate: phase.expectedThroughput * 2, // Allow bursts
      amount: undefined, // Run for duration, not request count
      
      // Bailout conditions
      bailout: phase.maxErrorRate * 100, // Bailout at max error rate percentage
      
      // Enable detailed tracking
      track: true,
      
      // Custom request setup for realistic load
      setupClient: (client) => {
        client.setHeaders({
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache'
        });
      },
    });
    
    const endTime = performance.now();
    
    // Transform autocannon result to our format
    const stressResult: StressTestResult = {
      phase: phase.name,
      connections: phase.connections,
      duration: Math.round((endTime - startTime) / 1000),
      requests: result.requests.total,
      bytes: result.bytes,
      throughput: {
        average: result.throughput.average,
        mean: result.throughput.mean,
        stddev: result.throughput.stddev,
        min: result.throughput.min,
        max: result.throughput.max,
      },
      latency: {
        average: result.latency.average,
        mean: result.latency.mean,
        stddev: result.latency.stddev,
        min: result.latency.min,
        max: result.latency.max,
        p0_001: result.latency.p0_001,
        p0_01: result.latency.p0_01,
        p0_1: result.latency.p0_1,
        p1: result.latency.p1,
        p2_5: result.latency.p2_5,
        p10: result.latency.p10,
        p25: result.latency.p25,
        p50: result.latency.p50,
        p75: result.latency.p75,
        p90: result.latency.p90,
        p97_5: result.latency.p97_5,
        p99: result.latency.p99,
        p99_9: result.latency.p99_9,
        p99_99: result.latency.p99_99,
        p99_999: result.latency.p99_999,
      },
      errors: result.errors,
      timeouts: result.timeouts,
      mismatches: result.mismatches,
    };
    
    this.results.push(stressResult);
    return stressResult;
  }

  /**
   * Analyze phase results against expectations
   */
  private analyzePhaseResult(result: StressTestResult, phase: StressTestPhase) {
    const errorRate = ((result.errors + result.timeouts) / result.requests) * 100;
    const throughputRatio = result.throughput.average / phase.expectedThroughput;
    const latencyExceeded = result.latency.p99 > phase.maxLatency;
    const errorRateExceeded = errorRate > (phase.maxErrorRate * 100);
    
    const failed = latencyExceeded || errorRateExceeded || throughputRatio < 0.5;
    
    const summary = `
   📊 Results:
   • Throughput: ${Math.round(result.throughput.average)} req/sec (${(throughputRatio * 100).toFixed(1)}% of target)
   • Latency P50: ${Math.round(result.latency.p50)}ms
   • Latency P99: ${Math.round(result.latency.p99)}ms ${latencyExceeded ? '❌ EXCEEDED' : '✅'}
   • Error Rate: ${errorRate.toFixed(2)}% ${errorRateExceeded ? '❌ EXCEEDED' : '✅'}
   • Errors: ${result.errors}, Timeouts: ${result.timeouts}
   • Status: ${failed ? '❌ FAILED' : '✅ PASSED'}
    `;
    
    return {
      failed,
      errorRate,
      throughputRatio,
      latencyExceeded,
      errorRateExceeded,
      summary
    };
  }

  /**
   * Generate comprehensive stress test report
   */
  private async generateStressTestReport(): Promise<void> {
    const timestamp = new Date().toISOString();
    const reportData = {
      timestamp,
      testType: 'stress-test',
      environment: process.env.NODE_ENV || 'test',
      target: this.baseUrl,
      breakingPoint: this.breakingPoint,
      phases: this.results,
      summary: this.generateSummary(),
      recommendations: this.generateRecommendations()
    };
    
    // Save JSON report
    const jsonPath = path.join(this.outputDir, `stress-test-${timestamp.replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(reportData, null, 2));
    
    // Generate HTML report
    await this.generateHTMLReport(reportData, jsonPath);
    
    // Display final summary
    console.log('\n🏁 Stress Test Complete');
    console.log('========================');
    console.log(`📄 Report: ${jsonPath}`);
    console.log(`🔍 Phases Completed: ${this.results.length}`);
    
    if (this.breakingPoint) {
      console.log(`💥 Breaking Point: ${this.breakingPoint.name} (${this.breakingPoint.connections} connections)`);
    } else {
      console.log('💪 No breaking point reached - system held under all tested loads');
    }
    
    console.log('\n📋 Performance Summary:');
    const summary = this.generateSummary();
    console.log(`• Peak Throughput: ${Math.round(summary.peakThroughput)} req/sec`);
    console.log(`• Peak Connections: ${summary.peakConnections}`);
    console.log(`• Best P99 Latency: ${Math.round(summary.bestP99Latency)}ms`);
    console.log(`• Worst P99 Latency: ${Math.round(summary.worstP99Latency)}ms`);
    console.log(`• Total Errors: ${summary.totalErrors}`);
    console.log(`• Total Requests: ${summary.totalRequests.toLocaleString()}`);
  }

  /**
   * Generate performance summary
   */
  private generateSummary() {
    return {
      peakThroughput: Math.max(...this.results.map(r => r.throughput.average)),
      peakConnections: Math.max(...this.results.map(r => r.connections)),
      bestP99Latency: Math.min(...this.results.map(r => r.latency.p99)),
      worstP99Latency: Math.max(...this.results.map(r => r.latency.p99)),
      totalErrors: this.results.reduce((sum, r) => sum + r.errors + r.timeouts, 0),
      totalRequests: this.results.reduce((sum, r) => sum + r.requests, 0),
      averageLatency: this.results.reduce((sum, r) => sum + r.latency.average, 0) / this.results.length
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations() {
    const recommendations = [];
    const summary = this.generateSummary();
    
    if (this.breakingPoint) {
      recommendations.push(`Consider scaling horizontally before reaching ${this.breakingPoint.connections} concurrent connections`);
      recommendations.push('Implement circuit breakers to handle overload gracefully');
      recommendations.push('Add rate limiting to prevent system overload');
    }
    
    if (summary.worstP99Latency > 2000) {
      recommendations.push('Optimize slow endpoints to improve P99 latency');
      recommendations.push('Consider implementing caching for frequently accessed data');
    }
    
    if (summary.totalErrors > 0) {
      recommendations.push('Investigate error patterns and implement better error handling');
      recommendations.push('Add health checks and auto-recovery mechanisms');
    }
    
    recommendations.push('Monitor key metrics in production using the established baselines');
    recommendations.push('Set up alerts for approaching breaking point thresholds');
    
    return recommendations;
  }

  /**
   * Generate HTML report
   */
  private async generateHTMLReport(reportData: any, jsonPath: string): Promise<void> {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stress Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2, h3 { color: #333; margin-top: 0; }
        .alert { padding: 15px; border-radius: 6px; margin: 20px 0; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .phase-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .phase-card { background: #f8f9fa; padding: 20px; border-radius: 6px; border-left: 4px solid #007bff; }
        .phase-card.failed { border-left-color: #dc3545; }
        .metric { display: flex; justify-content: space-between; margin: 8px 0; }
        .metric-label { font-weight: 500; }
        .metric-value { color: #666; }
        .chart { width: 100%; height: 400px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .breaking-point { background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔥 Stress Test Report</h1>
        <p><strong>Generated:</strong> ${new Date(reportData.timestamp).toLocaleString()}</p>
        <p><strong>Target:</strong> ${reportData.target}</p>
        <p><strong>Environment:</strong> ${reportData.environment}</p>
        
        ${reportData.breakingPoint ? `
        <div class="breaking-point">
            <h2>💥 Breaking Point Detected</h2>
            <p><strong>Phase:</strong> ${reportData.breakingPoint.name}</p>
            <p><strong>Connections:</strong> ${reportData.breakingPoint.connections}</p>
            <p><strong>Max Error Rate:</strong> ${(reportData.breakingPoint.maxErrorRate * 100).toFixed(1)}%</p>
        </div>
        ` : `
        <div class="alert alert-success">
            <h3>💪 No Breaking Point Reached</h3>
            <p>The system handled all stress test phases successfully!</p>
        </div>
        `}
        
        <h2>📊 Performance Summary</h2>
        <div class="phase-grid">
            <div class="phase-card">
                <h4>Peak Performance</h4>
                <div class="metric">
                    <span class="metric-label">Peak Throughput:</span>
                    <span class="metric-value">${Math.round(reportData.summary.peakThroughput)} req/sec</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Peak Connections:</span>
                    <span class="metric-value">${reportData.summary.peakConnections}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Best P99 Latency:</span>
                    <span class="metric-value">${Math.round(reportData.summary.bestP99Latency)}ms</span>
                </div>
            </div>
            <div class="phase-card">
                <h4>Total Statistics</h4>
                <div class="metric">
                    <span class="metric-label">Total Requests:</span>
                    <span class="metric-value">${reportData.summary.totalRequests.toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Errors:</span>
                    <span class="metric-value">${reportData.summary.totalErrors}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Worst P99 Latency:</span>
                    <span class="metric-value">${Math.round(reportData.summary.worstP99Latency)}ms</span>
                </div>
            </div>
        </div>
        
        <h2>📈 Performance Progression</h2>
        <canvas id="throughputChart" class="chart"></canvas>
        <canvas id="latencyChart" class="chart"></canvas>
        
        <h2>🔍 Phase Details</h2>
        <div class="phase-grid">
            ${reportData.phases.map((phase: any, index: number) => `
            <div class="phase-card ${phase.errors > 0 ? 'failed' : ''}">
                <h4>${phase.phase}</h4>
                <div class="metric">
                    <span class="metric-label">Connections:</span>
                    <span class="metric-value">${phase.connections}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Duration:</span>
                    <span class="metric-value">${phase.duration}s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Requests:</span>
                    <span class="metric-value">${phase.requests.toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Throughput:</span>
                    <span class="metric-value">${Math.round(phase.throughput.average)} req/sec</span>
                </div>
                <div class="metric">
                    <span class="metric-label">P50 Latency:</span>
                    <span class="metric-value">${Math.round(phase.latency.p50)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">P99 Latency:</span>
                    <span class="metric-value">${Math.round(phase.latency.p99)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Errors:</span>
                    <span class="metric-value">${phase.errors + phase.timeouts}</span>
                </div>
            </div>
            `).join('')}
        </div>
        
        <h2>💡 Recommendations</h2>
        <ul>
            ${reportData.recommendations.map((rec: string) => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
    
    <script>
        // Throughput Chart
        const throughputCtx = document.getElementById('throughputChart').getContext('2d');
        new Chart(throughputCtx, {
            type: 'line',
            data: {
                labels: ${JSON.stringify(reportData.phases.map((p: any) => p.phase))},
                datasets: [{
                    label: 'Throughput (req/sec)',
                    data: ${JSON.stringify(reportData.phases.map((p: any) => Math.round(p.throughput.average)))},
                    borderColor: 'rgba(0, 123, 255, 1)',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: 'Throughput Progression' }
                }
            }
        });
        
        // Latency Chart
        const latencyCtx = document.getElementById('latencyChart').getContext('2d');
        new Chart(latencyCtx, {
            type: 'line',
            data: {
                labels: ${JSON.stringify(reportData.phases.map((p: any) => p.phase))},
                datasets: [{
                    label: 'P50 Latency (ms)',
                    data: ${JSON.stringify(reportData.phases.map((p: any) => Math.round(p.latency.p50)))},
                    borderColor: 'rgba(40, 167, 69, 1)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                }, {
                    label: 'P99 Latency (ms)',
                    data: ${JSON.stringify(reportData.phases.map((p: any) => Math.round(p.latency.p99)))},
                    borderColor: 'rgba(220, 53, 69, 1)',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: 'Latency Progression' }
                }
            }
        });
    </script>
</body>
</html>
    `;
    
    const htmlPath = jsonPath.replace('.json', '.html');
    await fs.writeFile(htmlPath, htmlContent);
    console.log(`📊 HTML stress test report: ${htmlPath}`);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  const runner = new StressTestRunner();
  await runner.runStressTest();
}

if (require.main === module) {
  main().catch(console.error);
}