#!/usr/bin/env tsx

/**
 * Performance Testing Suite Setup Validation
 * Validates that all components are properly configured and working
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import axios from 'axios';
import { PERFORMANCE_CONFIG } from '../config/performance-config';

interface ValidationResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

class SetupValidator {
  private results: ValidationResult[] = [];

  async validateSetup(): Promise<void> {
    console.log('🔍 Validating Performance Testing Suite Setup');
    console.log('============================================\n');

    try {
      // Validate file structure
      await this.validateFileStructure();
      
      // Validate configuration
      await this.validateConfiguration();
      
      // Validate dependencies
      await this.validateDependencies();
      
      // Validate API connectivity
      await this.validateAPIConnectivity();
      
      // Generate validation report
      await this.generateValidationReport();
      
      // Display results
      this.displayResults();
      
    } catch (error) {
      console.error('❌ Setup validation failed:', error);
      process.exit(1);
    }
  }

  /**
   * Validate required file structure exists
   */
  private async validateFileStructure(): Promise<void> {
    console.log('📁 Validating file structure...');
    
    const requiredFiles = [
      'config/performance-config.ts',
      'metrics/baseline-collector.ts',
      'scenarios/artillery-advanced.yml',
      'scenarios/k6-load-test.js',
      'scripts/baseline-metrics.ts',
      'scripts/stress-test.ts',
      'scripts/chaos-test.ts',
      'scripts/performance-monitor.ts',
      'scripts/generate-report.ts',
      'scripts/advanced-processors.js',
      'data/test-users.csv',
      'data/test-services.csv',
      'data/test-workflows.csv',
      'jest.config.js',
      'jest.setup.js',
      'api-performance.test.ts',
      'workflow-performance.test.ts',
      'README.md'
    ];
    
    const requiredDirectories = [
      'config',
      'metrics',
      'scenarios',
      'scripts',
      'data',
      'reports',
      'dashboards'
    ];

    // Check directories
    for (const dir of requiredDirectories) {
      const dirPath = path.join(__dirname, '..', dir);
      try {
        await fs.access(dirPath);
        this.addResult('file-structure', 'pass', `Directory exists: ${dir}`);
      } catch (error) {
        this.addResult('file-structure', 'fail', `Missing directory: ${dir}`);
      }
    }

    // Check files
    for (const file of requiredFiles) {
      const filePath = path.join(__dirname, '..', file);
      try {
        await fs.access(filePath);
        this.addResult('file-structure', 'pass', `File exists: ${file}`);
      } catch (error) {
        this.addResult('file-structure', 'fail', `Missing file: ${file}`);
      }
    }

    console.log('✅ File structure validation complete\n');
  }

  /**
   * Validate configuration settings
   */
  private async validateConfiguration(): Promise<void> {
    console.log('⚙️ Validating configuration...');

    // Check performance config
    if (PERFORMANCE_CONFIG.apiBase) {
      this.addResult('configuration', 'pass', `API base URL configured: ${PERFORMANCE_CONFIG.apiBase}`);
    } else {
      this.addResult('configuration', 'fail', 'API base URL not configured');
    }

    // Check thresholds
    if (PERFORMANCE_CONFIG.defaultThresholds) {
      const thresholds = PERFORMANCE_CONFIG.defaultThresholds;
      if (thresholds.p95 && thresholds.p95 > 0) {
        this.addResult('configuration', 'pass', `P95 threshold: ${thresholds.p95}ms`);
      } else {
        this.addResult('configuration', 'warning', 'P95 threshold not properly configured');
      }

      if (thresholds.maxErrorRate && thresholds.maxErrorRate < 1) {
        this.addResult('configuration', 'pass', `Max error rate: ${(thresholds.maxErrorRate * 100).toFixed(2)}%`);
      } else {
        this.addResult('configuration', 'warning', 'Max error rate not properly configured');
      }
    }

    // Check endpoint budgets
    if (PERFORMANCE_CONFIG.endpointBudgets && Object.keys(PERFORMANCE_CONFIG.endpointBudgets).length > 0) {
      this.addResult('configuration', 'pass', `Endpoint budgets configured: ${Object.keys(PERFORMANCE_CONFIG.endpointBudgets).length} endpoints`);
    } else {
      this.addResult('configuration', 'warning', 'No endpoint budgets configured');
    }

    console.log('✅ Configuration validation complete\n');
  }

  /**
   * Validate required dependencies
   */
  private async validateDependencies(): Promise<void> {
    console.log('📦 Validating dependencies...');

    const requiredDeps = [
      'axios',
      'autocannon',
      'prom-client',
      'v8-profiler-next',
      'memwatch-next'
    ];

    // Check package.json
    try {
      const backendPackageJson = JSON.parse(await fs.readFile(
        path.join(__dirname, '../../../backend/package.json'), 
        'utf-8'
      ));
      
      const allDeps = {
        ...backendPackageJson.dependencies || {},
        ...backendPackageJson.devDependencies || {}
      };

      for (const dep of requiredDeps) {
        if (allDeps[dep]) {
          this.addResult('dependencies', 'pass', `Dependency installed: ${dep}@${allDeps[dep]}`);
        } else {
          this.addResult('dependencies', 'warning', `Optional dependency missing: ${dep}`);
        }
      }

    } catch (error) {
      this.addResult('dependencies', 'fail', 'Could not read backend package.json');
    }

    // Check root package.json for testing tools
    try {
      const rootPackageJson = JSON.parse(await fs.readFile(
        path.join(__dirname, '../../../package.json'), 
        'utf-8'
      ));
      
      const allDeps = {
        ...rootPackageJson.dependencies || {},
        ...rootPackageJson.devDependencies || {}
      };

      const testingTools = ['artillery', 'playwright', 'autocannon'];
      for (const tool of testingTools) {
        if (allDeps[tool]) {
          this.addResult('dependencies', 'pass', `Testing tool available: ${tool}@${allDeps[tool]}`);
        } else {
          this.addResult('dependencies', 'warning', `Testing tool not installed: ${tool}`);
        }
      }

    } catch (error) {
      this.addResult('dependencies', 'fail', 'Could not read root package.json');
    }

    console.log('✅ Dependencies validation complete\n');
  }

  /**
   * Validate API connectivity
   */
  private async validateAPIConnectivity(): Promise<void> {
    console.log('🌐 Validating API connectivity...');

    try {
      const response = await axios.get(`${PERFORMANCE_CONFIG.apiBase}/health`, {
        timeout: 5000,
        validateStatus: () => true
      });

      if (response.status === 200) {
        this.addResult('api-connectivity', 'pass', 'API health check successful');
        
        // Test response structure
        if (response.data && typeof response.data === 'object') {
          this.addResult('api-connectivity', 'pass', 'API response structure valid');
        } else {
          this.addResult('api-connectivity', 'warning', 'API response structure unexpected');
        }

      } else {
        this.addResult('api-connectivity', 'warning', `API returned status: ${response.status}`);
      }

    } catch (error: any) {
      if (error.code === 'ECONNREFUSED') {
        this.addResult('api-connectivity', 'warning', 'API server not running (start with npm run dev:backend)');
      } else {
        this.addResult('api-connectivity', 'fail', `API connectivity error: ${error.message}`);
      }
    }

    // Test additional endpoints if API is available
    const hasPassingHealthCheck = this.results.some(r => 
      r.component === 'api-connectivity' && r.status === 'pass' && r.message.includes('health check')
    );

    if (hasPassingHealthCheck) {
      const testEndpoints = [
        '/api/v1/',
        '/api/v1/auth/login'
      ];

      for (const endpoint of testEndpoints) {
        try {
          const response = await axios.get(`${PERFORMANCE_CONFIG.apiBase}${endpoint}`, {
            timeout: 5000,
            validateStatus: () => true
          });

          if (response.status >= 200 && response.status < 500) {
            this.addResult('api-connectivity', 'pass', `Endpoint accessible: ${endpoint} (${response.status})`);
          } else {
            this.addResult('api-connectivity', 'warning', `Endpoint returned: ${endpoint} (${response.status})`);
          }

        } catch (error) {
          this.addResult('api-connectivity', 'warning', `Endpoint test failed: ${endpoint}`);
        }
      }
    }

    console.log('✅ API connectivity validation complete\n');
  }

  /**
   * Generate validation report
   */
  private async generateValidationReport(): Promise<void> {
    const timestamp = new Date().toISOString();
    const report = {
      timestamp,
      validation_summary: this.getValidationSummary(),
      results: this.results,
      recommendations: this.generateRecommendations()
    };

    const reportDir = path.join(__dirname, '../reports');
    await fs.mkdir(reportDir, { recursive: true });

    const reportPath = path.join(reportDir, `setup-validation-${timestamp.replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`📄 Validation report saved: ${reportPath}`);
  }

  /**
   * Display validation results
   */
  private displayResults(): void {
    console.log('📊 Validation Results Summary');
    console.log('============================\n');

    const summary = this.getValidationSummary();
    
    console.log(`✅ Passed: ${summary.passed}`);
    console.log(`⚠️  Warnings: ${summary.warnings}`);
    console.log(`❌ Failed: ${summary.failed}`);
    console.log(`📊 Total: ${summary.total}\n`);

    // Group by component
    const byComponent = new Map<string, ValidationResult[]>();
    this.results.forEach(result => {
      if (!byComponent.has(result.component)) {
        byComponent.set(result.component, []);
      }
      byComponent.get(result.component)!.push(result);
    });

    // Display component-wise results
    for (const [component, componentResults] of byComponent) {
      console.log(`\n🔍 ${component.toUpperCase()}:`);
      
      componentResults.forEach(result => {
        const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        console.log(`   ${icon} ${result.message}`);
        if (result.details) {
          console.log(`      ${result.details}`);
        }
      });
    }

    // Display recommendations
    const recommendations = this.generateRecommendations();
    if (recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    // Final status
    console.log('\n🏁 Setup Validation Complete');
    
    if (summary.failed > 0) {
      console.log('❌ Setup has critical issues that must be resolved');
      process.exit(1);
    } else if (summary.warnings > 0) {
      console.log('⚠️  Setup is functional but has warnings to address');
      process.exit(0);
    } else {
      console.log('✅ Setup is fully validated and ready for performance testing!');
      process.exit(0);
    }
  }

  /**
   * Add validation result
   */
  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string, details?: string): void {
    this.results.push({ component, status, message, details });
  }

  /**
   * Get validation summary
   */
  private getValidationSummary() {
    const passed = this.results.filter(r => r.status === 'pass').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    
    return {
      passed,
      warnings,
      failed,
      total: this.results.length
    };
  }

  /**
   * Generate recommendations based on validation results
   */
  private generateRecommendations(): string[] {
    const recommendations = [];
    const failures = this.results.filter(r => r.status === 'fail');
    const warnings = this.results.filter(r => r.status === 'warning');

    // Critical issues
    if (failures.length > 0) {
      recommendations.push('Resolve all failed validations before running performance tests');
    }

    // API connectivity
    const apiIssues = this.results.filter(r => 
      r.component === 'api-connectivity' && r.status !== 'pass'
    );
    if (apiIssues.length > 0) {
      recommendations.push('Start the backend server with: npm run dev:backend');
      recommendations.push('Ensure database is properly setup with: npm run db:migrate && npm run db:seed');
    }

    // Dependencies
    const depIssues = this.results.filter(r => 
      r.component === 'dependencies' && r.status !== 'pass'
    );
    if (depIssues.length > 0) {
      recommendations.push('Install missing dependencies with: npm install');
    }

    // Configuration
    const configIssues = this.results.filter(r => 
      r.component === 'configuration' && r.status !== 'pass'
    );
    if (configIssues.length > 0) {
      recommendations.push('Review and update performance-config.ts settings');
    }

    // General recommendations
    if (warnings.length > 0) {
      recommendations.push('Address warnings to ensure optimal performance testing');
    }

    recommendations.push('Run initial baseline test: npm run performance:baseline');
    recommendations.push('Enable CI/CD integration by merging the GitHub Actions workflow');
    recommendations.push('Set up monitoring dashboard: npm run performance:monitor');

    return recommendations;
  }
}

// Main execution
async function main() {
  const validator = new SetupValidator();
  await validator.validateSetup();
}

if (require.main === module) {
  main().catch(console.error);
}