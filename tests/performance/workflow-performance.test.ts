/**
 * Workflow Performance Tests
 * Performance testing for workflow execution and orchestration
 */

import axios from 'axios';
import { PERFORMANCE_CONFIG } from './config/performance-config';

const API_BASE = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'WorkflowTest123!'
};

describe('Workflow Performance Tests', () => {
  let authToken: string;
  let testWorkflowId: string;
  let measurements: any[] = [];

  beforeAll(async () => {
    // Setup authentication
    try {
      const loginResponse = await axios.post(`${API_BASE}/api/v1/auth/login`, TEST_USER, {
        timeout: 10000,
        validateStatus: () => true
      });
      
      if (loginResponse.status === 200 && loginResponse.data.token) {
        authToken = loginResponse.data.token;
        console.log('✅ Authentication successful for workflow tests');
      } else {
        console.log('⚠️ Could not authenticate - some tests may be skipped');
      }
    } catch (error) {
      console.warn('Could not setup auth for workflow performance tests');
    }

    // Try to get or create a test workflow
    if (authToken) {
      try {
        const workflowsResponse = await axios.get(`${API_BASE}/api/v1/workflows`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          timeout: 10000,
          validateStatus: () => true
        });

        if (workflowsResponse.status === 200 && workflowsResponse.data.workflows?.length > 0) {
          testWorkflowId = workflowsResponse.data.workflows[0].id;
          console.log(`✅ Using test workflow: ${testWorkflowId}`);
        }
      } catch (error) {
        console.warn('Could not fetch test workflows');
      }
    }
  });

  afterAll(async () => {
    if (measurements.length > 0) {
      const summary = global.PerfUtils.generateSummary(measurements);
      console.log('\n📊 Workflow Performance Summary:');
      console.log(`Total Workflow Operations: ${summary.total_requests}`);
      console.log(`Success Rate: ${((summary.successful_requests / summary.total_requests) * 100).toFixed(2)}%`);
      console.log(`Avg Execution Time: ${summary.avg_response_time.toFixed(2)}ms`);
      console.log(`P95 Execution Time: ${summary.p95.toFixed(2)}ms`);
      console.log(`P99 Execution Time: ${summary.p99.toFixed(2)}ms\n`);
    }
  });

  describe('Workflow Listing Performance', () => {
    it('should list workflows efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping workflow list test - no auth token');
        return;
      }

      const iterations = 5;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const measurement = await global.PerfUtils.measureAsync(async () => {
          return await axios.get(`${API_BASE}/api/v1/workflows`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
            timeout: 10000,
            validateStatus: () => true
          });
        }, `Workflow list ${i + 1}`);

        responseTimes.push(measurement.duration);
        measurements.push({
          endpoint: '/api/v1/workflows',
          duration: measurement.duration,
          error: measurement.result.status !== 200,
          operation: 'list'
        });

        if (measurement.result.status === 200) {
          expect(measurement.result.data).toHaveProperty('workflows');
          expect(Array.isArray(measurement.result.data.workflows)).toBe(true);
        }

        expect(measurement.duration).toBeWithinResponseTime(1000); // 1 second max
      }

      const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);
      expect(percentiles.p95).toBeLessThan(500); // P95 under 500ms

      console.log(`✅ Workflow listing P95: ${percentiles.p95.toFixed(2)}ms`);
    });

    it('should handle paginated workflow listing efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping paginated workflow test - no auth token');
        return;
      }

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.get(`${API_BASE}/api/v1/workflows`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          params: { page: 1, limit: 10 },
          timeout: 10000,
          validateStatus: () => true
        });
      }, 'Paginated workflow listing');

      measurements.push({
        endpoint: '/api/v1/workflows',
        duration: measurement.duration,
        error: measurement.result.status !== 200,
        operation: 'paginated_list'
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(800); // 800ms max for paginated
        console.log(`✅ Paginated workflow listing: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Paginated workflow listing returned ${measurement.result.status}`);
      }
    });
  });

  describe('Workflow Execution Performance', () => {
    it('should execute simple workflow within performance budget', async () => {
      if (!authToken || !testWorkflowId) {
        console.log('⏭️ Skipping workflow execution test - no auth or workflow');
        return;
      }

      const workflowPayload = {
        input: {
          data: 'Performance test workflow execution data',
          parameters: {
            timeout: 60000,
            priority: 'normal',
            async: false
          }
        },
        context: {
          testId: `perf-test-${Date.now()}`,
          source: 'performance-test'
        }
      };

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.post(`${API_BASE}/api/v1/workflows/${testWorkflowId}/execute`, workflowPayload, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          timeout: 120000, // 2 minutes for workflow execution
          validateStatus: () => true
        });
      }, 'Simple workflow execution');

      measurements.push({
        endpoint: `/api/v1/workflows/${testWorkflowId}/execute`,
        duration: measurement.duration,
        error: measurement.result.status !== 200,
        operation: 'execute_simple'
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(PERFORMANCE_CONFIG.endpointBudgets['/api/v1/workflows/execute']?.p95 || 3000);
        expect(measurement.result.data).toHaveProperty('executionId');
        expect(measurement.result.data).toHaveProperty('status');
        
        console.log(`✅ Simple workflow execution: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Simple workflow execution returned ${measurement.result.status}, duration: ${measurement.duration.toFixed(2)}ms`);
      }
    });

    it('should handle complex workflow execution efficiently', async () => {
      if (!authToken || !testWorkflowId) {
        console.log('⏭️ Skipping complex workflow test - no auth or workflow');
        return;
      }

      const complexPayload = {
        input: {
          data: {
            documents: ['doc1.pdf', 'doc2.docx', 'doc3.txt'],
            processing_options: {
              extract_text: true,
              analyze_sentiment: true,
              generate_summary: true,
              detect_language: true
            },
            output_format: 'json',
            quality_threshold: 0.8
          },
          parameters: {
            parallel_processing: true,
            timeout: 180000, // 3 minutes
            priority: 'high',
            retry_count: 2
          }
        },
        context: {
          testId: `complex-perf-test-${Date.now()}`,
          source: 'performance-test',
          metadata: {
            test_type: 'complex_workflow',
            expected_duration_ms: 10000
          }
        }
      };

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.post(`${API_BASE}/api/v1/workflows/${testWorkflowId}/execute`, complexPayload, {
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 300000, // 5 minutes for complex workflow
          validateStatus: () => true
        });
      }, 'Complex workflow execution');

      measurements.push({
        endpoint: `/api/v1/workflows/${testWorkflowId}/execute`,
        duration: measurement.duration,
        error: measurement.result.status !== 200,
        operation: 'execute_complex'
      });

      if (measurement.result.status === 200) {
        // Complex workflows can take longer
        expect(measurement.duration).toBeWithinResponseTime(15000); // 15 seconds max
        expect(measurement.result.data).toHaveProperty('executionId');
        
        console.log(`✅ Complex workflow execution: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Complex workflow execution returned ${measurement.result.status}, duration: ${measurement.duration.toFixed(2)}ms`);
      }
    });

    it('should handle multiple concurrent workflow executions', async () => {
      if (!authToken || !testWorkflowId) {
        console.log('⏭️ Skipping concurrent workflow test - no auth or workflow');
        return;
      }

      const concurrentExecutions = 3; // Conservative number for workflow concurrency
      const promises = [];

      console.log(`🔄 Testing ${concurrentExecutions} concurrent workflow executions...`);

      for (let i = 0; i < concurrentExecutions; i++) {
        const payload = {
          input: {
            data: `Concurrent workflow test ${i + 1}`,
            parameters: {
              timeout: 60000,
              priority: 'normal'
            }
          },
          context: {
            testId: `concurrent-${i + 1}-${Date.now()}`,
            source: 'concurrent-performance-test'
          }
        };

        promises.push(
          global.PerfUtils.measureAsync(async () => {
            return await axios.post(`${API_BASE}/api/v1/workflows/${testWorkflowId}/execute`, payload, {
              headers: { 'Authorization': `Bearer ${authToken}` },
              timeout: 120000,
              validateStatus: () => true
            });
          }, `Concurrent workflow ${i + 1}`)
        );
      }

      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').map(r => r.value);
      const failed = results.filter(r => r.status === 'rejected');

      successful.forEach((result, index) => {
        measurements.push({
          endpoint: `/api/v1/workflows/${testWorkflowId}/execute`,
          duration: result.duration,
          error: result.result.status !== 200,
          operation: `concurrent_execute_${index + 1}`
        });
      });

      const successRate = successful.length / concurrentExecutions;
      
      if (successful.length > 0) {
        const responseTimes = successful.map(r => r.duration);
        const percentiles = global.PerfUtils.calculatePercentiles(responseTimes);

        expect(successRate).toBeGreaterThan(0.8); // 80% success rate minimum for concurrent workflows
        expect(percentiles.p95).toBeLessThan(10000); // P95 under 10 seconds for concurrent execution

        console.log(`✅ Concurrent workflows: ${successful.length}/${concurrentExecutions} successful, P95: ${percentiles.p95.toFixed(2)}ms`);
      }

      if (failed.length > 0) {
        console.log(`⚠️ ${failed.length} workflow executions failed during concurrent test`);
      }
    });
  });

  describe('Workflow Status and Monitoring', () => {
    it('should retrieve workflow execution status efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping workflow status test - no auth token');
        return;
      }

      // First, try to get any existing execution or create one
      let executionId = null;

      if (testWorkflowId) {
        try {
          const executeResponse = await axios.post(`${API_BASE}/api/v1/workflows/${testWorkflowId}/execute`, {
            input: { data: 'Status test workflow' },
            context: { testId: `status-test-${Date.now()}` }
          }, {
            headers: { 'Authorization': `Bearer ${authToken}` },
            timeout: 30000,
            validateStatus: () => true
          });

          if (executeResponse.status === 200 && executeResponse.data.executionId) {
            executionId = executeResponse.data.executionId;
          }
        } catch (error) {
          // Ignore execution failure, focus on status check performance
        }
      }

      if (executionId) {
        const measurement = await global.PerfUtils.measureAsync(async () => {
          return await axios.get(`${API_BASE}/api/v1/workflows/executions/${executionId}`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
            timeout: 10000,
            validateStatus: () => true
          });
        }, 'Workflow status check');

        measurements.push({
          endpoint: '/api/v1/workflows/executions/:id',
          duration: measurement.duration,
          error: measurement.result.status !== 200,
          operation: 'status_check'
        });

        if (measurement.result.status === 200) {
          expect(measurement.duration).toBeWithinResponseTime(500); // Status checks should be fast
          expect(measurement.result.data).toHaveProperty('status');
          
          console.log(`✅ Workflow status check: ${measurement.duration.toFixed(2)}ms`);
        } else {
          console.log(`⚠️ Workflow status check returned ${measurement.result.status}`);
        }
      } else {
        console.log('⏭️ Skipping status check - no execution ID available');
      }
    });

    it('should list workflow executions with pagination efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping workflow executions list test - no auth token');
        return;
      }

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.get(`${API_BASE}/api/v1/workflows/executions`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          params: { page: 1, limit: 10, status: 'all' },
          timeout: 15000,
          validateStatus: () => true
        });
      }, 'Workflow executions list');

      measurements.push({
        endpoint: '/api/v1/workflows/executions',
        duration: measurement.duration,
        error: measurement.result.status !== 200,
        operation: 'list_executions'
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(1000); // 1 second max for listing
        expect(measurement.result.data).toHaveProperty('executions');
        
        console.log(`✅ Workflow executions list: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Workflow executions list returned ${measurement.result.status}`);
      }
    });
  });

  describe('Workflow Template Performance', () => {
    it('should retrieve workflow templates efficiently', async () => {
      if (!authToken) {
        console.log('⏭️ Skipping workflow templates test - no auth token');
        return;
      }

      const measurement = await global.PerfUtils.measureAsync(async () => {
        return await axios.get(`${API_BASE}/api/v1/workflows/templates`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          timeout: 10000,
          validateStatus: () => true
        });
      }, 'Workflow templates');

      measurements.push({
        endpoint: '/api/v1/workflows/templates',
        duration: measurement.duration,
        error: measurement.result.status !== 200,
        operation: 'list_templates'
      });

      if (measurement.result.status === 200) {
        expect(measurement.duration).toBeWithinResponseTime(800); // Templates should load quickly
        
        console.log(`✅ Workflow templates: ${measurement.duration.toFixed(2)}ms`);
      } else {
        console.log(`⚠️ Workflow templates returned ${measurement.result.status}`);
      }
    });
  });

  describe('Workflow Performance Under Load', () => {
    it('should maintain workflow performance under sustained load', async () => {
      if (!authToken || !testWorkflowId) {
        console.log('⏭️ Skipping sustained workflow load test - no auth or workflow');
        return;
      }

      const testDuration = 30000; // 30 seconds
      const requestInterval = 2000; // 2 seconds between requests
      const startTime = Date.now();
      const loadTestResults: number[] = [];
      let requestCount = 0;

      console.log(`⏳ Running sustained workflow load test for ${testDuration / 1000} seconds...`);

      while (Date.now() - startTime < testDuration) {
        try {
          requestCount++;
          const measurement = await global.PerfUtils.measureAsync(async () => {
            return await axios.post(`${API_BASE}/api/v1/workflows/${testWorkflowId}/execute`, {
              input: {
                data: `Sustained load test ${requestCount}`,
                parameters: { timeout: 30000, priority: 'low' }
              },
              context: {
                testId: `load-${requestCount}-${Date.now()}`,
                source: 'sustained-load-test'
              }
            }, {
              headers: { 'Authorization': `Bearer ${authToken}` },
              timeout: 45000,
              validateStatus: () => true
            });
          }, `Load test request ${requestCount}`);

          loadTestResults.push(measurement.duration);
          measurements.push({
            endpoint: `/api/v1/workflows/${testWorkflowId}/execute`,
            duration: measurement.duration,
            error: measurement.result.status !== 200,
            operation: `sustained_load_${requestCount}`
          });

          // Wait before next request
          await new Promise(resolve => setTimeout(resolve, requestInterval));

        } catch (error) {
          console.warn(`Load test request ${requestCount} failed:`, error.message);
        }
      }

      if (loadTestResults.length > 0) {
        const percentiles = global.PerfUtils.calculatePercentiles(loadTestResults);
        const avgResponseTime = loadTestResults.reduce((sum, rt) => sum + rt, 0) / loadTestResults.length;

        expect(percentiles.p95).toBeLessThan(8000); // P95 under 8 seconds for sustained load
        expect(avgResponseTime).toBeLessThan(5000); // Average under 5 seconds

        console.log(`✅ Sustained load test: ${requestCount} requests, avg: ${avgResponseTime.toFixed(2)}ms, P95: ${percentiles.p95.toFixed(2)}ms`);
      } else {
        console.log('⚠️ No successful requests during sustained load test');
      }
    });
  });
});